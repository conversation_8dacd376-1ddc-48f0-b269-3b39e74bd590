# دليل ميزة سندات الأصناف - نظام إدارة مبيعات الألماس

## نظرة عامة

تم إضافة ميزة جديدة شاملة لإدارة سندات الأصناف في نظام إدارة مبيعات الألماس. هذه الميزة تتيح للمستخدمين إدارة صرف واستلام الأصناف من وإلى العملاء والموردين مع تتبع دقيق لحركة المخزون.

## الميزات الجديدة

### 1. سندات الأصناف
- **سندات صرف الأصناف**: لصرف الأصناف للعملاء أو الموردين
- **سندات استلام الأصناف**: لاستلام الأصناف من العملاء أو الموردين
- **تتبع تفصيلي**: كل سند يحتوي على تفاصيل الأصناف (النوع، الكمية، السعر)
- **ربط بالعملاء والموردين**: كل سند مرتبط بعميل أو مورد محدد

### 2. إدارة المخزون
- **تتبع حركة المخزون**: تسجيل تلقائي لجميع حركات الدخول والخروج
- **حساب الكميات المتاحة**: حساب دقيق للكميات المتاحة لكل صنف
- **التحقق من الكميات**: منع صرف كميات أكبر من المتاح
- **ملخص المخزون**: عرض شامل لحالة المخزون

### 3. التقارير والاستعلامات
- **تقارير سندات الأصناف**: تقارير مفصلة لجميع سندات الأصناف
- **تقارير المخزون**: ملخص شامل لحالة المخزون
- **فلترة متقدمة**: إمكانية فلترة التقارير حسب التاريخ والنوع والعميل/المورد
- **تصدير Excel**: تصدير التقارير إلى ملفات Excel

## الجداول الجديدة في قاعدة البيانات

### 1. جدول `receipt_items`
يحتوي على تفاصيل الأصناف في كل سند:
- `id`: المعرف الفريد
- `receipt_id`: معرف السند المرتبط
- `diamond_type`: نوع الألماس/الصنف
- `category_id`: معرف الفئة
- `unit_id`: معرف الوحدة
- `quantity`: الكمية
- `price_per_unit_usd`: سعر الوحدة بالدولار
- `total_value_usd`: إجمالي القيمة بالدولار
- `notes`: ملاحظات

### 2. جدول `inventory_movements`
يتتبع جميع حركات المخزون:
- `id`: المعرف الفريد
- `diamond_type`: نوع الألماس/الصنف
- `movement_type`: نوع الحركة (in/out)
- `quantity`: الكمية
- `movement_date`: تاريخ الحركة
- `reference_type`: نوع المرجع (receipt, sale, purchase)
- `reference_id`: معرف المرجع
- `customer_id`: معرف العميل (إن وجد)
- `supplier_id`: معرف المورد (إن وجد)

### 3. تحديثات على الجداول الموجودة
- إضافة عمود `notes` إلى جدول `receipts`
- إضافة أعمدة `category_id` و `unit_id` إلى جدول `opening_balances`

## كيفية الاستخدام

### 1. إنشاء سند صرف أصناف
1. انتقل إلى شاشة "إدارة السندات"
2. اختر تبويب "سند صرف أصناف"
3. حدد نوع السند (صرف للعميل أو المورد)
4. اختر العميل أو المورد
5. أضف الأصناف المطلوبة مع الكميات
6. احفظ السند

### 2. عرض تقارير سندات الأصناف
1. انتقل إلى شاشة "التقارير"
2. اختر تبويب "تقارير سندات الأصناف"
3. حدد معايير الفلترة (التاريخ، النوع، العميل/المورد)
4. انقر على "إنشاء التقرير"
5. يمكن تصدير التقرير إلى Excel

### 3. عرض ملخص المخزون
1. في شاشة السندات، انقر على "عرض المخزون"
2. أو في شاشة التقارير، انقر على "ملخص المخزون"
3. سيظهر ملخص شامل لجميع الأصناف مع الكميات المتاحة

## الملفات المضافة والمحدثة

### الملفات الجديدة
- `inventory_utils.py`: دوال مساعدة لإدارة المخزون
- `update_database_for_items.py`: ملف تحديث قاعدة البيانات
- `test_item_vouchers.py`: ملف اختبار الميزة الجديدة

### الملفات المحدثة
- `database.py`: إضافة النماذج الجديدة
- `vouchers_screen.py`: إضافة واجهة سندات الأصناف
- `reports_screen.py`: إضافة تقارير سندات الأصناف

## خصائص الأمان والتحقق

### 1. التحقق من الكميات
- يتم التحقق من توفر الكمية قبل السماح بالصرف
- عرض رسائل تحذيرية عند محاولة صرف كميات غير متاحة
- حساب دقيق للكميات المتاحة بناءً على الرصيد الافتتاحي والحركات

### 2. تتبع المراجع
- كل حركة مخزون مرتبطة بمرجع محدد (سند، مبيعات، مشتريات)
- إمكانية تتبع مصدر كل حركة
- حذف تلقائي لحركات المخزون عند حذف السند المرتبط

### 3. سجلات التدقيق
- تسجيل جميع العمليات في ملفات السجل
- تتبع المستخدم الذي قام بكل عملية
- تسجيل تواريخ ووقت جميع العمليات

## نتائج الاختبار

تم اختبار جميع الميزات الجديدة بنجاح:
- ✅ اختبار بنية قاعدة البيانات: نجح
- ✅ اختبار حسابات المخزون: نجح  
- ✅ اختبار إنشاء سند أصناف: نجح
- ✅ اختبار استعلامات السندات: نجح
- ✅ اختبار المخزون بعد الحركات: نجح

**معدل النجاح: 100%**

## التحسينات المستقبلية المقترحة

1. **إضافة باركود**: دعم قراءة الباركود للأصناف
2. **تنبيهات المخزون**: تنبيهات تلقائية عند انخفاض الكميات
3. **تقارير متقدمة**: تقارير تحليلية أكثر تفصيلاً
4. **دعم الصور**: إضافة صور للأصناف
5. **تكامل مع الموازين**: ربط مع موازين إلكترونية

## الدعم والصيانة

للحصول على الدعم أو الإبلاغ عن مشاكل:
1. راجع ملف `error_log.txt` للأخطاء
2. تأكد من تحديث قاعدة البيانات باستخدام `update_database_for_items.py`
3. شغل `test_item_vouchers.py` للتأكد من سلامة النظام

---

**تاريخ الإنشاء**: 2025-07-08  
**الإصدار**: 1.50  
**المطور**: Augment Agent
