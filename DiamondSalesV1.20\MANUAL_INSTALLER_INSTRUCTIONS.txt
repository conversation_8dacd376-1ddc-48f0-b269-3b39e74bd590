# تعليمات إنشاء المثبت يدويًا

نظرًا لوجود مشكلة في تنفيذ الأوامر تلقائيًا، يمكنك اتباع الخطوات التالية لإنشاء المثبت يدويًا:

## الخطوة 1: إنشاء الملفات التنفيذية

1. افتح موجه الأوامر (Command Prompt) بصلاحيات المسؤول
2. انتقل إلى مجلد المشروع:
   ```
   cd c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20
   ```
3. قم بتنفيذ الأمر التالي لإنشاء الملفات التنفيذية:
   ```
   .venv\Scripts\pyinstaller.exe diamond_sales.spec
   ```

## الخطوة 2: نسخ الملفات إلى مجلد المثبت

1. تأكد من وجود مجلد `installer_package`، وإذا لم يكن موجودًا قم بإنشائه:
   ```
   mkdir installer_package\DiamondSales
   ```
2. انسخ محتويات مجلد `dist\DiamondSales` إلى مجلد `installer_package\DiamondSales`:
   ```
   xcopy /E /I /Y dist\DiamondSales\* installer_package\DiamondSales\
   ```

## الخطوة 3: إنشاء المثبت باستخدام Inno Setup

1. تأكد من وجود ملف `DiamondSales_Installer.iss` في مجلد `installer_package`
   - إذا لم يكن موجودًا، قم بنسخ ملف `diamond_sales_main_installer_arabic.iss` إلى `installer_package\DiamondSales_Installer.iss`

2. قم بتشغيل Inno Setup Compiler:
   - افتح برنامج Inno Setup Compiler من قائمة البرامج
   - أو قم بتشغيله مباشرة من خلال:
     ```
     "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" "c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\diamond_sales_main_installer_arabic.iss"
     ```

3. بعد اكتمال العملية، ستجد ملف المثبت في مجلد `installer_output`

## ملاحظات هامة

- تأكد من تثبيت Inno Setup 6 على جهازك
- يمكنك تنزيله من الموقع الرسمي: https://jrsoftware.org/isdl.php
- تأكد من وجود جميع الملفات المطلوبة في المجلدات الصحيحة قبل بدء عملية الإنشاء
- إذا واجهت أي مشاكل، تحقق من ملفات السجل في مجلد المشروع