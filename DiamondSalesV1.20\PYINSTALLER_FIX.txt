# مشكلة تنفيذ PyInstaller وكيفية حلها

## المشكلة

عند تنفيذ الأمر:
```
.venv\Scripts\pyinstaller.exe diamond_sales.spec
```

تظهر رسالة الخطأ التالية:
```
Fatal error in launcher: Unable to create process using '"c:\Users\<USER>\Desktop\DiamondSalesV1.20\.venv\Scripts\python.exe"  "c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\.venv\Scripts\pyinstaller.exe" diamond_sales.spec': The system cannot find the file specified.
```

## سبب المشكلة

المشكلة تكمن في أن PyInstaller يحاول استخدام مسار غير صحيح لملف Python. المسار الذي يحاول استخدامه هو:
`c:\Users\<USER>\Desktop\DiamondSalesV1.20\.venv\Scripts\python.exe`

بينما المسار الصحيح يجب أن يكون:
`c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\.venv\Scripts\python.exe`

## الحل

### الطريقة 1: استخدام المسار المطلق

يمكنك استخدام المسار المطلق لكل من Python و PyInstaller:

```
c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\.venv\Scripts\python.exe c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\.venv\Scripts\pyinstaller.exe diamond_sales.spec
```

### الطريقة 2: تنشيط البيئة الافتراضية أولاً

1. قم بتنشيط البيئة الافتراضية:
```
.venv\Scripts\activate
```

2. ثم قم بتنفيذ PyInstaller:
```
pyinstaller diamond_sales.spec
```

### الطريقة 3: إنشاء ملف باتش جديد

قم بإنشاء ملف باتش جديد باسم `build_with_correct_path.bat` يحتوي على:

```batch
@echo off
echo === بدء عملية إنشاء الملفات التنفيذية ===
set PYTHON_EXE=c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\.venv\Scripts\python.exe
set PYINSTALLER_EXE=c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\.venv\Scripts\pyinstaller.exe

echo استخدام Python من: %PYTHON_EXE%
echo استخدام PyInstaller من: %PYINSTALLER_EXE%

"%PYTHON_EXE%" "%PYINSTALLER_EXE%" diamond_sales.spec

if %ERRORLEVEL% NEQ 0 (
    echo فشل في إنشاء الملفات التنفيذية.
    pause
    exit /b 1
)

echo === تم إنشاء الملفات التنفيذية بنجاح ===
echo الملفات التنفيذية موجودة في مجلد dist\DiamondSales

echo === بدء عملية إنشاء المثبت ===
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" diamond_sales_main_installer_arabic.iss

if %ERRORLEVEL% NEQ 0 (
    echo فشل في إنشاء المثبت.
    pause
    exit /b 1
)

echo === تم إنشاء المثبت بنجاح ===
echo المثبت موجود في مجلد installer_output

pause
```

## ملاحظات إضافية

- تأكد من أن البيئة الافتراضية تم إنشاؤها بشكل صحيح
- تأكد من تثبيت PyInstaller في البيئة الافتراضية
- تأكد من وجود ملف diamond_sales.spec في المجلد الحالي