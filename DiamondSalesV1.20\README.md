# نظام إدارة مبيعات الألماس

## التحديثات الأخيرة

### أداة إعادة تعيين كلمة المرور الجديدة
- تم إضافة أداة `reset_password.exe` لاستعادة كلمات المرور المنسية عبر واجهة رسومية سهلة الاستخدام
- تتضمن الأداة نظام تحقق عبر البريد الإلكتروني مع إرسال رمز تحقق آمن
- توفر إمكانية توليد كلمات مرور قوية تلقائياً أو السماح للمستخدم بإنشاء كلمة مرور جديدة
- تتحقق من معايير قوة كلمة المرور وتحافظ على أمان حسابات المستخدمين
- متاحة في مجلد `tools` مع توثيق شامل في `TROUBLESHOOTING_LOGIN.md`

### أدوات إعادة تعيين محاولات تسجيل الدخول
- تم إضافة أداة `reset_login_attempts.exe` التي تسمح بعرض وإعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدمين
- تم إضافة أداة `quick_reset_admin_login.exe` التي تقوم بإعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم الافتراضي (admin) بنقرة واحدة
- تم توفير هذه الأدوات في مجلد `tools` مع ملف شرح `TROUBLESHOOTING_LOGIN.md`
- هذه الأدوات تساعد في حل مشكلة "تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول" التي تظهر أحياناً بعد تثبيت النظام

### توسيط النوافذ
- تم إضافة خاصية توسيط جميع نوافذ البرنامج في منتصف الشاشة
- تم إضافة دالة `center_window` في ملف `ui_utils.py` لتوسيط النوافذ
- تم تعديل جميع الشاشات لاستخدام هذه الدالة
- تم إضافة فئات أساسية في ملف `base_window.py` يمكن استخدامها في المستقبل لتوسيط النوافذ تلقائياً

## كيفية استخدام الفئات الأساسية الجديدة

لإنشاء نافذة جديدة تتوسط الشاشة تلقائياً، يمكن استخدام الفئات الأساسية الموجودة في ملف `base_window.py`:

```python
from base_window import BaseQWidget

class MyNewWindow(BaseQWidget):
    def __init__(self):
        super().__init__()
        # قم بإعداد النافذة هنا
```

أو يمكن استخدام دالة `center_window` مباشرة:

```python
from ui_utils import center_window

# إنشاء النافذة
window = MyWindow()

# توسيط النافذة
center_window(window)

# عرض النافذة
window.show()
```
