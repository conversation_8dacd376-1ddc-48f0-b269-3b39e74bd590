# أدوات إعادة التعيين واستعادة كلمة المرور لنظام إدارة مبيعات الألماس

هذه الأداة تسمح بإعادة تعيين قاعدة بيانات النظام وتفريغها من جميع البيانات، مع الاحتفاظ بمعلومات المستخدمين والشركة.

## الاستخدام

1. قم بإغلاق البرنامج الرئيسي إذا كان قيد التشغيل.
2. قم بتشغيل أداة إعادة التعيين باستخدام الأمر التالي:

```
python reset_diamond_system.py
```

3. اكتب "ن" للتأكيد عندما يُطلب منك ذلك.
4. سيتم إنشاء نسخة احتياطية من قاعدة البيانات في مجلد "backups" قبل إجراء أي تغييرات.
5. سيتم تفريغ جميع الجداول باستثناء جداول المستخدمين ومعلومات الشركة.
6. بعد الانتهاء، يمكنك تشغيل البرنامج الرئيسي واستخدامه كما لو كان تثبيتًا جديدًا.

## ملاحظات هامة

- يتم الاحتفاظ بمعلومات المستخدمين، لذا يمكنك تسجيل الدخول باستخدام نفس بيانات الاعتماد.
- يتم الاحتفاظ بمعلومات الشركة (الاسم، الشعار، إلخ).
- يتم تفريغ جميع البيانات الأخرى بما في ذلك:
  - العملاء
  - الموردين
  - المبيعات
  - المشتريات
  - المدفوعات
  - المقبوضات
  - القيود المحاسبية
  - وغيرها من البيانات

## استعادة النسخة الاحتياطية

إذا كنت بحاجة إلى استعادة النسخة الاحتياطية، يمكنك ببساطة نسخ ملف النسخة الاحتياطية من مجلد "backups" واستبداله بملف "diamond_sales.db" الحالي.

## الدعم الفني

إذا واجهت أي مشاكل أو كانت لديك أسئلة، يرجى التواصل مع فريق الدعم الفني.
