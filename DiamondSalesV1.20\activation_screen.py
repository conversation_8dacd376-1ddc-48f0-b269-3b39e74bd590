"""
شاشة تفعيل البرنامج
تستخدم هذه الشاشة لتفعيل البرنامج باستخدام كود التفعيل
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QMessageBox, QGridLayout, QGroupBox)
from PyQt6.QtGui import QFont, QIcon, QPixmap
from PyQt6.QtCore import Qt, QSize
from activation import get_hardware_id, validate_activation_code, save_activation_code, get_activation_info
from logger import log_error, log_info
from datetime import datetime
from translations import get_translation

_ = get_translation

class ActivationScreen(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.init_ui()

    def init_ui(self):
        """
        تهيئة واجهة المستخدم
        """
        self.setWindowTitle("تفعيل البرنامج - نظام إدارة مبيعات الألماس")
        self.setGeometry(100, 100, 600, 400)
        self.setWindowIcon(QIcon('assets/diamond_icon.png'))
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # إضافة شعار البرنامج
        logo_layout = QHBoxLayout()
        logo_label = QLabel()
        logo_pixmap = QPixmap('assets/diamond_logo.png')
        if not logo_pixmap.isNull():
            logo_label.setPixmap(logo_pixmap.scaled(150, 150, Qt.AspectRatioMode.KeepAspectRatio))
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_layout.addWidget(logo_label)
        main_layout.addLayout(logo_layout)

        # إضافة عنوان الشاشة
        title_label = QLabel(_("activation_title", "تفعيل البرنامج"))
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # إضافة مجموعة معلومات الجهاز
        hardware_group = QGroupBox("معلومات الجهاز")
        hardware_layout = QGridLayout()

        # إضافة معرف الجهاز
        hardware_id_label = QLabel(_("hardware_id_label", "معرف الجهاز:"))
        self.hardware_id = QLineEdit()
        self.hardware_id.setText(get_hardware_id())
        self.hardware_id.setReadOnly(True)

        hardware_layout.addWidget(hardware_id_label, 0, 0)
        hardware_layout.addWidget(self.hardware_id, 0, 1)

        hardware_group.setLayout(hardware_layout)
        main_layout.addWidget(hardware_group)

        # إضافة مجموعة التفعيل
        activation_group = QGroupBox("كود التفعيل")
        activation_layout = QVBoxLayout()

        # إضافة حقل كود التفعيل
        activation_code_label = QLabel(_("activation_code_label", "أدخل كود التفعيل بالتنسيق: XXXX-XXXX-XXXX-XXXX-XXXX"))
        activation_layout.addWidget(activation_code_label)

        self.activation_code = QLineEdit()
        self.activation_code.setPlaceholderText("أدخل كود التفعيل هنا...")
        self.activation_code.setInputMask("NNNN-NNNN-NNNN-NNNN-NNNN")
        activation_layout.addWidget(self.activation_code)

        # إضافة زر التفعيل
        activate_button = QPushButton("تفعيل البرنامج")
        activate_button.clicked.connect(self.activate_program)
        activation_layout.addWidget(activate_button)

        activation_group.setLayout(activation_layout)
        main_layout.addWidget(activation_group)

        # إضافة معلومات التفعيل الحالية
        self.activation_info_group = QGroupBox("معلومات التفعيل الحالية")
        self.activation_info_layout = QGridLayout()

        # إضافة حالة التفعيل
        status_label = QLabel(_("activation_status_label", "حالة التفعيل:"))
        self.status_value = QLabel(_("not_activated", "غير مفعل"))

        # إضافة تاريخ انتهاء الصلاحية
        expiry_label = QLabel(_("expiry_date_label", "تاريخ انتهاء الصلاحية:"))
        self.expiry_value = QLabel("-")

        # إضافة الأيام المتبقية
        days_left_label = QLabel(_("days_left_label", "الأيام المتبقية:"))
        self.days_left_value = QLabel("-")

        # إضافة الميزات المفعلة
        features_label = QLabel(_("activated_features_label", "الميزات المفعلة:"))
        self.features_value = QLabel("-")

        # إضافة العناصر إلى التخطيط
        self.activation_info_layout.addWidget(status_label, 0, 0)
        self.activation_info_layout.addWidget(self.status_value, 0, 1)
        self.activation_info_layout.addWidget(expiry_label, 1, 0)
        self.activation_info_layout.addWidget(self.expiry_value, 1, 1)
        self.activation_info_layout.addWidget(days_left_label, 2, 0)
        self.activation_info_layout.addWidget(self.days_left_value, 2, 1)
        self.activation_info_layout.addWidget(features_label, 3, 0)
        self.activation_info_layout.addWidget(self.features_value, 3, 1)

        self.activation_info_group.setLayout(self.activation_info_layout)
        main_layout.addWidget(self.activation_info_group)

        # تحديث معلومات التفعيل
        self.update_activation_info()

    def update_activation_info(self):
        """
        تحديث معلومات التفعيل
        """
        activation_info = get_activation_info()

        if activation_info:
            self.status_value.setText("مفعل")
            self.status_value.setStyleSheet("color: green; font-weight: bold;")

            expiry_date = activation_info['expiry_date']
            self.expiry_value.setText(expiry_date.strftime("%Y-%m-%d"))

            days_left = activation_info['days_left']
            self.days_left_value.setText(str(days_left))

            if days_left < 30:
                self.days_left_value.setStyleSheet("color: red; font-weight: bold;")
            else:
                self.days_left_value.setStyleSheet("color: green; font-weight: bold;")

            features = ", ".join(activation_info['features'])
            self.features_value.setText(features)
        else:
            self.status_value.setText("غير مفعل")
            self.status_value.setStyleSheet("color: red; font-weight: bold;")
            self.expiry_value.setText("-")
            self.days_left_value.setText("-")
            self.features_value.setText("-")

    def activate_program(self):
        """
        تفعيل البرنامج باستخدام كود التفعيل
        """
        activation_code = self.activation_code.text().strip()

        if not activation_code:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال كود التفعيل")
            return

        # التحقق من صحة كود التفعيل
        hardware_id = get_hardware_id()
        activation_info = validate_activation_code(activation_code, hardware_id)

        if not activation_info:
            QMessageBox.critical(self, "خطأ", "كود التفعيل غير صالح أو منتهي الصلاحية")
            return

        # حفظ كود التفعيل
        if save_activation_code(activation_code):
            QMessageBox.information(self, "تم التفعيل", "تم تفعيل البرنامج بنجاح")
            log_info(f"تم تفعيل البرنامج بنجاح - معرف الجهاز: {hardware_id}")

            # تحديث معلومات التفعيل
            self.update_activation_info()

            # إغلاق شاشة التفعيل وفتح شاشة تسجيل الدخول
            if self.parent:
                self.parent.show_login_screen()
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تفعيل البرنامج")
            log_error(f"فشل تفعيل البرنامج - معرف الجهاز: {hardware_id}")
