"""
وحدة إدارة حالة التطبيق
تستخدم هذه الوحدة لإدارة الحالة العامة للتطبيق، مثل المستخدم الحالي والإعدادات
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from database import User, Setting
from logger import log_info, log_error

# إنشاء محرك قاعدة البيانات
engine = create_engine('sqlite:///diamond_sales.db')

# إنشاء مصنع الجلسات
session_factory = sessionmaker(bind=engine)

# إنشاء جلسة مرتبطة بالسلسلة الحالية (thread-local)
Session = scoped_session(session_factory)

# المستخدم الحالي
current_user = None

# الإعدادات الحالية
current_settings = None

def initialize_app():
    """
    تهيئة حالة التطبيق
    """
    try:
        # تحميل الإعدادات
        load_settings()
        log_info("تم تهيئة حالة التطبيق بنجاح")
        return True
    except Exception as e:
        log_error("خطأ في تهيئة حالة التطبيق", e)
        return False

def load_settings():
    """
    تحميل إعدادات التطبيق
    """
    global current_settings
    
    try:
        session = Session()
        current_settings = session.query(Setting).first()
        session.close()
        
        if not current_settings:
            log_info("لم يتم العثور على إعدادات، سيتم استخدام الإعدادات الافتراضية")
        
        return current_settings
    except Exception as e:
        log_error("خطأ في تحميل إعدادات التطبيق", e)
        return None

def set_current_user(user_id=None, username=None):
    """
    تعيين المستخدم الحالي
    
    Args:
        user_id (int, optional): معرف المستخدم
        username (str, optional): اسم المستخدم
        
    Returns:
        User: كائن المستخدم الحالي أو None في حالة الفشل
    """
    global current_user
    
    if not user_id and not username:
        log_error("يجب تحديد معرف المستخدم أو اسم المستخدم")
        return None
    
    try:
        session = Session()
        
        if user_id:
            current_user = session.query(User).filter_by(id=user_id).first()
        elif username:
            current_user = session.query(User).filter_by(username=username).first()
            
        session.close()
        
        if current_user:
            log_info(f"تم تعيين المستخدم الحالي: {current_user.username}")
        else:
            log_error(f"لم يتم العثور على المستخدم")
            
        return current_user
    except Exception as e:
        log_error("خطأ في تعيين المستخدم الحالي", e)
        return None

def get_current_user():
    """
    الحصول على المستخدم الحالي
    
    Returns:
        User: كائن المستخدم الحالي أو None إذا لم يكن هناك مستخدم حالي
    """
    global current_user
    
    if not current_user:
        log_error("لا يوجد مستخدم حالي")
        return None
        
    try:
        # تحديث بيانات المستخدم من قاعدة البيانات
        session = Session()
        user = session.query(User).filter_by(id=current_user.id).first()
        session.close()
        
        if user:
            # تحديث المستخدم الحالي
            current_user = user
            return user
        else:
            log_error("لم يتم العثور على المستخدم الحالي في قاعدة البيانات")
            current_user = None
            return None
    except Exception as e:
        log_error("خطأ في الحصول على المستخدم الحالي", e)
        return None

def clear_current_user():
    """
    مسح المستخدم الحالي
    """
    global current_user
    current_user = None
    log_info("تم مسح المستخدم الحالي")

def get_fresh_session():
    """
    الحصول على جلسة جديدة
    
    Returns:
        Session: جلسة جديدة
    """
    return Session()

def close_all_sessions():
    """
    إغلاق جميع الجلسات
    """
    Session.remove()
    log_info("تم إغلاق جميع الجلسات")
