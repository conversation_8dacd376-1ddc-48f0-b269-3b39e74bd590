# PowerShell script to build Diamond Sales installer

Write-Host "=== Starting build process for version 1.30 ===" -ForegroundColor Green

# Set working directory to script location
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

# Define paths
$pythonExe = Join-Path -Path $scriptPath -ChildPath ".venv\Scripts\python.exe"
$pyinstallerExe = Join-Path -Path $scriptPath -ChildPath ".venv\Scripts\pyinstaller.exe"
$distFolder = Join-Path -Path $scriptPath -ChildPath "dist\DiamondSales"
$installerPackageFolder = Join-Path -Path $scriptPath -ChildPath "installer_package"
$installerIssFile = Join-Path -Path $installerPackageFolder -ChildPath "DiamondSales_Installer.iss"
$specFile = Join-Path -Path $scriptPath -ChildPath "diamond_sales.spec"

# Check if Python and PyInstaller exist
if (-not (Test-Path $pythonExe)) {
    Write-Host "Python executable not found at: $pythonExe" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $pyinstallerExe)) {
    Write-Host "PyInstaller not found at: $pyinstallerExe" -ForegroundColor Red
    exit 1
}

# Build main program
Write-Host "=== Building main program ===" -ForegroundColor Green

# Delete previous output folder if exists
if (Test-Path $distFolder) {
    Write-Host "Deleting previous output folder..." -ForegroundColor Yellow
    Remove-Item -Path $distFolder -Recurse -Force
}

# Run PyInstaller
Write-Host "Running PyInstaller..." -ForegroundColor Yellow
Write-Host "Using Python: $pythonExe" -ForegroundColor Cyan
Write-Host "Using PyInstaller: $pyinstallerExe" -ForegroundColor Cyan

$pyinstallerProcess = Start-Process -FilePath $pyinstallerExe -ArgumentList $specFile -NoNewWindow -PassThru -Wait
if ($pyinstallerProcess.ExitCode -ne 0) {
    Write-Host "Error occurred while building the main program." -ForegroundColor Red
    exit 1
}

# Create distribution folder
Write-Host "=== Creating distribution folder ===" -ForegroundColor Green

if (-not (Test-Path $installerPackageFolder)) {
    Write-Host "Creating installer package folder..." -ForegroundColor Yellow
    New-Item -Path $installerPackageFolder -ItemType Directory -Force | Out-Null
}

# Copy files to distribution folder
Write-Host "Copying files to distribution folder..." -ForegroundColor Yellow
$distDiamondSalesFolder = Join-Path -Path $installerPackageFolder -ChildPath "DiamondSales"
if (Test-Path $distDiamondSalesFolder) {
    Remove-Item -Path $distDiamondSalesFolder -Recurse -Force
}
Copy-Item -Path $distFolder -Destination $installerPackageFolder -Recurse -Force

# Find Inno Setup Compiler
Write-Host "=== Creating installer ===" -ForegroundColor Green

$innoSetupPaths = @(
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
    "C:\Program Files\Inno Setup 6\ISCC.exe",
    "C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
    "C:\Program Files\Inno Setup 5\ISCC.exe"
)

$innoSetupPath = $null
foreach ($path in $innoSetupPaths) {
    if (Test-Path $path) {
        $innoSetupPath = $path
        break
    }
}

if ($null -eq $innoSetupPath) {
    Write-Host "Inno Setup Compiler not found. Please install it first." -ForegroundColor Red
    Write-Host "You can download it from: https://jrsoftware.org/isdl.php" -ForegroundColor Yellow
    exit 1
}

# Run Inno Setup Compiler
Write-Host "Running Inno Setup Compiler..." -ForegroundColor Yellow

if (Test-Path $installerIssFile) {
    Write-Host "Using DiamondSales installer setup file..." -ForegroundColor Cyan
    $innoSetupProcess = Start-Process -FilePath $innoSetupPath -ArgumentList "\"$installerIssFile\"" -NoNewWindow -PassThru -Wait
    if ($innoSetupProcess.ExitCode -ne 0) {
        Write-Host "Error occurred while creating the installer." -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "Setup file not found. Please check for DiamondSales_Installer.iss in installer_package folder." -ForegroundColor Red
    exit 1
}

# Success message
Write-Host "=== Build process completed successfully ===" -ForegroundColor Green
Write-Host "Installer is located in the 'installer_output' folder." -ForegroundColor Cyan

# Open installer output folder
$installerOutputFolder = Join-Path -Path $scriptPath -ChildPath "installer_output"
if (Test-Path $installerOutputFolder) {
    Start-Process -FilePath "explorer.exe" -ArgumentList $installerOutputFolder
}

Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")