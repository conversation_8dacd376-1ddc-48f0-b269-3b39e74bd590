@echo off
chcp 65001

echo === Starting build process for version 1.30 ===

cd /d "%~dp0"

echo === Building main program ===

if exist "dist\DiamondSales" (
    echo Deleting previous output folder...
    rmdir /s /q "dist\DiamondSales"
)

echo Running PyInstaller...

set PYTHON_EXE="%~dp0.venv\Scripts\python.exe"
set PYINSTALLER_EXE="%~dp0.venv\Scripts\pyinstaller.exe"

echo Using Python: %PYTHON_EXE%
echo Using PyInstaller: %PYINSTALLER_EXE%

%PYINSTALLER_EXE% diamond_sales.spec

if %ERRORLEVEL% neq 0 (
    echo Error occurred while building the main program.
    pause
    exit /b 1
)

echo === Creating distribution folder ===

if not exist "installer_package" (
    mkdir "installer_package"
)

echo Copying files to distribution folder...
xcopy /E /I /Y "dist\DiamondSales" "installer_package\DiamondSales"

echo === Creating installer ===

set INNO_SETUP_FOUND=0

if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set INNO_SETUP_PATH="C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    set INNO_SETUP_FOUND=1
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set INNO_SETUP_PATH="C:\Program Files\Inno Setup 6\ISCC.exe"
    set INNO_SETUP_FOUND=1
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    set INNO_SETUP_PATH="C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
    set INNO_SETUP_FOUND=1
) else if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    set INNO_SETUP_PATH="C:\Program Files\Inno Setup 5\ISCC.exe"
    set INNO_SETUP_FOUND=1
)

if %INNO_SETUP_FOUND% equ 0 (
    echo Inno Setup Compiler not found. Please install it first.
    echo You can download it from: https://jrsoftware.org/isdl.php
    pause
    exit /b 1
)

echo Running Inno Setup Compiler...
cd /d "%~dp0"

if exist "installer_package\DiamondSales_Installer.iss" (
    echo Using DiamondSales installer setup file...
    %INNO_SETUP_PATH% "installer_package\DiamondSales_Installer.iss"
) else (
    echo Setup file not found. Please check for DiamondSales_Installer.iss in installer_package folder.
    pause
    exit /b 1
)

if %ERRORLEVEL% neq 0 (
    echo Error occurred while creating the installer.
    pause
    exit /b 1
)

echo === Build process completed successfully ===
echo Installer is located in the 'installer_output' folder.

pause