@echo off
echo === بدء عملية إنشاء الملفات التنفيذية ===
set PYTHON_EXE=c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\.venv\Scripts\python.exe
set PYINSTALLER_EXE=c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\.venv\Scripts\pyinstaller.exe

echo استخدام Python من: %PYTHON_EXE%
echo استخدام PyInstaller من: %PYINSTALLER_EXE%

"%PYTHON_EXE%" "%PYINSTALLER_EXE%" diamond_sales.spec

if %ERRORLEVEL% NEQ 0 (
    echo فشل في إنشاء الملفات التنفيذية.
    pause
    exit /b 1
)

echo === تم إنشاء الملفات التنفيذية بنجاح ===
echo الملفات التنفيذية موجودة في مجلد dist\DiamondSales

echo === نسخ الملفات إلى مجلد المثبت ===
if not exist "installer_package\DiamondSales" mkdir "installer_package\DiamondSales"
xcopy /E /I /Y "dist\DiamondSales\*" "installer_package\DiamondSales\"

echo === بدء عملية إنشاء المثبت ===
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" diamond_sales_main_installer_arabic.iss

if %ERRORLEVEL% NEQ 0 (
    echo فشل في إنشاء المثبت.
    pause
    exit /b 1
)

echo === تم إنشاء المثبت بنجاح ===
echo المثبت موجود في مجلد installer_output

pause