"""
نظام التخزين المؤقت للتطبيق
"""

import time
import threading
from functools import wraps
from logger import log_error, log_info

# قاموس للتخزين المؤقت
_cache = {}
_cache_lock = threading.RLock()

def cached(timeout=300):
    """
    مزخرف (decorator) للتخزين المؤقت لنتائج الدوال
    
    Args:
        timeout (int): مدة صلاحية التخزين المؤقت بالثواني (الافتراضي: 300 ثانية)
    
    Returns:
        function: الدالة المزخرفة
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # إنشاء مفتاح فريد للتخزين المؤقت
            key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
            
            with _cache_lock:
                # التحقق من وجود النتيجة في التخزين المؤقت
                if key in _cache:
                    result, timestamp = _cache[key]
                    # التحقق من صلاحية التخزين المؤقت
                    if time.time() - timestamp < timeout:
                        return result
            
            # تنفيذ الدالة وتخزين النتيجة
            result = func(*args, **kwargs)
            
            with _cache_lock:
                _cache[key] = (result, time.time())
            
            return result
        return wrapper
    return decorator

def clear_cache():
    """
    مسح جميع العناصر من التخزين المؤقت
    """
    with _cache_lock:
        _cache.clear()
    log_info("تم مسح التخزين المؤقت")

def clear_cache_for_function(func_name):
    """
    مسح العناصر المرتبطة بدالة معينة من التخزين المؤقت
    
    Args:
        func_name (str): اسم الدالة
    """
    with _cache_lock:
        keys_to_remove = [key for key in _cache if key.startswith(f"{func_name}:")]
        for key in keys_to_remove:
            del _cache[key]
    log_info(f"تم مسح التخزين المؤقت للدالة: {func_name}")

def get_cache_size():
    """
    الحصول على حجم التخزين المؤقت
    
    Returns:
        int: عدد العناصر في التخزين المؤقت
    """
    with _cache_lock:
        return len(_cache)

def get_cache_keys():
    """
    الحصول على مفاتيح التخزين المؤقت
    
    Returns:
        list: قائمة بمفاتيح التخزين المؤقت
    """
    with _cache_lock:
        return list(_cache.keys())

# مثال على استخدام التخزين المؤقت
@cached(timeout=60)
def expensive_operation(param1, param2):
    """
    مثال على عملية مكلفة تستفيد من التخزين المؤقت
    
    Args:
        param1: المعامل الأول
        param2: المعامل الثاني
    
    Returns:
        النتيجة
    """
    # محاكاة عملية مكلفة
    time.sleep(2)
    return param1 + param2
