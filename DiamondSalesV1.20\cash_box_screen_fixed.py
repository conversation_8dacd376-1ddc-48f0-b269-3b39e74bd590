from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QTableWidget, QTableWidgetItem, QDateEdit, QComboBox,
                            QLineEdit, QFormLayout, QMessageBox, QHeaderView, QGroupBox,
                            QDialog, QDoubleSpinBox, QTextEdit)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QColor, QFont
from datetime import datetime
from sqlalchemy import func, desc, or_, case, String, Float
from ui_utils import confirm_dialog
from db_session import session_scope
from database import صندوق_النقدية, حركة_نقدية, Sale, Purchase, Receipt, Supplier, Customer
from permissions import check_permission
import re

class حوار_حركة_نقدية(QDialog):
    """حوار لإضافة حركة نقدية جديدة"""
    
    def __init__(self, transaction_type="deposit"):
        super().__init__()
        self.transaction_type = transaction_type
        self.init_ui()
    
    def init_ui(self):
        title = "إضافة إيداع نقدي" if self.transaction_type == "deposit" else "إضافة سحب نقدي"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QFormLayout()
        
        # المبلغ
        self.amount_spinbox = QDoubleSpinBox()
        self.amount_spinbox.setMinimum(0.01)
        self.amount_spinbox.setMaximum(999999.99)
        self.amount_spinbox.setDecimals(2)
        self.amount_spinbox.setSuffix(" $")
        layout.addRow("المبلغ:", self.amount_spinbox)
        
        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        layout.addRow("التاريخ:", self.date_edit)
        
        # المرجع
        self.reference_edit = QLineEdit()
        layout.addRow("المرجع:", self.reference_edit)
        
        # الوصف
        self.description_text = QTextEdit()
        self.description_text.setMaximumHeight(100)
        layout.addRow("الوصف:", self.description_text)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addRow(buttons_layout)
        self.setLayout(layout)

class شاشة_صندوق_النقدية(QWidget):
    refresh_signal = pyqtSignal()

    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_ui()
        self.load_data()

    def init_ui(self):
        self.setWindowTitle("صندوق النقدية")
        self.setGeometry(100, 100, 1000, 600)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # مجموعة ملخص الصندوق
        summary_group = QGroupBox("ملخص الصندوق")
        summary_layout = QHBoxLayout()
        
        # ملصقات رصيد الصندوق
        self.total_balance_label = QLabel("الرصيد الحالي: 0.00 $")
        self.total_balance_label.setStyleSheet("font-size: 16px; font-weight: bold; color: green;")
        
        self.total_in_label = QLabel("إجمالي الإيداعات: 0.00 $")
        self.total_in_label.setStyleSheet("color: blue;")
        
        self.total_out_label = QLabel("إجمالي المسحوبات: 0.00 $")
        self.total_out_label.setStyleSheet("color: red;")
        
        summary_layout.addWidget(self.total_balance_label)
        summary_layout.addWidget(self.total_in_label)
        summary_layout.addWidget(self.total_out_label)
        summary_group.setLayout(summary_layout)
        
        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(summary_group)
        
        # مجموعة البحث
        search_group = QGroupBox("بحث")
        search_layout = QHBoxLayout()
        
        # تاريخ البدء
        start_date_label = QLabel("من تاريخ:")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        
        # تاريخ الانتهاء
        end_date_label = QLabel("إلى تاريخ:")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())
        
        # نوع الحركة
        transaction_type_label = QLabel("نوع الحركة:")
        self.transaction_type_combo = QComboBox()
        self.transaction_type_combo.addItem("الكل", "all")
        self.transaction_type_combo.addItem("إيداع", "deposit")
        self.transaction_type_combo.addItem("سحب", "withdraw")
        
        # زر البحث
        self.search_button = QPushButton("بحث")
        self.search_button.clicked.connect(self.search_transactions)
        
        # إضافة عناصر البحث إلى تخطيط البحث
        search_layout.addWidget(start_date_label)
        search_layout.addWidget(self.start_date_edit)
        search_layout.addWidget(end_date_label)
        search_layout.addWidget(self.end_date_edit)
        search_layout.addWidget(transaction_type_label)
        search_layout.addWidget(self.transaction_type_combo)
        search_layout.addWidget(self.search_button)
        search_group.setLayout(search_layout)
        
        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(search_group)
        
        # مجموعة أزرار الإجراءات
        actions_group = QGroupBox("الإجراءات")
        actions_layout = QHBoxLayout()
          # زر إيداع نقدي
        self.deposit_button = QPushButton("إيداع نقدي")
        self.deposit_button.clicked.connect(self.add_deposit)
        
        # زر سحب نقدي
        self.withdraw_button = QPushButton("سحب نقدي")
        self.withdraw_button.clicked.connect(self.add_withdrawal)
        
        # زر طباعة التقرير
        self.print_button = QPushButton("طباعة التقرير")
        self.print_button.clicked.connect(self.print_report)
        
        # إضافة أزرار الإجراءات إلى تخطيط الإجراءات
        actions_layout.addWidget(self.deposit_button)
        actions_layout.addWidget(self.withdraw_button)
        actions_layout.addWidget(self.print_button)
        
        # التحقق من صلاحيات المستخدم للتعديل
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            self.deposit_button.setEnabled(False)
            self.withdraw_button.setEnabled(False)
            
        actions_group.setLayout(actions_layout)
        
        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(actions_group)
        
        # جدول حركات الصندوق
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(7)
        self.transactions_table.setHorizontalHeaderLabels(["رقم", "التاريخ", "النوع", "المبلغ", "الرصيد بعد العملية", "المرجع", "الوصف"])
        self.transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.transactions_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(self.transactions_table)
        
        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)
        
        # الاتصال بإشارة التحديث
        self.refresh_signal.connect(self.load_data)

    def load_data(self):
        """تحميل بيانات الصندوق وحركاته"""
        try:
            with session_scope() as session:
                # استعلام عن رصيد الصندوق
                cash_box = session.query(صندوق_النقدية).first()
                
                if not cash_box:
                    # إنشاء صندوق جديد إذا لم يكن موجوداً
                    cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                    session.add(cash_box)
                    session.commit()
                
                # تحديث ملصقات الملخص
                self.total_balance_label.setText(f"الرصيد الحالي: {cash_box.balance:.2f} $")
                
                # حساب إجمالي الإيداعات والمسحوبات
                start_date = datetime.combine(self.start_date_edit.date().toPyDate(), datetime.min.time())
                end_date = datetime.combine(self.end_date_edit.date().toPyDate(), datetime.max.time())
                
                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0
                
                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0
                
                self.total_in_label.setText(f"إجمالي الإيداعات: {total_deposits:.2f} $")
                self.total_out_label.setText(f"إجمالي المسحوبات: {total_withdrawals:.2f} $")
                
                # تحميل الحركات في الجدول                self.load_transactions(session, start_date, end_date)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الصندوق: {str(e)}")
            
    def load_transactions(self, session, start_date, end_date, transaction_type="all"):
        """تحميل حركات الصندوق إلى الجدول"""
        try:
            # بناء الاستعلام للحركات النقدية المباشرة
            query = session.query(حركة_نقدية).filter(
                حركة_نقدية.transaction_date >= start_date,
                حركة_نقدية.transaction_date <= end_date
            )
            
            if transaction_type != "all":
                query = query.filter(حركة_نقدية.transaction_type == transaction_type)            # استعلام سندات الصرف (المسحوبات) من المشتريات
            purchase_query = session.query(
                Purchase.id,
                Purchase.purchase_date.label('transaction_date'),
                func.cast('withdraw', String).label('transaction_type'),
                Purchase.amount_paid.label('amount'),  # تأكد من استخدام amount_paid وليس paid_amount
                func.cast(0.0, Float).label('balance_after'),  # سيتم حسابه لاحقًا
                func.cast("فاتورة مشتريات #" + func.cast(Purchase.id, String), String).label('reference'),  # استخدام + بدلاً من func.concat
                func.cast("دفعة لفاتورة المورد " + Supplier.name, String).label('description'),  # استخدام + بدلاً من func.concat
                Purchase.id.label('created_by')  # استخدام معرف الشراء إذا لم يكن هناك created_by
            ).join(Supplier, Purchase.supplier_id == Supplier.id).filter(
                Purchase.purchase_date >= start_date,
                Purchase.purchase_date <= end_date,
                Purchase.amount_paid > 0
            )
              # استعلام سندات الصرف (المسحوبات) من سندات الصرف المباشرة
            receipt_query = session.query(
                Receipt.id,
                Receipt.issue_date.label('transaction_date'),
                func.cast('withdraw', String).label('transaction_type'),
                Receipt.amount_usd.label('amount'),
                func.cast(0.0, Float).label('balance_after'),  # سيتم حسابه لاحقًا
                func.cast("سند صرف #" + func.cast(Receipt.id, String), String).label('reference'),
                func.cast(
                    case(
                        (Receipt.supplier_id != None, "دفعة للمورد " + Supplier.name), 
                        else_="سند صرف نقدي"
                    ), 
                    String
                ).label('description'),
                Receipt.created_by.label('created_by')  # استخدام معرف المنشئ المخزن في سجل السند
            ).outerjoin(Supplier, Receipt.supplier_id == Supplier.id).filter(
                Receipt.issue_date >= start_date,
                Receipt.issue_date <= end_date,
                or_(Receipt.receipt_type == "CashOut", Receipt.receipt_type == "صرف")  # دعم كلا النوعين من سندات الصرف
            )
            
            if transaction_type == "deposit":
                purchase_query = purchase_query.filter(False)  # لا تضمن سندات الصرف إذا كان البحث عن الإيداعات فقط
                receipt_query = receipt_query.filter(False)  # لا تضمن سندات الصرف إذا كان البحث عن الإيداعات فقط
            
            # ترتيب حسب التاريخ تنازلياً
            transactions = query.order_by(desc(حركة_نقدية.transaction_date)).all()
            
            # دمج سندات الصرف مع الحركات النقدية إذا لم يكن البحث عن الإيداعات فقط
            if transaction_type != "deposit":
                # إضافة المسحوبات من فواتير المشتريات
                purchase_transactions = purchase_query.all()
                for pt in purchase_transactions:
                    # إنشاء كائن مؤقت لحركة نقدية من سند الصرف
                    temp_transaction = type('TransactionLike', (), {
                        'id': pt.id,
                        'transaction_date': pt.transaction_date,
                        'transaction_type': pt.transaction_type,
                        'amount': pt.amount,
                        'balance_after': pt.balance_after,
                        'reference': pt.reference,
                        'description': pt.description,
                        'created_by': pt.created_by
                    })
                    transactions.append(temp_transaction)
                
                # إضافة المسحوبات من سندات الصرف
                receipt_transactions = receipt_query.all()
                for rt in receipt_transactions:
                    # إنشاء كائن مؤقت لحركة نقدية من سند الصرف
                    temp_transaction = type('TransactionLike', (), {
                        'id': rt.id,
                        'transaction_date': rt.transaction_date,
                        'transaction_type': rt.transaction_type,
                        'amount': rt.amount,
                        'balance_after': rt.balance_after,
                        'reference': rt.reference,
                        'description': rt.description,
                        'created_by': rt.created_by
                    })
                    transactions.append(temp_transaction)
                
                # إعادة ترتيب جميع الحركات حسب التاريخ
                transactions.sort(key=lambda x: x.transaction_date, reverse=True)
            
            # تعيين عدد الصفوف
            self.transactions_table.setRowCount(len(transactions))
            
            # ملء بيانات الجدول
            for row, transaction in enumerate(transactions):
                # رقم العملية
                id_item = QTableWidgetItem(str(transaction.id))
                id_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 0, id_item)
                
                # التاريخ
                date_item = QTableWidgetItem(transaction.transaction_date.strftime("%Y-%m-%d %H:%M"))
                date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 1, date_item)
                
                # النوع
                type_text = "إيداع" if transaction.transaction_type == "deposit" else "سحب"
                type_item = QTableWidgetItem(type_text)
                type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                
                # تلوين الخلية حسب النوع
                if transaction.transaction_type == "deposit":
                    type_item.setBackground(QColor(200, 250, 200))  # أخضر فاتح للإيداعات
                else:
                    type_item.setBackground(QColor(250, 200, 200))  # أحمر فاتح للمسحوبات
                
                self.transactions_table.setItem(row, 2, type_item)
                
                # المبلغ
                amount_item = QTableWidgetItem(f"{transaction.amount:.2f} $")
                amount_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 3, amount_item)
                
                # الرصيد بعد العملية
                balance_item = QTableWidgetItem(f"{transaction.balance_after:.2f} $")
                balance_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 4, balance_item)
                
                # المرجع
                reference_item = QTableWidgetItem(transaction.reference or "")
                reference_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 5, reference_item)
                  # الوصف
                description_item = QTableWidgetItem(transaction.description or "")
                self.transactions_table.setItem(row, 6, description_item)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل حركات الصندوق: {str(e)}")
            
    def search_transactions(self):
        """البحث في حركات الصندوق حسب المعايير المحددة"""
        try:
            start_date = datetime.combine(self.start_date_edit.date().toPyDate(), datetime.min.time())
            end_date = datetime.combine(self.end_date_edit.date().toPyDate(), datetime.max.time())
            transaction_type = self.transaction_type_combo.currentData()
            
            with session_scope() as session:
                self.load_transactions(session, start_date, end_date, transaction_type)
                
                # تحديث ملخص الصندوق
                cash_box = session.query(صندوق_النقدية).first()
                if cash_box:
                    self.total_balance_label.setText(f"الرصيد الحالي: {cash_box.balance:.2f} $")
                
                # حساب إجمالي الإيداعات والمسحوبات للفترة المحددة
                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0
                
                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0
                
                # حساب المسحوبات من فواتير المشتريات
                purchase_withdrawals = session.query(func.sum(Purchase.amount_paid)).filter(
                    Purchase.purchase_date >= start_date,
                    Purchase.purchase_date <= end_date,
                    Purchase.amount_paid > 0
                ).scalar() or 0
                
                # حساب المسحوبات من سندات الصرف
                receipt_withdrawals = session.query(func.sum(Receipt.amount_usd)).filter(
                    Receipt.issue_date >= start_date,
                    Receipt.issue_date <= end_date,
                    or_(Receipt.receipt_type == "CashOut", Receipt.receipt_type == "صرف")
                ).scalar() or 0
                
                total_all_withdrawals = total_withdrawals + purchase_withdrawals + receipt_withdrawals
                
                self.total_in_label.setText(f"إجمالي الإيداعات: {total_deposits:.2f} $")
                self.total_out_label.setText(f"إجمالي المسحوبات: {total_all_withdrawals:.2f} $")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
            
    def add_deposit(self):
        """إضافة إيداع نقدي جديد"""
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية لإضافة إيداعات نقدية")
            return
            
        dialog = حوار_حركة_نقدية("deposit")
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                amount = dialog.amount_spinbox.value()
                transaction_date = datetime.combine(dialog.date_edit.date().toPyDate(), datetime.now().time())
                reference = dialog.reference_edit.text().strip()
                description = dialog.description_text.toPlainText().strip()
                
                with session_scope() as session:
                    # الحصول على صندوق النقدية
                    cash_box = session.query(صندوق_النقدية).first()
                    if not cash_box:
                        cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                        session.add(cash_box)
                        session.flush()
                    
                    # حساب الرصيد الجديد
                    new_balance = cash_box.balance + amount
                    
                    # إضافة حركة جديدة
                    transaction = حركة_نقدية(
                        cash_box_id=cash_box.id,
                        transaction_type="deposit",
                        amount=amount,
                        balance_after=new_balance,
                        transaction_date=transaction_date,
                        description=description,
                        reference=reference,
                        created_by=self.user.id
                    )
                    session.add(transaction)
                    
                    # تحديث رصيد الصندوق
                    cash_box.balance = new_balance
                    cash_box.last_updated = datetime.now()
                    
                    session.commit()
                    
                    QMessageBox.information(self, "نجح", f"تم إضافة إيداع بقيمة {amount:.2f} $ بنجاح")
                    self.load_data()
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الإيداع: {str(e)}")
                
    def add_withdrawal(self):
        """إضافة سحب نقدي جديد"""
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية لإضافة مسحوبات نقدية")
            return
            
        dialog = حوار_حركة_نقدية("withdraw")
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                amount = dialog.amount_spinbox.value()
                transaction_date = datetime.combine(dialog.date_edit.date().toPyDate(), datetime.now().time())
                reference = dialog.reference_edit.text().strip()
                description = dialog.description_text.toPlainText().strip()
                
                with session_scope() as session:
                    # الحصول على صندوق النقدية
                    cash_box = session.query(صندوق_النقدية).first()
                    if not cash_box:
                        QMessageBox.warning(self, "تنبيه", "لا يوجد صندوق نقدية")
                        return
                    
                    # التحقق من توفر الرصيد
                    if cash_box.balance < amount:
                        if not confirm_dialog(self, "تأكيد", 
                                            f"الرصيد الحالي ({cash_box.balance:.2f} $) أقل من المبلغ المطلوب سحبه ({amount:.2f} $).\n"
                                            "هل تريد المتابعة؟"):
                            return
                    
                    # حساب الرصيد الجديد
                    new_balance = cash_box.balance - amount
                    
                    # إضافة حركة جديدة
                    transaction = حركة_نقدية(
                        cash_box_id=cash_box.id,
                        transaction_type="withdraw",
                        amount=amount,
                        balance_after=new_balance,
                        transaction_date=transaction_date,
                        description=description,
                        reference=reference,
                        created_by=self.user.id
                    )
                    session.add(transaction)
                    
                    # تحديث رصيد الصندوق
                    cash_box.balance = new_balance
                    cash_box.last_updated = datetime.now()
                    
                    session.commit()
                    
                    QMessageBox.information(self, "نجح", f"تم إضافة سحب بقيمة {amount:.2f} $ بنجاح")
                    self.load_data()
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة السحب: {str(e)}")
                
    def print_report(self):
        """طباعة تقرير صندوق النقدية"""
        QMessageBox.information(self, "معلومات", "ميزة الطباعة قيد التطوير")
