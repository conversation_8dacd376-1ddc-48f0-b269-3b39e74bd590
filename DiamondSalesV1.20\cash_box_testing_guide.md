# Cash Box (صندوق النقدية) Testing Guide

This guide will help you test the fixed cash box functionality in the Diamond Sales Management System, particularly focusing on the withdrawal vouchers display issue.

## 1. Testing Withdrawal Vouchers Integration

### 1.1 Initial Data Check
Run the diagnostic tool to check current status:
```bash
python test_cash_box_withdrawals.py
```

This will show:
- Number of direct withdrawal vouchers (سندات الصرف المباشرة) from Receipt table
- Number of withdrawals from purchase invoices (دفعات فواتير المشتريات)
- Check if any vouchers are missing corresponding cash transactions

### 1.2 Fixing Existing Data
If issues are found, run the fixing tool:
```bash
python fix_cash_vouchers.py
```

### 1.3 Verification
After running the fix, run the test tool again to verify all issues are resolved:
```bash
python test_cash_box_withdrawals.py
```

### 1.4 UI Testing
Open the fixed cash box screen to visually inspect the withdrawals:
```bash
python test_cash_box_withdrawals.py --ui
```

## 2. Testing New Voucher Creation

### 2.1 Create a New Withdrawal Voucher
1. Open the main application
2. Navigate to the vouchers screen (شاشة السندات)
3. Create a new withdrawal voucher (سند صرف)
4. Save the voucher

### 2.2 Verify in Cash Box
1. Navigate to the cash box screen (صندوق النقدية)
2. Verify the new withdrawal appears in the transactions list
3. Check that the cash box balance has been properly updated

## 3. Basic Interface Testing

### 3.1 Display Testing
1. Launch the application and navigate to the Cash Box screen
2. Verify that all currency values display with $ symbol
3. Check that the current balance, total deposits, and total withdrawals show the correct currency
4. Verify withdrawal transactions are highlighted in light red color

### 3.2 Transaction Filtering
1. Set date range
2. Test filtering by:
   - All transactions
   - Deposits only
   - Withdrawals only
3. Verify the totals update correctly for each filter

## 4. Report & Printing Testing

### 4.1 Generate Report
1. Apply filters if needed
2. Click "طباعة التقرير" (Print Report)
3. Verify the report includes all withdrawal vouchers:
   - Direct withdrawal vouchers
   - Purchase payment withdrawals
4. Check formatting and colors are correct

## 5. Regression Testing

### 5.1 Ensure Other Functionality Works
1. Test that deposit vouchers still work correctly
2. Test editing and deleting transactions
3. Verify balance calculation remains accurate

## Technical Notes

### Types of Withdrawal Transactions
The system now handles two types of withdrawal transactions:

1. **Direct Withdrawal Vouchers** 
   - Stored in `Receipt` table with `receipt_type = "CashOut"`
   - Reference format in cash transactions: `سند صرف #{receipt_id}`

2. **Purchase Payment Withdrawals**
   - From `Purchase` table where `paid_amount > 0`
   - Reference format in cash transactions: `فاتورة مشتريات #{purchase_id}`

### Common Issues and Solutions

1. **Withdrawals not appearing in cash box:**
   - Check if the `receipt_type` is correctly set to "CashOut"
   - Run `fix_cash_vouchers.py` to create missing cash transactions

2. **Balance mismatch:**
   - Run `fix_cash_vouchers.py` to recalculate all balances
   - Check for negative amounts that should be positive

3. **Database inconsistencies:**
   - Verify `cash_box_id` is properly set for all transactions
   - Check for duplicate references in cash transactions

## Expected Results

- All withdrawal vouchers should appear in the cash box transactions list
- Withdrawals should be highlighted in light red (deposit transactions in light green)
- The cash box balance should match the sum of all deposits minus withdrawals
- Reports should include all withdrawal transactions properly formatted

If any issues are found, please document them with screenshots and descriptions.
