from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel, QLineEdit,
                             QTableWidget, QTableWidgetItem, QPushButton, QHeaderView, QMessageBox,
                             QComboBox, QTextEdit, QDialog, QFormLayout, QDialogButtonBox, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QFont
from database import Category, Unit
from db_session import session_scope
from logger import log_error, log_info
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from ui_utils import center_window
from translations import get_translation as _

class CategoriesUnitsScreen(QWidget):
    """
    شاشة إدارة الأصناف والوحدات
    """
    # إشارة لتحديث البيانات في الشاشات الأخرى
    data_updated = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """
        تهيئة واجهة المستخدم
        """
        # تعيين عنوان النافذة وحجمها مثل شاشة التقارير
        self.setWindowTitle(_("categories_units_title", "نظام إدارة مبيعات الألماس - شاشة الأصناف والوحدات"))
        self.setGeometry(100, 100, 900, 600)
        self.setMinimumSize(900, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # تعيين الخط للواجهة بشكل عام
        font = QFont()
        font.setPointSize(10)  # حجم الخط مثل شاشة التقارير
        font.setBold(True)    # خط غامق
        self.setFont(font)

        # إنشاء تخطيط رئيسي
        main_layout = QVBoxLayout()

        # إنشاء علامات التبويب
        self.tabs = QTabWidget()
        self.tabs.setTabPosition(QTabWidget.TabPosition.North)
        self.tabs.setDocumentMode(True)  # مظهر تبويب أكثر عصرية

        # تنسيق التبويبات لتكون أكثر وضوحًا
        tab_font = QFont()
        tab_font.setPointSize(12)  # حجم الخط مثل شاشة التقارير
        tab_font.setBold(True)
        self.tabs.setFont(tab_font)

        # تنسيق خاص للتبويبات
        tab_style = """
            QTabWidget::pane {
                border-top: 2px solid #3498db;
                background: white;
            }
            QTabBar::tab {
                background: #f0f0f0;
                color: #333;
                min-width: 120px;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background: #3498db;
                color: white;
                font-weight: bold;
            }
            QTabBar::tab:hover:!selected {
                background: #d0d0d0;
            }
        """
        self.tabs.setStyleSheet(tab_style)
        self.tabs.setMinimumHeight(50)  # ارتفاع شريط التبويب مثل شاشة التقارير

        # إنشاء علامة تبويب الأصناف
        self.categories_tab = QWidget()
        self.init_categories_tab()
        self.tabs.addTab(self.categories_tab, "الأصناف")

        # إنشاء علامة تبويب الوحدات
        self.units_tab = QWidget()
        self.init_units_tab()
        self.tabs.addTab(self.units_tab, "الوحدات")

        # إضافة علامات التبويب إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)

        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)

        # تحميل البيانات
        self.load_categories()
        self.load_units()

        # توسيط النافذة في الشاشة
        center_window(self)

    def init_categories_tab(self):
        """
        تهيئة علامة تبويب الأصناف
        """
        # إنشاء تخطيط
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)  # هوامش للتخطيط
        layout.setSpacing(10)  # المسافة بين العناصر

        # إنشاء أزرار الإجراءات
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(20)  # زيادة المسافة بين الأزرار
        actions_layout.setContentsMargins(5, 5, 5, 15)  # هوامش لمجموعة الأزرار

        # إنشاء أزرار مع تحديد الحجم المناسب
        # تنسيق الأزرار على غرار الشاشة الرئيسية
        btn_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12pt;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """

        self.add_category_btn = QPushButton("إضافة صنف جديد")
        self.add_category_btn.clicked.connect(self.add_category)
        self.add_category_btn.setMinimumWidth(180)  # العرض الأدنى مثل شاشة التقارير
        self.add_category_btn.setMinimumHeight(45)  # ارتفاع الزر مثل شاشة التقارير
        self.add_category_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.add_category_btn.setStyleSheet(btn_style.replace("3498db", "27ae60")) # لون أخضر للإضافة

        self.edit_category_btn = QPushButton("تعديل الصنف")
        self.edit_category_btn.clicked.connect(self.edit_category)
        self.edit_category_btn.setMinimumWidth(150)  # العرض الأدنى مثل شاشة التقارير
        self.edit_category_btn.setMinimumHeight(45)  # ارتفاع الزر مثل شاشة التقارير
        self.edit_category_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.edit_category_btn.setStyleSheet(btn_style) # لون أزرق للتعديل

        self.delete_category_btn = QPushButton("حذف الصنف")
        self.delete_category_btn.clicked.connect(self.delete_category)
        self.delete_category_btn.setMinimumWidth(150)  # العرض الأدنى مثل شاشة التقارير
        self.delete_category_btn.setMinimumHeight(45)  # ارتفاع الزر مثل شاشة التقارير
        self.delete_category_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.delete_category_btn.setStyleSheet(btn_style.replace("3498db", "e74c3c")) # لون أحمر للحذف

        actions_layout.addWidget(self.add_category_btn)
        actions_layout.addWidget(self.edit_category_btn)
        actions_layout.addWidget(self.delete_category_btn)

        # إنشاء جدول الأصناف
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(3)
        self.categories_table.setHorizontalHeaderLabels(["الرقم", "اسم الصنف", "الوصف"])
        # تخصيص خط لرأس الجدول
        header_font = QFont()
        header_font.setPointSize(10)
        header_font.setBold(True)
        self.categories_table.horizontalHeader().setFont(header_font)

        # تعيين خط للجدول
        table_font = QFont()
        table_font.setPointSize(10)
        self.categories_table.setFont(table_font)

        # ضبط حجم الأعمدة مثل شاشة التقارير
        header = self.categories_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)  # جعل جميع الأعمدة تمتد

        # تلوين الصفوف بالتناوب
        self.categories_table.setAlternatingRowColors(True)
        self.categories_table.setStyleSheet("QTableWidget { gridline-color: #d0d0d0; alternate-background-color: #f5f5f5; }")

        # ضبط ارتفاع الصفوف
        self.categories_table.verticalHeader().setDefaultSectionSize(30)
        self.categories_table.verticalHeader().setVisible(True)  # إظهار أرقام الصفوف مثل شاشة التقارير

        # إضافة العناصر إلى التخطيط
        layout.addLayout(actions_layout)
        layout.addWidget(self.categories_table)

        # تعيين التخطيط
        self.categories_tab.setLayout(layout)

    def init_units_tab(self):
        """
        تهيئة علامة تبويب الوحدات
        """
        # إنشاء تخطيط
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)  # هوامش للتخطيط
        layout.setSpacing(10)  # المسافة بين العناصر

        # إنشاء أزرار الإجراءات
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(20)  # زيادة المسافة بين الأزرار
        actions_layout.setContentsMargins(5, 5, 5, 15)  # هوامش لمجموعة الأزرار

        # تنسيق الأزرار على غرار الشاشة الرئيسية
        btn_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12pt;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """

        self.add_unit_btn = QPushButton("إضافة وحدة جديدة")
        self.add_unit_btn.clicked.connect(self.add_unit)
        self.add_unit_btn.setMinimumWidth(180)  # العرض الأدنى مثل شاشة التقارير
        self.add_unit_btn.setMinimumHeight(45)  # ارتفاع الزر مثل شاشة التقارير
        self.add_unit_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.add_unit_btn.setStyleSheet(btn_style.replace("3498db", "27ae60")) # لون أخضر للإضافة

        self.edit_unit_btn = QPushButton("تعديل الوحدة")
        self.edit_unit_btn.clicked.connect(self.edit_unit)
        self.edit_unit_btn.setMinimumWidth(150)  # العرض الأدنى مثل شاشة التقارير
        self.edit_unit_btn.setMinimumHeight(45)  # ارتفاع الزر مثل شاشة التقارير
        self.edit_unit_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.edit_unit_btn.setStyleSheet(btn_style) # لون أزرق للتعديل

        self.delete_unit_btn = QPushButton("حذف الوحدة")
        self.delete_unit_btn.clicked.connect(self.delete_unit)
        self.delete_unit_btn.setMinimumWidth(150)  # العرض الأدنى مثل شاشة التقارير
        self.delete_unit_btn.setMinimumHeight(45)  # ارتفاع الزر مثل شاشة التقارير
        self.delete_unit_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.delete_unit_btn.setStyleSheet(btn_style.replace("3498db", "e74c3c")) # لون أحمر للحذف

        actions_layout.addWidget(self.add_unit_btn)
        actions_layout.addWidget(self.edit_unit_btn)
        actions_layout.addWidget(self.delete_unit_btn)

        # إنشاء جدول الوحدات
        self.units_table = QTableWidget()
        self.units_table.setColumnCount(5)
        self.units_table.setHorizontalHeaderLabels(["الرقم", "اسم الوحدة", "الرمز", "الصنف", "الوصف"])

        # تخصيص خط لرأس الجدول
        header_font = QFont()
        header_font.setPointSize(10)
        header_font.setBold(True)
        self.units_table.horizontalHeader().setFont(header_font)

        # تعيين خط للجدول
        table_font = QFont()
        table_font.setPointSize(10)
        self.units_table.setFont(table_font)

        # ضبط حجم الأعمدة مثل شاشة التقارير
        header = self.units_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)  # جعل جميع الأعمدة تمتد

        # تلوين الصفوف بالتناوب
        self.units_table.setAlternatingRowColors(True)
        self.units_table.setStyleSheet("QTableWidget { gridline-color: #d0d0d0; alternate-background-color: #f5f5f5; }")

        # ضبط ارتفاع الصفوف
        self.units_table.verticalHeader().setDefaultSectionSize(30)
        self.units_table.verticalHeader().setVisible(True)  # إظهار أرقام الصفوف مثل شاشة التقارير

        # إضافة العناصر إلى التخطيط
        layout.addLayout(actions_layout)
        layout.addWidget(self.units_table)

        # تعيين التخطيط
        self.units_tab.setLayout(layout)

    def load_categories(self):
        """
        تحميل الأصناف من قاعدة البيانات
        """
        try:
            with session_scope() as session:
                # الحصول على جميع الأصناف
                categories = session.query(Category).all()

                # تعيين عدد الصفوف
                self.categories_table.setRowCount(len(categories))

                # إضافة البيانات إلى الجدول
                for row, category in enumerate(categories):
                    self.categories_table.setItem(row, 0, QTableWidgetItem(str(category.id)))
                    self.categories_table.setItem(row, 1, QTableWidgetItem(category.name))
                    self.categories_table.setItem(row, 2, QTableWidgetItem(category.description or ""))

        except Exception as e:
            log_error("خطأ في تحميل الأصناف", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الأصناف: {str(e)}")

    def load_units(self):
        """
        تحميل الوحدات من قاعدة البيانات
        """
        try:
            with session_scope() as session:
                # الحصول على جميع الوحدات
                units = session.query(Unit).all()

                # تعيين عدد الصفوف
                self.units_table.setRowCount(len(units))

                # إضافة البيانات إلى الجدول
                for row, unit in enumerate(units):
                    self.units_table.setItem(row, 0, QTableWidgetItem(str(unit.id)))
                    self.units_table.setItem(row, 1, QTableWidgetItem(unit.name))
                    self.units_table.setItem(row, 2, QTableWidgetItem(unit.symbol or ""))

                    # عرض اسم الصنف إذا كان موجود
                    category_name = ""
                    if unit.category_id:
                        category = session.query(Category).filter_by(id=unit.category_id).first()
                        if category:
                            category_name = category.name
                    self.units_table.setItem(row, 3, QTableWidgetItem(category_name))

                    self.units_table.setItem(row, 4, QTableWidgetItem(unit.description or ""))

        except Exception as e:
            log_error("خطأ في تحميل الوحدات", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الوحدات: {str(e)}")

    def add_category(self):
        """
        إضافة صنف جديد
        """
        dialog = CategoryDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                with session_scope() as session:
                    # إنشاء صنف جديد
                    category = Category(
                        name=dialog.name_edit.text(),
                        description=dialog.description_edit.toPlainText()
                    )

                    # إضافة الصنف إلى قاعدة البيانات
                    session.add(category)

                # تحديث الجدول
                self.load_categories()

                # إرسال إشارة لتحديث البيانات في الشاشات الأخرى
                self.data_updated.emit()

                QMessageBox.information(self, "نجاح", "تمت إضافة الصنف بنجاح")

            except Exception as e:
                log_error("خطأ في إضافة صنف", e)
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الصنف: {str(e)}")

    def edit_category(self):
        """
        تعديل الصنف المحدد
        """
        # التحقق من تحديد صنف
        selected_rows = self.categories_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد صنف للتعديل")
            return

        # الحصول على معرف الصنف المحدد
        row = self.categories_table.currentRow()
        category_id = int(self.categories_table.item(row, 0).text())

        try:
            with session_scope() as session:
                # الحصول على الصنف من قاعدة البيانات
                category = session.query(Category).filter_by(id=category_id).first()

                if category:
                    # إنشاء مربع حوار التعديل
                    dialog = CategoryDialog(self, category)

                    if dialog.exec() == QDialog.DialogCode.Accepted:
                        # تحديث بيانات الصنف
                        category.name = dialog.name_edit.text()
                        category.description = dialog.description_edit.toPlainText()

                # تحديث الجدول بعد الانتهاء من session_scope
                self.load_categories()

                # إرسال إشارة لتحديث البيانات في الشاشات الأخرى
                self.data_updated.emit()

                if 'dialog' in locals() and dialog.result() == QDialog.DialogCode.Accepted:
                    QMessageBox.information(self, "نجاح", "تم تعديل الصنف بنجاح")
                elif not category:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على الصنف")

        except Exception as e:
            log_error("خطأ في تعديل صنف", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل الصنف: {str(e)}")

    def delete_category(self):
        """
        حذف الصنف المحدد
        """
        # التحقق من تحديد صنف
        selected_rows = self.categories_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد صنف للحذف")
            return

        # الحصول على معرف الصنف المحدد
        row = self.categories_table.currentRow()
        category_id = int(self.categories_table.item(row, 0).text())
        category_name = self.categories_table.item(row, 1).text()

        # تأكيد الحذف
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                    f"هل أنت متأكد من حذف الصنف '{category_name}'؟",
                                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                    QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            try:
                with session_scope() as session:
                    # التحقق من استخدام الصنف في المبيعات أو المشتريات
                    sales_count = session.query(Category).join(Category.sales).filter(Category.id == category_id).count()
                    purchases_count = session.query(Category).join(Category.purchases).filter(Category.id == category_id).count()

                    if sales_count > 0 or purchases_count > 0:
                        QMessageBox.warning(self, "تحذير",
                                        f"لا يمكن حذف الصنف '{category_name}' لأنه مستخدم في المبيعات أو المشتريات")
                        return

                    # حذف الصنف
                    category = session.query(Category).filter_by(id=category_id).first()
                    if category:
                        session.delete(category)
                        deleted = True
                    else:
                        deleted = False

                # تحديث الجدول
                self.load_categories()

                # إرسال إشارة لتحديث البيانات في الشاشات الأخرى
                self.data_updated.emit()

                if deleted:
                    QMessageBox.information(self, "نجاح", "تم حذف الصنف بنجاح")
                else:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على الصنف")

            except Exception as e:
                log_error("خطأ في حذف صنف", e)
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الصنف: {str(e)}")

    def add_unit(self):
        """
        إضافة وحدة جديدة
        """
        dialog = UnitDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                with session_scope() as session:
                    # الحصول على معرف الصنف المحدد
                    category_id = dialog.category_combo.currentData()

                    # إنشاء وحدة جديدة
                    unit = Unit(
                        name=dialog.name_edit.text(),
                        symbol=dialog.symbol_edit.text(),
                        description=dialog.description_edit.toPlainText(),
                        category_id=category_id
                    )

                    # إضافة الوحدة إلى قاعدة البيانات
                    session.add(unit)

                # تحديث الجدول
                self.load_units()

                # إرسال إشارة لتحديث البيانات في الشاشات الأخرى
                self.data_updated.emit()

                QMessageBox.information(self, "نجاح", "تمت إضافة الوحدة بنجاح")

            except Exception as e:
                log_error("خطأ في إضافة وحدة", e)
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الوحدة: {str(e)}")

    def edit_unit(self):
        """
        تعديل الوحدة المحددة
        """
        # التحقق من تحديد وحدة
        selected_rows = self.units_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد وحدة للتعديل")
            return

        # الحصول على معرف الوحدة المحددة
        row = self.units_table.currentRow()
        unit_id = int(self.units_table.item(row, 0).text())

        try:
            with session_scope() as session:
                # الحصول على الوحدة من قاعدة البيانات
                unit = session.query(Unit).filter_by(id=unit_id).first()

                if unit:
                    # إنشاء مربع حوار التعديل
                    dialog = UnitDialog(self, unit)

                    if dialog.exec() == QDialog.DialogCode.Accepted:
                        # الحصول على معرف الصنف المحدد
                        category_id = dialog.category_combo.currentData()

                        # تحديث بيانات الوحدة
                        unit.name = dialog.name_edit.text()
                        unit.symbol = dialog.symbol_edit.text()
                        unit.description = dialog.description_edit.toPlainText()
                        unit.category_id = category_id

                        edited = True
                    else:
                        edited = False
                else:
                    edited = False

            # تحديث الجدول
            self.load_units()

            # إرسال إشارة لتحديث البيانات في الشاشات الأخرى
            self.data_updated.emit()

            if edited:
                QMessageBox.information(self, "نجاح", "تم تعديل الوحدة بنجاح")
            elif not unit:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الوحدة")

        except Exception as e:
            log_error("خطأ في تعديل وحدة", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل الوحدة: {str(e)}")

    def delete_unit(self):
        """
        حذف الوحدة المحددة
        """
        # التحقق من تحديد وحدة
        selected_rows = self.units_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد وحدة للحذف")
            return

        # الحصول على معرف الوحدة المحددة
        row = self.units_table.currentRow()
        unit_id = int(self.units_table.item(row, 0).text())
        unit_name = self.units_table.item(row, 1).text()

        # تأكيد الحذف
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                    f"هل أنت متأكد من حذف الوحدة '{unit_name}'؟",
                                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                    QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            try:
                with session_scope() as session:
                    # التحقق من استخدام الوحدة في المبيعات أو المشتريات
                    sales_count = session.query(Unit).join(Unit.sales).filter(Unit.id == unit_id).count()
                    purchases_count = session.query(Unit).join(Unit.purchases).filter(Unit.id == unit_id).count()

                    if sales_count > 0 or purchases_count > 0:
                        QMessageBox.warning(self, "تحذير",
                                        f"لا يمكن حذف الوحدة '{unit_name}' لأنها مستخدمة في المبيعات أو المشتريات")
                        return

                    # حذف الوحدة
                    unit = session.query(Unit).filter_by(id=unit_id).first()
                    if unit:
                        session.delete(unit)
                        deleted = True
                    else:
                        deleted = False

                # تحديث الجدول
                self.load_units()

                # إرسال إشارة لتحديث البيانات في الشاشات الأخرى
                self.data_updated.emit()

                if deleted:
                    QMessageBox.information(self, "نجاح", "تم حذف الوحدة بنجاح")
                else:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على الوحدة")

            except Exception as e:
                log_error("خطأ في حذف وحدة", e)
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الوحدة: {str(e)}")

    def closeEvent(self, event):
        """
        معالجة حدث إغلاق النافذة
        """
        event.accept()

class CategoryDialog(QDialog):
    """
    مربع حوار إضافة/تعديل صنف
    """
    def __init__(self, parent=None, category=None):
        super().__init__(parent)
        self.category = category
        self.init_ui()
        # تعيين نمط مربع الحوار للتناسب مع النظام
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                color: #333;
            }
            QLineEdit, QTextEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                font-size: 11pt;
            }
            QLineEdit:focus, QTextEdit:focus {
                border: 1px solid #3498db;
            }
        """)

    def init_ui(self):
        """
        تهيئة واجهة المستخدم
        """
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة صنف جديد" if not self.category else "تعديل صنف")

        # إنشاء تخطيط
        layout = QFormLayout()

        # إنشاء حقول الإدخال
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم الصنف")
        self.name_edit.setMinimumHeight(28)  # زيادة ارتفاع الحقل
        font = QFont()
        font.setPointSize(10)
        self.name_edit.setFont(font)

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("أدخل وصف الصنف")
        self.description_edit.setFont(font)

        # إضافة الحقول إلى التخطيط
        layout.addRow("اسم الصنف:", self.name_edit)
        layout.addRow("الوصف:", self.description_edit)

        # إنشاء أزرار الإجراءات
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص الأزرار
        ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText(_("ok", "موافق"))
        ok_button.setMinimumWidth(120)
        ok_button.setMinimumHeight(40)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #219653;
            }
        """)

        cancel_button = buttons.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText(_("cancel", "إلغاء"))
        cancel_button.setMinimumWidth(120)
        cancel_button.setMinimumHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #f5543f;
            }
            QPushButton:pressed {
                background-color: #c0392b;
            }
        """)

        # إضافة الأزرار إلى التخطيط
        layout.addRow(buttons)

        # تعيين التخطيط
        self.setLayout(layout)

        # تعيين الأبعاد المناسبة لنافذة الحوار
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)

        # ملء البيانات إذا كان هناك صنف للتعديل
        if self.category:
            self.name_edit.setText(self.category.name)
            self.description_edit.setText(self.category.description or "")

    def accept(self):
        """
        التحقق من صحة البيانات قبل القبول
        """
        # التحقق من إدخال اسم الصنف
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال اسم الصنف")
            return

        super().accept()

class UnitDialog(QDialog):
    """
    مربع حوار إضافة/تعديل وحدة
    """
    def __init__(self, parent=None, unit=None):
        super().__init__(parent)
        self.unit = unit
        self.init_ui()
        # تعيين نمط مربع الحوار للتناسب مع النظام
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                color: #333;
            }
            QLineEdit, QTextEdit, QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                font-size: 11pt;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border: 1px solid #3498db;
            }
            QComboBox {
                min-height: 30px;
            }
        """)

    def init_ui(self):
        """
        تهيئة واجهة المستخدم
        """
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة وحدة جديدة" if not self.unit else "تعديل وحدة")

        # إنشاء تخطيط
        layout = QFormLayout()

        # إنشاء حقول الإدخال
        font = QFont()
        font.setPointSize(10)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم الوحدة")
        self.name_edit.setMinimumHeight(28)  # زيادة ارتفاع الحقل
        self.name_edit.setFont(font)

        self.symbol_edit = QLineEdit()
        self.symbol_edit.setPlaceholderText("أدخل رمز الوحدة")
        self.symbol_edit.setMinimumHeight(28)  # زيادة ارتفاع الحقل
        self.symbol_edit.setFont(font)

        # إنشاء قائمة منسدلة للأصناف
        self.category_combo = QComboBox()
        self.category_combo.addItem("-- اختر الصنف --", None)
        self.category_combo.setMinimumHeight(28)  # زيادة ارتفاع القائمة المنسدلة
        self.category_combo.setFont(font)
        self.load_categories()

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("أدخل وصف الوحدة")
        self.description_edit.setFont(font)

        # إضافة الحقول إلى التخطيط
        layout.addRow("اسم الوحدة:", self.name_edit)
        layout.addRow("الرمز:", self.symbol_edit)
        layout.addRow("الصنف:", self.category_combo)
        layout.addRow("الوصف:", self.description_edit)

        # إنشاء أزرار الإجراءات
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص الأزرار
        ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText(_("ok", "موافق"))
        ok_button.setMinimumWidth(120)
        ok_button.setMinimumHeight(40)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #219653;
            }
        """)

        cancel_button = buttons.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText(_("cancel", "إلغاء"))
        cancel_button.setMinimumWidth(120)
        cancel_button.setMinimumHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #f5543f;
            }
            QPushButton:pressed {
                background-color: #c0392b;
            }
        """)

        # إضافة الأزرار إلى التخطيط
        layout.addRow(buttons)

        # تعيين التخطيط
        self.setLayout(layout)

        # تعيين الأبعاد المناسبة لنافذة الحوار
        self.setMinimumWidth(450)
        self.setMinimumHeight(350)

        # ملء البيانات إذا كان هناك وحدة للتعديل
        if self.unit:
            self.name_edit.setText(self.unit.name)
            self.symbol_edit.setText(self.unit.symbol or "")
            self.description_edit.setText(self.unit.description or "")

            # تحديد الصنف إذا كان موجود
            if self.unit.category_id:
                index = self.category_combo.findData(self.unit.category_id)
                if index >= 0:
                    self.category_combo.setCurrentIndex(index)

    def load_categories(self):
        """
        تحميل الأصناف من قاعدة البيانات
        """
        try:
            with session_scope() as session:
                # الحصول على جميع الأصناف
                categories = session.query(Category).all()

                # إضافة الأصناف إلى القائمة المنسدلة
                for category in categories:
                    self.category_combo.addItem(category.name, category.id)

        except Exception as e:
            log_error(f"خطأ في تحميل الأصناف: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الأصناف: {str(e)}")

    def accept(self):
        """
        التحقق من صحة البيانات قبل القبول
        """
        # التحقق من إدخال اسم الوحدة
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال اسم الوحدة")
            return

        super().accept()



