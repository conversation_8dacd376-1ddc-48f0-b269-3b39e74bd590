"""
Script para verificar los datos en las tablas de la base de datos
"""

import sqlite3
import os

def check_table_data(table_name):
    """
    Verificar los datos en una tabla específica
    
    Args:
        table_name (str): Nombre de la tabla a verificar
    """
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect("diamond_sales.db")
        cursor = conn.cursor()
        
        # Obtener el número de registros en la tabla
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        print(f"Tabla '{table_name}': {count} registros")
        
        # Si hay registros, mostrar los primeros 5
        if count > 0:
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
            records = cursor.fetchall()
            
            # Obtener los nombres de las columnas
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [column[1] for column in cursor.fetchall()]
            
            print("\nColumnas:", columns)
            print("\nPrimeros registros:")
            for record in records:
                print(record)
        
        print("\n" + "-"*50 + "\n")
        
    except Exception as e:
        print(f"Error al verificar la tabla '{table_name}': {str(e)}")
    finally:
        if conn:
            conn.close()

def main():
    """Función principal"""
    print("Verificando datos en las tablas de la base de datos...\n")
    
    # Verificar las tablas principales
    tables = [
        "cash_box",
        "cash_transactions",
        "customers",
        "suppliers",
        "sales",
        "purchases",
        "receipts",
        "users",
        "settings"
    ]
    
    for table in tables:
        check_table_data(table)

if __name__ == "__main__":
    main()
