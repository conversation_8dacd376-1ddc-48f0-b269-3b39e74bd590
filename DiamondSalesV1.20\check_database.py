import sqlite3

# Conectar a la base de datos
conn = sqlite3.connect('diamond_sales.db')
cursor = conn.cursor()

# Obtener la lista de tablas
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()

print("=== حالة قاعدة البيانات ===")
for table in tables:
    table_name = table[0]
    if not table_name.startswith('sqlite_'):
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"{table_name}: {count} سجل")

# Cerrar la conexión
conn.close()
