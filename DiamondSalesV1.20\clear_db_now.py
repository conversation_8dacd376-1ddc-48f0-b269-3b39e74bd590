#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime
import shutil

def clear_database_now():
    print("=" * 50)
    print("تفريغ قاعدة البيانات")
    print("=" * 50)
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists('diamond_sales.db'):
        print("❌ لا توجد قاعدة بيانات للتفريغ")
        return False
    
    # إنشاء نسخة احتياطية
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'diamond_sales_backup_before_clear_{timestamp}.db'
        shutil.copy2('diamond_sales.db', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return False
    
    # تفريغ قاعدة البيانات
    try:
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        print("🔄 بدء تفريغ قاعدة البيانات...")
        
        # إيقاف قيود المفاتيح الخارجية مؤقتاً
        cursor.execute('PRAGMA foreign_keys = OFF')
        
        # قائمة الجداول المراد تفريغها
        tables_to_clear = [
            'customers', 'suppliers', 'sales', 'purchases',
            'sale_items', 'purchase_items', 'receipts',
            'payments', 'vouchers', 'cash_transactions',
            'journal_entries', 'data_locks'
        ]
        
        cleared_count = 0
        total_rows_deleted = 0
        
        for table in tables_to_clear:
            try:
                # التحقق من وجود الجدول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if cursor.fetchone():
                    # حذف البيانات من الجدول
                    cursor.execute(f'DELETE FROM {table}')
                    rows_deleted = cursor.rowcount
                    print(f"  ✅ تم تفريغ جدول {table}: {rows_deleted} صف")
                    cleared_count += 1
                    total_rows_deleted += rows_deleted
                else:
                    print(f"  ⚠️ الجدول {table} غير موجود")
            except Exception as e:
                print(f"  ❌ خطأ في تفريغ جدول {table}: {e}")
        
        # إعادة تعيين معرفات التسلسل التلقائي
        print("🔄 إعادة تعيين معرفات التسلسل التلقائي...")
        try:
            cursor.execute('DELETE FROM sqlite_sequence WHERE name IN ({})'.format(
                ','.join(['?' for _ in tables_to_clear])
            ), tables_to_clear)
            print("  ✅ تم إعادة تعيين معرفات التسلسل التلقائي")
        except Exception as e:
            print(f"  ⚠️ تحذير في إعادة تعيين التسلسل: {e}")
        
        # إعادة تفعيل قيود المفاتيح الخارجية
        cursor.execute('PRAGMA foreign_keys = ON')
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ تم تفريغ قاعدة البيانات بنجاح!")
        print(f"📊 تم تفريغ {cleared_count} جدول")
        print(f"🗑️ تم حذف {total_rows_deleted} صف إجمالي")
        print(f"💾 النسخة الاحتياطية: {backup_name}")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تفريغ قاعدة البيانات: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == '__main__':
    success = clear_database_now()
    if success:
        print("\n🎉 العملية مكتملة بنجاح!")
        input("اضغط Enter للخروج...")
    else:
        print("\n❌ فشلت العملية!")
        input("اضغط Enter للخروج...")