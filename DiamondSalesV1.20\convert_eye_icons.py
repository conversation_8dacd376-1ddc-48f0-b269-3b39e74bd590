#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت لإنشاء أيقونات العين المفتوحة والمغلقة بتنسيق PNG
"""

from PyQt6.QtGui import QPainter, QColor, QPen, QBrush, QPixmap
from PyQt6.QtCore import Qt, QRect, QPoint
import os

def create_eye_open_icon():
    """إنشاء أيقونة العين المفتوحة"""
    pixmap = QPixmap(24, 24)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم العين
    pen = QPen(QColor(0, 0, 0))
    pen.setWidth(2)
    painter.setPen(pen)
    
    # رسم الشكل الخارجي للعين
    painter.drawEllipse(QRect(2, 7, 20, 10))
    
    # رسم بؤبؤ العين
    painter.setBrush(QBrush(QColor(0, 0, 0)))
    painter.drawEllipse(QRect(9, 9, 6, 6))
    
    painter.end()
    
    # حفظ الأيقونة
    if not os.path.exists('assets'):
        os.makedirs('assets')
    pixmap.save('assets/eye_open.png')
    
def create_eye_closed_icon():
    """إنشاء أيقونة العين المغلقة"""
    pixmap = QPixmap(24, 24)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم العين
    pen = QPen(QColor(0, 0, 0))
    pen.setWidth(2)
    painter.setPen(pen)
    
    # رسم الشكل الخارجي للعين
    painter.drawArc(QRect(2, 7, 20, 10), 0, 180 * 16)
    
    # رسم خط مائل فوق العين
    painter.drawLine(QPoint(3, 3), QPoint(21, 21))
    
    painter.end()
    
    # حفظ الأيقونة
    if not os.path.exists('assets'):
        os.makedirs('assets')
    pixmap.save('assets/eye_closed.png')

if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    create_eye_open_icon()
    create_eye_closed_icon()
    print("تم إنشاء الأيقونات بنجاح")
