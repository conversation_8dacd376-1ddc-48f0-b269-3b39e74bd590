import os
import cairosvg
import sys

def convert_svg_to_png(svg_path, png_path, size=64):
    """Convert SVG file to PNG with specified size."""
    try:
        cairosvg.svg2png(url=svg_path, write_to=png_path, output_width=size, output_height=size)
        print(f"Converted {svg_path} to {png_path}")
    except Exception as e:
        print(f"Error converting {svg_path}: {e}")

def main():
    # Check if cairosvg is installed
    try:
        import cairosvg
    except ImportError:
        print("cairosvg is not installed. Installing...")
        os.system("pip install cairosvg")
        try:
            import cairosvg
        except ImportError:
            print("Failed to install cairosvg. Please install it manually with 'pip install cairosvg'")
            return

    # Create assets directory if it doesn't exist
    if not os.path.exists("assets"):
        os.makedirs("assets")

    # Get all SVG files in assets directory
    svg_files = [f for f in os.listdir("assets") if f.endswith(".svg")]

    if not svg_files:
        print("No SVG files found in assets directory.")
        return

    # Convert each SVG file to PNG
    for svg_file in svg_files:
        svg_path = os.path.join("assets", svg_file)
        png_path = os.path.join("assets", svg_file.replace(".svg", ".png"))
        
        # Convert to different sizes
        convert_svg_to_png(svg_path, png_path, size=64)
        
        # Also create a larger version for the main icon
        if svg_file == "diamond_icon.svg":
            large_png_path = os.path.join("assets", "diamond_icon_large.png")
            convert_svg_to_png(svg_path, large_png_path, size=256)

    print("Conversion complete!")

if __name__ == "__main__":
    main()
