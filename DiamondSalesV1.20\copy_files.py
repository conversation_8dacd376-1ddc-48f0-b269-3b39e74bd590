# -*- coding: utf-8 -*-
import os
import shutil

def copy_files():
    print("📁 نسخ الملفات الإضافية...")
    
    # إنشاء مجلد التوزيع
    dist_path = "dist/DiamondSales"
    os.makedirs(dist_path, exist_ok=True)
    print("  ✅ تم إنشاء مجلد التوزيع")
    
    # نسخ الملف التنفيذي
    if os.path.exists("dist/DiamondSales.exe"):
        shutil.copy2("dist/DiamondSales.exe", os.path.join(dist_path, "DiamondSales.exe"))
        print("  ✅ تم نسخ الملف التنفيذي")
    
    # نسخ قاعدة البيانات
    if os.path.exists("diamond_sales.db"):
        shutil.copy2("diamond_sales.db", os.path.join(dist_path, "diamond_sales.db"))
        print("  ✅ تم نسخ قاعدة البيانات")
    
    # نسخ مجلد الأصول
    if os.path.exists("assets"):
        assets_dest = os.path.join(dist_path, "assets")
        if os.path.exists(assets_dest):
            shutil.rmtree(assets_dest)
        shutil.copytree("assets", assets_dest)
        print("  ✅ تم نسخ مجلد الأصول")
    
    # نسخ مجلد الترجمات
    if os.path.exists("translations"):
        translations_dest = os.path.join(dist_path, "translations")
        if os.path.exists(translations_dest):
            shutil.rmtree(translations_dest)
        shutil.copytree("translations", translations_dest)
        print("  ✅ تم نسخ مجلد الترجمات")
    
    # إنشاء مجلد logs
    logs_dest = os.path.join(dist_path, "logs")
    os.makedirs(logs_dest, exist_ok=True)
    print("  ✅ تم إنشاء مجلد logs")
    
    print("🎉 تم نسخ جميع الملفات بنجاح!")

if __name__ == "__main__":
    copy_files()