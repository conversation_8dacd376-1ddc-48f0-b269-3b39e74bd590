#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diamond Sales V1.30 - Create Individual Arabic Installers
إنشاء برامج تثبيت منفصلة بدعم اللغة العربية لكل برنامج
"""

import os
import shutil
import subprocess
import datetime
from pathlib import Path

def print_status(message, status="INFO"):
    """طباعة رسالة حالة بتنسيق منظم"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def create_directory_if_not_exists(path):
    """إنشاء مجلد إذا لم يكن موجوداً"""
    Path(path).mkdir(parents=True, exist_ok=True)

def create_diamond_sales_installer():
    """إنشاء برنامج تثبيت البرنامج الرئيسي"""
    print("\n" + "="*60)
    print("💎 إنشاء برنامج تثبيت البرنامج الرئيسي")
    print("="*60)
    
    installer_content = """#define MyAppName "نظام إدارة مبيعات الألماس"
#define MyAppNameEN "Diamond Sales"
#define MyAppVersion "1.30"
#define MyAppPublisher "Diamond Sales System"
#define MyAppURL "https://www.example.com/"
#define MyAppExeName "Diamond Sales.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
AppId={{A1B2C3D4-E5F6-4A5B-9C8D-7E6F5A4B3C2D}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\\{#MyAppNameEN}
DefaultGroupName={#MyAppName}
AllowNoIcons=yes
; تثبيت البرنامج بصلاحيات المسؤول ولكن تشغيله بصلاحيات المستخدم العادي
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog
OutputDir=installer_output
OutputBaseFilename=DiamondSalesMainProgram_v1.30_Arabic_Setup
SetupIconFile=icons\\diamond_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
; دعم اللغة العربية
WizardResizable=yes
DirExistsWarning=no
; إضافة معلومات النسخة
VersionInfoVersion={#MyAppVersion}
VersionInfoCompany={#MyAppPublisher}
VersionInfoDescription={#MyAppName}
VersionInfoCopyright=Copyright © 2025 {#MyAppPublisher}
; إعدادات إضافية لدعم اللغة العربية
AlwaysShowComponentsList=no
AlwaysShowDirOnReadyPage=yes

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl,custom_arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "إنشاء اختصار على سطح المكتب"; GroupDescription: "اختصارات:"
Name: "quicklaunchicon"; Description: "إنشاء اختصار في شريط المهام"; GroupDescription: "اختصارات:"; OnlyBelowVersion: 6.1

[Files]
Source: "installer_package\\DiamondSales\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "installer_package\\README.txt"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"
Name: "{group}\\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{commondesktop}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"; Tasks: desktopicon

[UninstallDelete]
Type: filesandordirs; Name: "{app}\\logs"
Type: filesandordirs; Name: "{app}\\backups"
Type: files; Name: "{app}\\diamond_sales.db"

[Run]
Filename: "{app}\\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#MyAppName}}"; Flags: nowait postinstall skipifsilent
"""
    
    with open("diamond_sales_main_installer_arabic.iss", "w", encoding="utf-8") as f:
        f.write(installer_content)
        
    print_status("تم إنشاء ملف installer: diamond_sales_main_installer_arabic.iss", "SUCCESS")


def create_activation_generator_installer():
    """إنشاء برنامج تثبيت مولد أكواد التفعيل"""
    print("\n" + "="*60)
    print("🔑 إنشاء برنامج تثبيت مولد أكواد التفعيل")
    print("="*60)
    
    installer_content = """#define MyAppName "مولد أكواد التفعيل - نظام مبيعات الألماس"
#define MyAppNameEN "Diamond Sales - Activation Generator"
#define MyAppVersion "1.30"
#define MyAppPublisher "Diamond Sales System"
#define MyAppURL "https://www.example.com/"
#define MyAppExeName "ActivationGenerator.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
AppId={{B2C3D4E5-F6A5-B9C8-D7E6-F5A4B3C2D1E0}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\\Diamond Sales\\Activation Generator
DefaultGroupName=نظام مبيعات الألماس\\أدوات
AllowNoIcons=yes
; تثبيت البرنامج بصلاحيات المسؤول ولكن تشغيله بصلاحيات المستخدم العادي
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog
OutputDir=installer_output
OutputBaseFilename=DiamondSalesActivationGenerator_v1.30_Arabic_Setup
SetupIconFile=icons\\key_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
; دعم اللغة العربية
WizardResizable=yes
DirExistsWarning=no
; إضافة معلومات النسخة
VersionInfoVersion={#MyAppVersion}
VersionInfoCompany={#MyAppPublisher}
VersionInfoDescription={#MyAppName}
VersionInfoCopyright=Copyright © 2025 {#MyAppPublisher}
; إعدادات إضافية لدعم اللغة العربية
AlwaysShowComponentsList=no
AlwaysShowDirOnReadyPage=yes

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl,custom_arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "إنشاء اختصار على سطح المكتب"; GroupDescription: "اختصارات:"
Name: "quicklaunchicon"; Description: "إنشاء اختصار في شريط المهام"; GroupDescription: "اختصارات:"; OnlyBelowVersion: 6.1

[Files]
Source: "installer_package\\ActivationGenerator\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "installer_package\\README.txt"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"
Name: "{group}\\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{commondesktop}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"; Tasks: desktopicon

[UninstallDelete]
Type: filesandordirs; Name: "{app}\\logs"
"""
    
    with open("activation_generator_installer_arabic.iss", "w", encoding="utf-8") as f:
        f.write(installer_content)
        
    print_status("تم إنشاء ملف installer: activation_generator_installer_arabic.iss", "SUCCESS")


def create_reset_password_installer():
    """إنشاء برنامج تثبيت أداة إعادة تعيين كلمة المرور"""
    print("\n" + "="*60)
    print("🔓 إنشاء برنامج تثبيت أداة إعادة تعيين كلمة المرور")
    print("="*60)
    
    installer_content = """#define MyAppName "أداة إعادة تعيين كلمة المرور - نظام مبيعات الألماس"
#define MyAppNameEN "Diamond Sales - Password Reset Tool"
#define MyAppVersion "1.30"
#define MyAppPublisher "Diamond Sales System"
#define MyAppURL "https://www.example.com/"
#define MyAppExeName "reset_password.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
AppId={{C3D4E5F6-A5B9-C8D7-E6F5-A4B3C2D1E0F9}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\\Diamond Sales\\Password Reset Tool
DefaultGroupName=نظام مبيعات الألماس\\أدوات
AllowNoIcons=yes
; تثبيت البرنامج بصلاحيات المسؤول ولكن تشغيله بصلاحيات المستخدم العادي
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog
OutputDir=installer_output
OutputBaseFilename=DiamondSalesPasswordReset_v1.30_Arabic_Setup
SetupIconFile=icons\\key_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
; دعم اللغة العربية
WizardResizable=yes
DirExistsWarning=no
; إضافة معلومات النسخة
VersionInfoVersion={#MyAppVersion}
VersionInfoCompany={#MyAppPublisher}
VersionInfoDescription={#MyAppName}
VersionInfoCopyright=Copyright © 2025 {#MyAppPublisher}
; إعدادات إضافية لدعم اللغة العربية
AlwaysShowComponentsList=no
AlwaysShowDirOnReadyPage=yes

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl,custom_arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "إنشاء اختصار على سطح المكتب"; GroupDescription: "اختصارات:"
Name: "quicklaunchicon"; Description: "إنشاء اختصار في شريط المهام"; GroupDescription: "اختصارات:"; OnlyBelowVersion: 6.1

[Files]
Source: "installer_package\\reset_password.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "installer_package\\README.txt"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"
Name: "{group}\\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{commondesktop}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"; Tasks: desktopicon
"""
    
    with open("reset_password_installer_arabic.iss", "w", encoding="utf-8") as f:
        f.write(installer_content)
        
    print_status("تم إنشاء ملف installer: reset_password_installer_arabic.iss", "SUCCESS")


def build_installers():
    """بناء برامج التثبيت"""
    print("\n" + "="*60)
    print("🏗️ بناء برامج التثبيت")
    print("="*60)
    
    # البحث عن Inno Setup Compiler
    inno_setup_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe",
    ]
    
    iscc_path = None
    for path in inno_setup_paths:
        if os.path.exists(path):
            iscc_path = path
            print_status(f"تم العثور على Inno Setup في: {iscc_path}", "SUCCESS")
            break
            
    if iscc_path is None:
        print_status("لم يتم العثور على Inno Setup Compiler. يرجى تثبيته وإعادة المحاولة.", "ERROR")
        return False
    
    # قائمة الملفات التي سيتم بناؤها
    iss_files = [
        "diamond_sales_main_installer_arabic.iss",
        "activation_generator_installer_arabic.iss",
        "reset_password_installer_arabic.iss",
    ]
    
    successful_builds = []
    
    for iss_file in iss_files:
        if os.path.exists(iss_file):
            print_status(f"بناء: {iss_file}")
            
            cmd = f'"{iscc_path}" "{iss_file}"'
            try:
                process = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if process.returncode == 0:
                    print_status(f"تم بناء {iss_file} بنجاح", "SUCCESS")
                    successful_builds.append(iss_file)
                else:
                    print_status(f"فشل في بناء {iss_file}", "ERROR")
                    print(f"الخطأ: {process.stderr}")
            except Exception as e:
                print_status(f"حدث استثناء أثناء بناء {iss_file}: {str(e)}", "ERROR")
        else:
            print_status(f"ملف {iss_file} غير موجود!", "ERROR")
    
    return successful_builds


def get_installer_sizes():
    """الحصول على أحجام ملفات برامج التثبيت"""
    installers = {
        "DiamondSalesMainProgram_v1.30_Arabic_Setup.exe": "البرنامج الرئيسي",
        "DiamondSalesActivationGenerator_v1.30_Arabic_Setup.exe": "مولد أكواد التفعيل",
        "DiamondSalesPasswordReset_v1.30_Arabic_Setup.exe": "أداة إعادة تعيين كلمة المرور",
    }
    
    result = {}
    total_size = 0
    
    for filename, description in installers.items():
        path = os.path.join("installer_output", filename)
        if os.path.exists(path):
            size_mb = os.path.getsize(path) / (1024 * 1024)
            total_size += size_mb
            result[filename] = (description, size_mb)
    
    return result, total_size


def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء برامج تثبيت منفصلة بدعم اللغة العربية لبرامج مبيعات الألماس")
    print("="*70)
    
    # التأكد من وجود مجلد المخرجات
    create_directory_if_not_exists("installer_output")
    
    # إنشاء ملفات التثبيت
    create_diamond_sales_installer()
    create_activation_generator_installer()
    create_reset_password_installer()
    
    # طباعة قائمة الملفات المنشأة
    print("\n" + "="*60)
    print("📄 ملفات .iss المُنشأة:")
    print("="*60)
    for iss_file in ["diamond_sales_main_installer_arabic.iss", "activation_generator_installer_arabic.iss", "reset_password_installer_arabic.iss"]:
        if os.path.exists(iss_file):
            print_status(f"✅ {iss_file}")
        else:
            print_status(f"❌ {iss_file}")
    
    # بناء برامج التثبيت
    successful_builds = build_installers()
    
    # طباعة النتائج
    print("\n" + "="*60)
    print("🎉 نتائج إنشاء برامج التثبيت العربية")
    print("="*60)
    
    print("\n📊 الإحصائيات:")
    print(f"   - ملفات .iss مُنشأة: {len(['diamond_sales_main_installer_arabic.iss', 'activation_generator_installer_arabic.iss', 'reset_password_installer_arabic.iss'])}")
    print(f"   - برامج تثبيت مبنية: {len(successful_builds)}")
    
    if len(successful_builds) == 3:
        print_status("تم بناء جميع برامج التثبيت بنجاح! 🎉", "SUCCESS")
    else:
        print_status(f"تم بناء {len(successful_builds)} من أصل 3 برامج تثبيت.", "WARNING")
    
    # طباعة معلومات الحجم
    installer_sizes, total_size = get_installer_sizes()
    
    if installer_sizes:
        print("\n📦 برامج التثبيت الجاهزة:")
        for filename, (description, size_mb) in installer_sizes.items():
            print(f"   ✅ {filename} ({size_mb:.1f} MB)")
        
        print(f"\n📈 الحجم الإجمالي: {total_size:.1f} MB")
        
        # توصيات التوزيع
        print("\n🎯 توصيات التوزيع:")
        print("   📦 DiamondSalesMainProgram_v1.30_Arabic_Setup.exe - للعملاء العاديين")
        print("   🔑 DiamondSalesActivationGenerator_v1.30_Arabic_Setup.exe - للموزعين فقط")
        print("   🔓 DiamondSalesPasswordReset_v1.30_Arabic_Setup.exe - لفرق الدعم الفني")
    else:
        print_status("لم يتم العثور على أي برنامج تثبيت في مجلد المخرجات.", "ERROR")
    
    # تحديث ملف README.txt
    with open("installer_package/README.txt", "a", encoding="utf-8") as f:
        f.write(f"\nتم إنشاء برامج تثبيت منفصلة بتاريخ {datetime.datetime.now().strftime('%d-%m-%Y')}\n")
        f.write("البرامج المنفصلة تدعم اللغة العربية بالكامل.\n")


if __name__ == "__main__":
    main()
