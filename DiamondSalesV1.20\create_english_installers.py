#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diamond Sales V1.30 - Create Individual English Installers
Create separate installers for each program with English interface
"""

import os
import subprocess
import datetime
from pathlib import Path

def print_status(message, status="INFO"):
    """Print a formatted status message"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def create_directory_if_not_exists(path):
    """Create a directory if it doesn't exist"""
    Path(path).mkdir(parents=True, exist_ok=True)

def build_installers():
    """Build the installer packages"""
    print("\n" + "="*60)
    print("🏗️ Building English Installers")
    print("="*60)
    
    # Find Inno Setup Compiler
    inno_setup_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe",
    ]
    
    iscc_path = None
    for path in inno_setup_paths:
        if os.path.exists(path):
            iscc_path = path
            print_status(f"Found Inno Setup at: {iscc_path}", "SUCCESS")
            break
            
    if iscc_path is None:
        print_status("Inno Setup Compiler not found. Please install it and try again.", "ERROR")
        return False
    
    # List of installer files to build
    iss_files = [
        "diamond_sales_main_installer_english.iss",
        "activation_generator_installer_english.iss",
        "reset_password_installer_english.iss",
    ]
    
    successful_builds = []
    
    for iss_file in iss_files:
        if os.path.exists(iss_file):
            print_status(f"Building: {iss_file}")
            
            cmd = f'"{iscc_path}" "{iss_file}"'
            try:
                process = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if process.returncode == 0:
                    print_status(f"Successfully built {iss_file}", "SUCCESS")
                    successful_builds.append(iss_file)
                else:
                    print_status(f"Failed to build {iss_file}", "ERROR")
                    print(f"Error: {process.stderr}")
            except Exception as e:
                print_status(f"Exception while building {iss_file}: {str(e)}", "ERROR")
        else:
            print_status(f"File {iss_file} not found!", "ERROR")
    
    return successful_builds

def get_installer_sizes():
    """Get the sizes of the installer files"""
    installers = {
        "DiamondSalesMainProgram_v1.30_English_Setup.exe": "Main Program",
        "DiamondSalesActivationGenerator_v1.30_English_Setup.exe": "Activation Generator",
        "DiamondSalesPasswordReset_v1.30_English_Setup.exe": "Password Reset Tool",
    }
    
    result = {}
    total_size = 0
    
    for filename, description in installers.items():
        path = os.path.join("installer_output", filename)
        if os.path.exists(path):
            size_mb = os.path.getsize(path) / (1024 * 1024)
            total_size += size_mb
            result[filename] = (description, size_mb)
    
    return result, total_size

def main():
    """Main function"""
    print("🚀 Creating Separate English Installers for Diamond Sales Programs")
    print("="*70)
    
    # Make sure output directory exists
    create_directory_if_not_exists("installer_output")
    
    # List the created ISS files
    print("\n" + "="*60)
    print("📄 ISS Files Created:")
    print("="*60)
    for iss_file in ["diamond_sales_main_installer_english.iss", "activation_generator_installer_english.iss", "reset_password_installer_english.iss"]:
        if os.path.exists(iss_file):
            print_status(f"✅ {iss_file}")
        else:
            print_status(f"❌ {iss_file}")
    
    # Build the installers
    successful_builds = build_installers()
    
    # Print results
    print("\n" + "="*60)
    print("🎉 English Installer Results")
    print("="*60)
    
    print("\n📊 Statistics:")
    print(f"   - ISS files created: {len(['diamond_sales_main_installer_english.iss', 'activation_generator_installer_english.iss', 'reset_password_installer_english.iss'])}")
    print(f"   - Installers built: {len(successful_builds)}")
    
    if len(successful_builds) == 3:
        print_status("All installers were built successfully! 🎉", "SUCCESS")
    else:
        print_status(f"Built {len(successful_builds)} out of 3 installers.", "WARNING")
    
    # Print size information
    installer_sizes, total_size = get_installer_sizes()
    
    if installer_sizes:
        print("\n📦 Available Installers:")
        for filename, (description, size_mb) in installer_sizes.items():
            print(f"   ✅ {filename} ({size_mb:.1f} MB)")
        
        print(f"\n📈 Total Size: {total_size:.1f} MB")
        
        # Distribution recommendations
        print("\n🎯 Distribution Recommendations:")
        print("   📦 DiamondSalesMainProgram_v1.30_English_Setup.exe - For regular customers")
        print("   🔑 DiamondSalesActivationGenerator_v1.30_English_Setup.exe - For distributors only")
        print("   🔓 DiamondSalesPasswordReset_v1.30_English_Setup.exe - For support teams")
    else:
        print_status("No installer files found in the output directory.", "ERROR")

if __name__ == "__main__":
    main()
