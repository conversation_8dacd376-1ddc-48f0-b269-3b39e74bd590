#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para crear iconos simples en formato ICO
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_diamond_icon(output_path, sizes=[16, 32, 48, 64, 128, 256]):
    """
    Crea un icono simple de diamante

    Args:
        output_path (str): <PERSON>uta donde guardar el archivo ICO
        sizes (list): Lista de tamaños para el icono
    """
    print(f"Creating diamond icon in {output_path}...")

    images = []
    for size in sizes:
        # Crear una imagen con fondo transparente
        img = Image.new('RGBA', (size, size), color=(0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Dibujar un diamante (rombo)
        margin = size // 8
        points = [
            (size // 2, margin),  # Arriba
            (size - margin, size // 2),  # Derecha
            (size // 2, size - margin),  # Abajo
            (margin, size // 2)  # Izquierda
        ]

        # Dibujar el diamante con un color azul
        draw.polygon(points, fill=(0, 100, 255, 255))

        # Añadir un brillo
        highlight_points = [
            (size // 2, margin + size // 10),
            (size // 2 + size // 6, size // 2 - size // 10),
            (size // 2, size // 2),
            (size // 2 - size // 6, size // 2 - size // 10)
        ]
        draw.polygon(highlight_points, fill=(100, 200, 255, 200))

        images.append(img)

    # Guardar como ICO
    images[0].save(
        output_path,
        format='ICO',
        sizes=[(img.width, img.height) for img in images],
        append_images=images[1:]
    )

    print(f"Icon saved in {output_path}")

def create_key_icon(output_path, sizes=[16, 32, 48, 64, 128, 256]):
    """
    Crea un icono simple de llave

    Args:
        output_path (str): Ruta donde guardar el archivo ICO
        sizes (list): Lista de tamaños para el icono
    """
    print(f"Creating key icon in {output_path}...")

    images = []
    for size in sizes:
        # Crear una imagen con fondo transparente
        img = Image.new('RGBA', (size, size), color=(0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Dibujar una llave simple
        # Círculo para la cabeza de la llave
        circle_radius = size // 4
        circle_center = (size // 3, size // 3)
        draw.ellipse(
            (
                circle_center[0] - circle_radius,
                circle_center[1] - circle_radius,
                circle_center[0] + circle_radius,
                circle_center[1] + circle_radius
            ),
            fill=(255, 200, 0, 255)
        )

        # Dibujar el cuerpo de la llave
        key_width = size // 12
        draw.rectangle(
            (
                circle_center[0] - key_width // 2,
                circle_center[1],
                circle_center[0] + key_width // 2,
                size - size // 4
            ),
            fill=(255, 200, 0, 255)
        )

        # Dibujar los dientes de la llave
        tooth_width = size // 6
        tooth_height = key_width

        # Primer diente
        draw.rectangle(
            (
                circle_center[0] + key_width // 2,
                size - size // 4 - tooth_height,
                circle_center[0] + key_width // 2 + tooth_width,
                size - size // 4
            ),
            fill=(255, 200, 0, 255)
        )

        # Segundo diente
        draw.rectangle(
            (
                circle_center[0] + key_width // 2,
                size - size // 4 - tooth_height * 2,
                circle_center[0] + key_width // 2 + tooth_width // 2,
                size - size // 4 - tooth_height
            ),
            fill=(255, 200, 0, 255)
        )

        images.append(img)

    # Guardar como ICO
    images[0].save(
        output_path,
        format='ICO',
        sizes=[(img.width, img.height) for img in images],
        append_images=images[1:]
    )

    print(f"Icon saved in {output_path}")

def main():
    # Crear carpeta para los iconos si no existe
    if not os.path.exists('icons'):
        os.makedirs('icons')

    # Crear iconos
    create_diamond_icon('icons/diamond_icon.ico')
    create_key_icon('icons/key_icon.ico')

    print("Icons creation completed.")

if __name__ == "__main__":
    main()
