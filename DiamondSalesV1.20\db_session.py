"""
مدير جلسات قاعدة البيانات
"""

from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from logger import log_error, log_info
import traceback
import threading
import time
import os

# تكوين محرك قاعدة البيانات مع خيارات متقدمة
engine = create_engine(
    'sqlite:///diamond_sales.db',
    connect_args={'timeout': 30},  # زيادة مهلة الاتصال
    pool_size=10,  # حجم تجمع الاتصالات
    max_overflow=20,  # الحد الأقصى للاتصالات الإضافية
    pool_timeout=30,  # مهلة انتظار اتصال متاح
    pool_recycle=3600  # إعادة تدوير الاتصالات كل ساعة
)

# إنشاء مصنع الجلسات
SessionFactory = sessionmaker(bind=engine)

# إنشاء جلسة مرتبطة بالسلسلة الحالية (thread-local)
ScopedSession = scoped_session(SessionFactory)

# تتبع الجلسات النشطة
_active_sessions = {}
_session_lock = threading.Lock()

# تسجيل أحداث المحرك للتشخيص
@event.listens_for(engine, "connect")
def connect(dbapi_connection, connection_record):
    log_info(f"تم فتح اتصال جديد بقاعدة البيانات (ID: {id(dbapi_connection)})")

@event.listens_for(engine, "checkout")
def checkout(dbapi_connection, connection_record, connection_proxy):
    log_info(f"تم استخدام اتصال من التجمع (ID: {id(dbapi_connection)})")

@event.listens_for(engine, "checkin")
def checkin(dbapi_connection, connection_record):
    log_info(f"تم إرجاع اتصال إلى التجمع (ID: {id(dbapi_connection)})")

@contextmanager
def session_scope():
    """
    مدير سياق لجلسات قاعدة البيانات يضمن إغلاق الجلسة بشكل صحيح

    Yields:
        Session: جلسة قاعدة البيانات
    """
    session = SessionFactory()
    session_id = id(session)

    # تسجيل الجلسة النشطة
    with _session_lock:
        caller = traceback.extract_stack()[-3]  # الحصول على معلومات المستدعي
        caller_info = f"{os.path.basename(caller.filename)}:{caller.lineno} in {caller.name}"
        _active_sessions[session_id] = {
            'created_at': time.time(),
            'thread_id': threading.get_ident(),
            'caller': caller_info
        }

    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        log_error(f"خطأ في قاعدة البيانات (session_id: {session_id}): {str(e)}", e)
        raise
    finally:
        session.close()
        # إزالة الجلسة من القائمة النشطة
        with _session_lock:
            if session_id in _active_sessions:
                del _active_sessions[session_id]

def get_session():
    """
    الحصول على جلسة قاعدة البيانات

    Returns:
        Session: جلسة قاعدة البيانات

    Note:
        يجب إغلاق الجلسة يدويًا عند استخدام هذه الدالة
    """
    session = SessionFactory()
    session_id = id(session)

    # تسجيل الجلسة النشطة
    with _session_lock:
        caller = traceback.extract_stack()[-2]  # الحصول على معلومات المستدعي
        caller_info = f"{os.path.basename(caller.filename)}:{caller.lineno} in {caller.name}"
        _active_sessions[session_id] = {
            'created_at': time.time(),
            'thread_id': threading.get_ident(),
            'caller': caller_info
        }

    return session

def get_scoped_session():
    """
    الحصول على جلسة مرتبطة بالسلسلة الحالية

    Returns:
        Session: جلسة قاعدة البيانات مرتبطة بالسلسلة الحالية
    """
    return ScopedSession()

def close_session(session):
    """
    إغلاق جلسة قاعدة البيانات بشكل آمن

    Args:
        session: جلسة قاعدة البيانات
    """
    if session:
        session_id = id(session)
        try:
            session.close()
        except Exception as e:
            log_error(f"خطأ أثناء إغلاق جلسة قاعدة البيانات (session_id: {session_id}): {str(e)}", e)
        finally:
            # إزالة الجلسة من القائمة النشطة
            with _session_lock:
                if session_id in _active_sessions:
                    del _active_sessions[session_id]

def get_active_sessions_info():
    """
    الحصول على معلومات حول جلسات قاعدة البيانات النشطة

    Returns:
        dict: معلومات حول الجلسات النشطة
    """
    with _session_lock:
        current_time = time.time()
        result = {}
        for session_id, info in _active_sessions.items():
            duration = current_time - info['created_at']
            result[session_id] = {
                **info,
                'duration_seconds': duration
            }
        return result

def cleanup_stale_sessions(max_age_seconds=300):
    """
    تنظيف الجلسات القديمة التي قد تكون مسربة

    Args:
        max_age_seconds: العمر الأقصى للجلسة بالثواني قبل اعتبارها قديمة

    Returns:
        int: عدد الجلسات التي تم تنظيفها
    """
    cleaned_count = 0
    with _session_lock:
        current_time = time.time()
        stale_sessions = []

        for session_id, info in _active_sessions.items():
            if current_time - info['created_at'] > max_age_seconds:
                stale_sessions.append(session_id)

        for session_id in stale_sessions:
            log_error(f"تنظيف جلسة قديمة (session_id: {session_id}, caller: {_active_sessions[session_id]['caller']}, age: {current_time - _active_sessions[session_id]['created_at']:.1f}s)")
            del _active_sessions[session_id]
            cleaned_count += 1

    return cleaned_count
