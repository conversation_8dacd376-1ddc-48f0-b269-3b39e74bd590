#define MyAppName "نظام مبيعات الماس"
#define MyAppNameEN "Diamond Sales"
#define MyAppVersion "1.30"
#define MyAppPublisher "Diamond Sales System"
#define MyAppURL "https://www.example.com/"
#define MyAppExeName "DiamondSales.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
AppId={{A1B2C3D4-E5F6-4A5B-9C8D-7E6F5A4B3C2D}}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppNameEN}
DefaultGroupName={#MyAppName}
AllowNoIcons=yes
; تثبيت البرنامج بصلاحيات المسؤول ولكن تشغيله بصلاحيات المستخدم العادي
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog
OutputDir=installer_output
OutputBaseFilename=DiamondSalesMainProgram_v1.30_English_Setup
SetupIconFile=assets\diamond_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
; دعم اللغة العربية
WizardResizable=yes
DirExistsWarning=no
; إضافة معلومات النسخة
VersionInfoVersion={#MyAppVersion}
VersionInfoCompany={#MyAppPublisher}
VersionInfoDescription={#MyAppName}
VersionInfoCopyright=Copyright © 2025 {#MyAppPublisher}
; إعدادات إضافية لدعم اللغة العربية
AlwaysShowComponentsList=no
AlwaysShowDirOnReadyPage=yes

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "Create a desktop shortcut"; GroupDescription: "Additional shortcuts:"
Name: "quicklaunchicon"; Description: "Create a Quick Launch shortcut"; GroupDescription: "Additional shortcuts:"; OnlyBelowVersion: 6.1

[Files]
Source: "dist\DiamondSales\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "installer_package\README.txt"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
; الاختصار الرئيسي للبرنامج
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\diamond_icon.ico"
; اختصارات الشاشات المختلفة
Name: "{group}\المبيعات - Sales"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\sales_icon.ico"; Parameters: "--screen=sales"
Name: "{group}\المشتريات - Purchases"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\purchases_icon.ico"; Parameters: "--screen=purchases"
Name: "{group}\العملاء - Customers"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\customers_icon.ico"; Parameters: "--screen=customers"
Name: "{group}\الموردين - Suppliers"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\suppliers_icon.ico"; Parameters: "--screen=suppliers"
Name: "{group}\المخزون - Inventory"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\inventory_icon.ico"; Parameters: "--screen=inventory"
Name: "{group}\التقارير - Reports"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\reports_icon.ico"; Parameters: "--screen=reports"
Name: "{group}\السندات - Vouchers"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\vouchers_icon.ico"; Parameters: "--screen=vouchers"
Name: "{group}\صندوق النقدية - Cash Box"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\cash_box_icon.png"; Parameters: "--screen=cashbox"
Name: "{group}\المستخدمين - Users"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\users_icon.ico"; Parameters: "--screen=users"
Name: "{group}\الإعدادات - Settings"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\settings_icon.ico"; Parameters: "--screen=settings"
; اختصار إلغاء التثبيت
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
; اختصار سطح المكتب
Name: "{commondesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\assets\diamond_icon.ico"; Tasks: desktopicon

[UninstallDelete]
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\backups"
Type: files; Name: "{app}\diamond_sales.db"

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#MyAppName}}"; Flags: nowait postinstall skipifsilent
