.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |dlcorn|  unicode:: U+0231E .. BOTTOM LEFT CORNER
.. |drcorn|  unicode:: U+0231F .. BOTTOM RIGHT CORNER
.. |gtlPar|  unicode:: U+02995 .. DOUBLE LEFT ARC GREATER-THAN BRACKET
.. |langd|   unicode:: U+02991 .. LEFT ANGLE BRACKET WITH DOT
.. |lbrke|   unicode:: U+0298B .. LEFT SQUARE BRACKET WITH UNDERBAR
.. |lbrksld| unicode:: U+0298F .. LEFT SQUARE BRACKET WITH TICK IN BOTTOM CORNER
.. |lbrkslu| unicode:: U+0298D .. LEFT SQUARE BRACKET WITH TICK IN TOP CORNER
.. |lceil|   unicode:: U+02308 .. LEFT CEILING
.. |lfloor|  unicode:: U+0230A .. LEFT FLOOR
.. |lmoust|  unicode:: U+023B0 .. UPPER LEFT OR LOWER RIGHT CURLY BRACKET SECTION
.. |lpargt|  unicode:: U+029A0 .. SPHERICAL ANGLE OPENING LEFT
.. |lparlt|  unicode:: U+02993 .. LEFT ARC LESS-THAN BRACKET
.. |ltrPar|  unicode:: U+02996 .. DOUBLE RIGHT ARC LESS-THAN BRACKET
.. |rangd|   unicode:: U+02992 .. RIGHT ANGLE BRACKET WITH DOT
.. |rbrke|   unicode:: U+0298C .. RIGHT SQUARE BRACKET WITH UNDERBAR
.. |rbrksld| unicode:: U+0298E .. RIGHT SQUARE BRACKET WITH TICK IN BOTTOM CORNER
.. |rbrkslu| unicode:: U+02990 .. RIGHT SQUARE BRACKET WITH TICK IN TOP CORNER
.. |rceil|   unicode:: U+02309 .. RIGHT CEILING
.. |rfloor|  unicode:: U+0230B .. RIGHT FLOOR
.. |rmoust|  unicode:: U+023B1 .. UPPER RIGHT OR LOWER LEFT CURLY BRACKET SECTION
.. |rpargt|  unicode:: U+02994 .. RIGHT ARC GREATER-THAN BRACKET
.. |ulcorn|  unicode:: U+0231C .. TOP LEFT CORNER
.. |urcorn|  unicode:: U+0231D .. TOP RIGHT CORNER
