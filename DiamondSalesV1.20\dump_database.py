#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para extraer los datos de todas las tablas de la base de datos
"""

import sqlite3
import json
import os
from datetime import datetime

def format_value(value):
    """Formatea los valores para una mejor visualización"""
    if isinstance(value, datetime):
        return value.strftime('%Y-%m-%d %H:%M:%S')
    return value

def dump_table(cursor, table_name):
    """Extrae los datos de una tabla y los devuelve como una lista de diccionarios"""
    try:
        # Obtener los nombres de las columnas
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Obtener los datos
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()
        
        # Convertir a lista de diccionarios
        result = []
        for row in rows:
            row_dict = {}
            for i, column in enumerate(columns):
                row_dict[column] = row[i]
            result.append(row_dict)
        
        return result
    except Exception as e:
        print(f"Error al extraer datos de la tabla {table_name}: {str(e)}")
        return []

def main():
    # Conectar a la base de datos
    conn = sqlite3.connect('diamond_sales.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Obtener la lista de tablas
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = [table[0] for table in cursor.fetchall()]
    
    # Crear directorio para los datos si no existe
    output_dir = "database_dump"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Extraer datos de cada tabla
    all_data = {}
    for table in tables:
        print(f"Extrayendo datos de la tabla: {table}")
        table_data = dump_table(cursor, table)
        all_data[table] = table_data
        
        # Guardar datos de la tabla en un archivo separado
        with open(os.path.join(output_dir, f"{table}.json"), 'w', encoding='utf-8') as f:
            json.dump(table_data, f, ensure_ascii=False, indent=2, default=str)
    
    # Guardar todos los datos en un solo archivo
    with open(os.path.join(output_dir, "all_data.json"), 'w', encoding='utf-8') as f:
        json.dump(all_data, f, ensure_ascii=False, indent=2, default=str)
    
    # Imprimir resumen
    print("\n=== Resumen de datos extraídos ===")
    for table, data in all_data.items():
        print(f"{table}: {len(data)} registros")
    
    # Cerrar la conexión
    conn.close()
    
    print(f"\nLos datos han sido guardados en el directorio: {output_dir}")

if __name__ == "__main__":
    main()
