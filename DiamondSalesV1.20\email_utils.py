#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
وحدة المساعدة لإرسال رسائل البريد الإلكتروني
"""

import smtplib
import ssl
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from logger import log_error, log_info

# إعدادات البريد الإلكتروني
DEFAULT_SMTP_SERVER = "smtp.gmail.com"
DEFAULT_SMTP_PORT = 587

def send_verification_email(recipient_email, verification_code, username=None):
    """
    إرسال رمز التحقق عبر البريد الإلكتروني
    
    المعلمات:
    ----------
    recipient_email : str
        عنوان البريد الإلكتروني للمستلم
    verification_code : str
        رمز التحقق المراد إرساله
    username : str, optional
        اسم المستخدم (إذا كان متوفرًا)
    
    العوائد:
    -------
    bool
        True في حالة النجاح، False في حالة الفشل
    str
        رسالة توضح النتيجة (نجاح أو سبب الفشل)
    """
    # التحقق من وجود معلومات SMTP في متغيرات البيئة
    smtp_server = os.environ.get("DIAMOND_SMTP_SERVER", DEFAULT_SMTP_SERVER)
    smtp_port = int(os.environ.get("DIAMOND_SMTP_PORT", DEFAULT_SMTP_PORT))
    smtp_user = os.environ.get("DIAMOND_SMTP_USER")
    smtp_password = os.environ.get("DIAMOND_SMTP_PASSWORD")
    sender_email = os.environ.get("DIAMOND_EMAIL_SENDER")
    
    # التحقق من وجود معلومات الاتصال الضرورية
    if not all([smtp_user, smtp_password, sender_email]):
        message = "لم يتم تكوين خادم البريد الإلكتروني. يرجى الاتصال بمسؤول النظام."
        log_error(message)
        return False, message
    
    # إنشاء رسالة البريد الإلكتروني
    message = MIMEMultipart("alternative")
    message["Subject"] = "رمز التحقق لإعادة تعيين كلمة المرور - نظام مبيعات الألماس"
    message["From"] = sender_email
    message["To"] = recipient_email
    
    # إنشاء محتوى البريد بالنص العادي وHTML
    user_greeting = f" {username}" if username else ""
    
    text = f"""
    مرحباً{user_greeting}،

    لقد طلبت إعادة تعيين كلمة المرور الخاصة بك في نظام مبيعات الألماس.
    رمز التحقق الخاص بك هو: {verification_code}

    هذا الرمز صالح لمدة 15 دقيقة فقط.
    إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذه الرسالة.

    مع تحيات،
    فريق نظام مبيعات الألماس
    """
    
    html = f"""
    <html>
    <body dir="rtl" style="font-family: Arial, sans-serif; line-height: 1.6;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <h2 style="color: #333;">إعادة تعيين كلمة المرور</h2>
            <p>مرحباً{user_greeting}،</p>
            <p>لقد طلبت إعادة تعيين كلمة المرور الخاصة بك في نظام مبيعات الألماس.</p>
            <p>رمز التحقق الخاص بك هو:</p>
            <div style="background-color: #f5f5f5; padding: 15px; font-size: 24px; text-align: center; letter-spacing: 5px; font-weight: bold; border-radius: 4px; margin: 20px 0;">
                {verification_code}
            </div>
            <p>هذا الرمز صالح لمدة <strong>15 دقيقة</strong> فقط.</p>
            <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذه الرسالة.</p>
            <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
            <p style="color: #777; font-size: 12px;">مع تحيات،<br>فريق نظام مبيعات الألماس</p>
        </div>
    </body>
    </html>
    """
    
    # إضافة أجزاء الرسالة
    part1 = MIMEText(text, "plain", "utf-8")
    part2 = MIMEText(html, "html", "utf-8")
    message.attach(part1)
    message.attach(part2)
    
    try:
        # إنشاء اتصال آمن
        context = ssl.create_default_context()
        
        # محاولة الاتصال وإرسال البريد الإلكتروني
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.ehlo()
            server.starttls(context=context)
            server.ehlo()
            server.login(smtp_user, smtp_password)
            server.sendmail(sender_email, recipient_email, message.as_string())
        
        log_info(f"تم إرسال رمز التحقق بنجاح إلى {recipient_email}")
        return True, "تم إرسال رمز التحقق بنجاح"
    
    except Exception as e:
        error_msg = f"فشل في إرسال البريد الإلكتروني: {str(e)}"
        log_error(error_msg)
        return False, error_msg
