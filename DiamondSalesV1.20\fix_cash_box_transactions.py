"""
تصحيح دائم للمشكلة في أكواد صندوق النقدية مع سندات الصرف
"""

from PyQt6.QtWidgets import QMessageBox
from sqlalchemy.sql.expression import or_
from sqlalchemy import func
from db_session import session_scope
from database import صندوق_النقدية, حركة_نقدية, Receipt
from datetime import datetime

def apply_fixes():
    """
    تطبيق التصحيحات اللازمة لملف cash_box_screen.py
    """
    try:
        # 1. إصلاح مشكلة حركات النقدية غير المرتبطة بصندوق النقدية
        with session_scope() as session:
            # التحقق من وجود صندوق النقدية
            cash_box = session.query(صندوق_النقدية).first()
            if not cash_box:
                cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                session.add(cash_box)
                session.commit()
            
            # إصلاح الحركات النقدية التي ليس لها صندوق نقدية
            orphaned_transactions = session.query(حركة_نقدية).filter(
                or_(حركة_نقدية.cash_box_id == None, حركة_نقدية.cash_box_id != cash_box.id)
            ).all()
            
            if orphaned_transactions:
                for transaction in orphaned_transactions:
                    transaction.cash_box_id = cash_box.id
                session.commit()
            
            # 2. إعادة حساب الرصيد الحالي للصندوق
            total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                حركة_نقدية.transaction_type == "deposit"
            ).scalar() or 0
            
            total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                حركة_نقدية.transaction_type == "withdraw"
            ).scalar() or 0
            
            correct_balance = total_deposits - total_withdrawals
            
            # تحديث رصيد الصندوق إذا كان مختلفًا
            if cash_box.balance != correct_balance:
                cash_box.balance = correct_balance
                cash_box.last_updated = datetime.now()
                session.commit()

            return {
                "success": True,
                "fixed_transactions": len(orphaned_transactions),
                "correct_balance": correct_balance
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def show_result(result):
    """
    عرض نتيجة التصحيح
    """
    if result["success"]:
        QMessageBox.information(None, "نجاح", 
            f"تم إصلاح {result['fixed_transactions']} حركة نقدية.\n"
            f"تم تصحيح رصيد الصندوق إلى: {result['correct_balance']:.2f} $")
    else:
        QMessageBox.critical(None, "خطأ", 
            f"حدث خطأ أثناء التصحيح: {result['error']}")

if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication.instance() or QApplication(sys.argv)
    result = apply_fixes()
    show_result(result)
