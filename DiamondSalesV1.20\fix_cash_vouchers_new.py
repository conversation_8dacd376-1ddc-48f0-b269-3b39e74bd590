"""
تصحيح مشكلة عرض سندات الصرف في صندوق النقدية
Este script verifica y corrige el problema con los sندات الصرف que no aparecen en el صندوق النقدية
"""

from PyQt6.QtWidgets import QApplication, QMessageBox
from sqlalchemy import func, or_, case, String, Float
from db_session import session_scope
from database import صندوق_النقدية, حركة_نقدية, Receipt, Purchase, Supplier
import sys
from datetime import datetime

def fix_cash_box_vouchers():
    """
    هذه الدالة تتحقق من سندات الصرف وتصلح المشكلة
    """
    try:
        with session_scope() as session:
            # التحقق من وجود صندوق النقدية
            cash_box = session.query(صندوق_النقدية).first()
            if not cash_box:
                print("إنشاء صندوق نقدية جديد...")
                cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                session.add(cash_box)
                session.commit()
            
            # 1. إصلاح سندات الصرف المباشرة (CashOut)
            print("إصلاح سندات الصرف المباشرة...")
            withdrawals = session.query(Receipt).filter(
                or_(Receipt.receipt_type == "CashOut", Receipt.receipt_type == "صرف")
            ).all()
            
            fixed_receipt_count = 0
            errors_count = 0
            
            for receipt in withdrawals:
                # التحقق مما إذا كان هناك حركة نقدية مرتبطة بهذا السند
                transaction = session.query(حركة_نقدية).filter(
                    or_(
                        حركة_نقدية.reference == f"سند صرف #{receipt.id}",
                        حركة_نقدية.reference == f"سند صرف رقم {receipt.id}"
                    )
                ).first()
                
                if not transaction:
                    try:
                        # إنشاء وصف مناسب
                        description = "سند صرف نقدي"
                        if receipt.supplier_id:
                            supplier = session.query(Supplier).filter_by(id=receipt.supplier_id).first()
                            if supplier:
                                description = f"دفعة للمورد {supplier.name}"
                        
                        # إضافة حركة نقدية للسند
                        transaction = حركة_نقدية(
                            cash_box_id=cash_box.id,
                            transaction_type="withdraw",
                            amount=receipt.amount_usd,
                            balance_after=0,  # سيتم تحديثه لاحقاً
                            transaction_date=receipt.issue_date,
                            reference=f"سند صرف #{receipt.id}",
                            description=description,
                            created_by=receipt.created_by if receipt.created_by else 1  # استخدام معرف المنشئ من السند أو استخدام المشرف كبديل
                        )
                        session.add(transaction)
                        session.flush()
                        fixed_receipt_count += 1
                        print(f"تم إصلاح سند الصرف المباشر رقم {receipt.id}")
                    except Exception as e:
                        errors_count += 1
                        print(f"خطأ في إصلاح سند الصرف المباشر رقم {receipt.id}: {str(e)}")
            
            # 2. إصلاح سندات الصرف من المشتريات
            print("إصلاح سندات الصرف من المشتريات...")
            purchase_payments = session.query(Purchase).filter(Purchase.amount_paid > 0).all()
            
            fixed_purchase_count = 0
            for purchase in purchase_payments:
                # التحقق من وجود حركة نقدية مرتبطة بفاتورة الشراء هذه
                transaction_reference = f"فاتورة مشتريات #{purchase.id}"
                existing_transaction = session.query(حركة_نقدية).filter(
                    حركة_نقدية.reference == transaction_reference
                ).first()
                
                if not existing_transaction:
                    try:
                        # الحصول على اسم المورد
                        supplier = session.query(Supplier).filter_by(id=purchase.supplier_id).first()
                        supplier_name = supplier.name if supplier else "غير معروف"
                        
                        # إنشاء حركة نقدية جديدة لسند الصرف
                        transaction = حركة_نقدية(
                            cash_box_id=cash_box.id,
                            transaction_type="withdraw",
                            amount=purchase.amount_paid,
                            balance_after=0,  # سيتم تحديثه لاحقاً
                            transaction_date=purchase.purchase_date or datetime.now(),
                            reference=transaction_reference,
                            description=f"دفعة لفاتورة المورد {supplier_name}",
                            created_by=purchase.created_by if hasattr(purchase, 'created_by') and purchase.created_by else 1  # Admin user as fallback
                        )
                        session.add(transaction)
                        session.flush()
                        fixed_purchase_count += 1
                        print(f"تم إصلاح سند الصرف من فاتورة المشتريات رقم {purchase.id}")
                    except Exception as e:
                        errors_count += 1
                        print(f"خطأ في إصلاح سند الصرف من فاتورة المشتريات رقم {purchase.id}: {str(e)}")
            
            # 3. تحديث أرصدة ما بعد العمليات وإعادة حساب رصيد الصندوق
            print("إعادة حساب جميع الأرصدة...")
            
            # ترتيب الحركات حسب التاريخ
            transactions = session.query(حركة_نقدية).order_by(حركة_نقدية.transaction_date).all()
            
            # إعادة حساب الأرصدة
            current_balance = 0.0
            for transaction in transactions:
                if transaction.transaction_type == "deposit":
                    current_balance += transaction.amount
                else:
                    current_balance -= transaction.amount
                transaction.balance_after = current_balance
            
            cash_box.balance = current_balance
            cash_box.last_updated = datetime.now()
            session.commit()            
            return {
                "success": True,
                "fixed_receipt_count": fixed_receipt_count,
                "fixed_purchase_count": fixed_purchase_count,
                "errors_count": errors_count,
                "balance": current_balance
            }
    except Exception as e:
        print(f"خطأ: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

def show_result_dialog(result):
    """
    عرض نتيجة الإصلاح
    """
    app = QApplication.instance() or QApplication(sys.argv)
    
    if result["success"]:
        total_fixed = result.get("fixed_receipt_count", 0) + result.get("fixed_purchase_count", 0)
        if total_fixed > 0:
            QMessageBox.information(None, "نجاح", 
                f"تم إصلاح {result.get('fixed_receipt_count', 0)} سند صرف مباشر و "
                f"{result.get('fixed_purchase_count', 0)} سند صرف من المشتريات.\n"
                f"رصيد الصندوق الحالي: {result['balance']:.2f} $")
        else:
            QMessageBox.information(None, "معلومات",
                "لم يتم العثور على سندات صرف بحاجة إلى إصلاح.\n"
                f"رصيد الصندوق الحالي: {result['balance']:.2f} $")
    else:
        QMessageBox.critical(None, "خطأ", 
            f"حدث خطأ أثناء إصلاح سندات الصرف: {result['error']}")

if __name__ == "__main__":
    result = fix_cash_box_vouchers()
    show_result_dialog(result)
