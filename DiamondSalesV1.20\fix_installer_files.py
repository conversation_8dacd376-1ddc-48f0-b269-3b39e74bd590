#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix the installer files to use only custom_arabic.isl
"""

files_to_fix = [
    "diamond_sales_main_installer_arabic.iss",
    "activation_generator_installer_arabic.iss",
    "reset_password_installer_arabic.iss"
]

for filename in files_to_fix:
    try:
        print(f"Fixing {filename}...")
        with open(filename, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Replace the language reference
        content = content.replace(
            'Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl,custom_arabic.isl"', 
            'Name: "arabic"; MessagesFile: "custom_arabic.isl"'
        )
        
        with open(filename, "w", encoding="utf-8") as f:
            f.write(content)
            
        print(f"✅ {filename} fixed")
    except Exception as e:
        print(f"❌ Error fixing {filename}: {e}")
