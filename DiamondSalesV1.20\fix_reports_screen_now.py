"""
تصحيح مشكلة في شاشة تقارير السندات
"""

from PyQt6.QtWidgets import QMessageBox
import os
import shutil
import sys

# المسارات
dir_path = os.path.dirname(os.path.realpath(__file__))
reports_screen_path = os.path.join(dir_path, "reports_screen.py")
reports_screen_fixed_path = os.path.join(dir_path, "reports_screen_fixed.py")
reports_screen_backup_path = os.path.join(dir_path, "reports_screen_backup.py")

try:
    print(f"المسار الحالي: {dir_path}")
    print(f"مسار ملف reports_screen.py: {reports_screen_path}")
    print(f"مسار ملف reports_screen_fixed.py: {reports_screen_fixed_path}")
    
    # 1. عمل نسخة احتياطية من الملف الأصلي
    if os.path.exists(reports_screen_path):
        shutil.copy2(reports_screen_path, reports_screen_backup_path)
        print(f"تم عمل نسخة احتياطية: {reports_screen_backup_path}")
    else:
        print(f"الملف الأصلي غير موجود: {reports_screen_path}")
        sys.exit(1)
    
    # 2. التحقق من وجود الملف المعدل
    if not os.path.exists(reports_screen_fixed_path):
        print(f"الملف المعدل غير موجود: {reports_screen_fixed_path}")
        sys.exit(1)
    
    # 3. نسخ الملف المعدل إلى الملف الأصلي
    shutil.copy2(reports_screen_fixed_path, reports_screen_path)
    print(f"تم استبدال الملف: {reports_screen_path}")
    
    # 4. التحقق من نجاح العملية
    if os.path.exists(reports_screen_path) and os.path.getsize(reports_screen_path) > 0:
        print("تم إصلاح ملف شاشة التقارير بنجاح!")
    else:
        print("حدث خطأ أثناء إصلاح الملف.")
    
except Exception as e:
    print(f"حدث خطأ: {str(e)}")
