"""
برنامج إصلاح سندات الصرف المحددة
يقوم هذا البرنامج بإصلاح ثلاثة سندات صرف محددة وربطها بحركات نقدية في صندوق النقدية
السندات المستهدفة:
1. سند رقم 5: 25.00 دولار (CashOut)
2. سند رقم 4: 50.00 دولار (صرف) - للمورد MOHAMMED
3. سند رقم 2: 2666.67 دولار (CashOut) - للمورد MOHAMMED
"""

from database import Receipt, حركة_نقدية, صندوق_النقدية, Supplier
from db_session import session_scope
from datetime import datetime
from sqlalchemy import or_

def fix_specific_vouchers():
    """
    إصلاح سندات الصرف المحددة وربطها بحركات نقدية
    """
    try:
        with session_scope() as session:
            # الحصول على صندوق النقدية
            cash_box = session.query(صندوق_النقدية).first()
            if not cash_box:
                print("خطأ: لا يوجد صندوق نقدية في النظام!")
                return False

            print(f"معرف صندوق النقدية: {cash_box.id}")
            print(f"الرصيد الحالي: {cash_box.balance:.2f} دولار")

            # الحصول على المورد MOHAMMED
            mohammed_supplier = session.query(Supplier).filter_by(name="MOHAMMED").first()
            if not mohammed_supplier:
                print("خطأ: لم يتم العثور على المورد MOHAMMED!")
                return False

            print(f"معرف المورد MOHAMMED: {mohammed_supplier.id}")

            # قائمة السندات المطلوب إصلاحها
            vouchers_to_fix = [
                {"id": 5, "amount": 25.00, "type": "CashOut", "supplier_id": None},
                {"id": 4, "amount": 50.00, "type": "صرف", "supplier_id": mohammed_supplier.id},
                {"id": 2, "amount": 2666.67, "type": "CashOut", "supplier_id": mohammed_supplier.id}
            ]

            successful_fixes = 0
            current_balance = cash_box.balance

            for voucher_info in vouchers_to_fix:
                # البحث عن السند
                receipt = session.query(Receipt).filter_by(id=voucher_info["id"]).first()
                if not receipt:
                    print(f"خطأ: لم يتم العثور على السند رقم {voucher_info['id']}")
                    continue

                # التحقق من عدم وجود حركة نقدية مرتبطة مسبقاً
                existing_transaction = session.query(حركة_نقدية).filter(
                    or_(
                        حركة_نقدية.reference == f"سند صرف #{receipt.id}",
                        حركة_نقدية.reference == f"سند صرف رقم {receipt.id}"
                    )
                ).first()

                if existing_transaction:
                    print(f"تنبيه: السند رقم {receipt.id} مرتبط بالفعل بحركة نقدية رقم {existing_transaction.id}")
                    continue

                # التحقق من تطابق المبلغ
                if abs(receipt.amount_usd - voucher_info["amount"]) > 0.01:
                    print(f"خطأ: مبلغ السند رقم {receipt.id} ({receipt.amount_usd:.2f}) لا يتطابق مع المبلغ المتوقع ({voucher_info['amount']:.2f})")
                    continue

                # تحديث الرصيد المؤقت
                current_balance -= receipt.amount_usd

                # إنشاء حركة نقدية جديدة
                new_transaction = حركة_نقدية(
                    cash_box_id=cash_box.id,  # تحديد معرف صندوق النقدية
                    transaction_type="withdraw",
                    amount=receipt.amount_usd,
                    transaction_date=receipt.issue_date or datetime.now(),
                    reference=f"سند صرف #{receipt.id}",
                    description=f"دفعة نقدية - سند صرف رقم {receipt.id}" + 
                              (f" - المورد: {mohammed_supplier.name}" if voucher_info["supplier_id"] else ""),
                    balance_after=current_balance
                )
                session.add(new_transaction)

                # تحديث رصيد الصندوق
                cash_box.balance = current_balance
                cash_box.last_updated = datetime.now()

                # طباعة تفاصيل العملية
                print(f"تم إنشاء حركة نقدية للسند رقم {receipt.id}:")
                print(f"- المبلغ: {receipt.amount_usd:.2f} دولار")
                print(f"- الرصيد الجديد: {current_balance:.2f} دولار")
                print(f"- معرف صندوق النقدية: {cash_box.id}")
                if voucher_info["supplier_id"]:
                    print(f"- المورد: {mohammed_supplier.name}")
                
                # تحديث عداد الإصلاحات الناجحة
                successful_fixes += 1

            # حفظ التغييرات
            session.commit()
                
            print(f"\nتم الانتهاء من إصلاح {successful_fixes} سندات من أصل {len(vouchers_to_fix)}")
            return successful_fixes == len(vouchers_to_fix)

    except Exception as e:
        print(f"حدث خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_specific_vouchers()
    if success:
        print("تم إصلاح جميع السندات بنجاح!")
    else:
        print("حدث خطأ أثناء إصلاح السندات")
