"""
إصلاح مشكلة شاشة تقارير السندات
"""

import re

def fix_reports_screen():
    try:
        # 1. قراءة محتوى الملف
        with open('reports_screen.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 2. إصلاح مشكلة القيم الـ None في الحسابات
        # تحديث الجزء المتعلق بإضافة القيم إلى table_data
        pattern1 = r'("amount_usd": voucher\.amount_usd,)'
        replacement1 = r'"amount_usd": voucher.amount_usd if voucher.amount_usd is not None else 0,'
        
        pattern2 = r'("amount_sar": voucher\.amount_sar,)'
        replacement2 = r'"amount_sar": voucher.amount_sar if voucher.amount_sar is not None else 0,'
        
        content = re.sub(pattern1, replacement1, content)
        content = re.sub(pattern2, replacement2, content)
        
        # 3. إصلاح مشكلة محتملة في تنسيق الجدول
        pattern3 = r'(self\.vouchers_table\.setItem\([^,]+, [^,]+, QTableWidgetItem\(f"{data\[\'amount_usd\'\]:[^}]+}\))'
        replacement3 = r'self.vouchers_table.setItem(row, 4, QTableWidgetItem(f"{data[\'amount_usd\'] if data[\'amount_usd\'] is not None else 0:.3f}"))'
        
        pattern4 = r'(self\.vouchers_table\.setItem\([^,]+, [^,]+, QTableWidgetItem\(f"{data\[\'amount_sar\'\]:[^}]+}\))'
        replacement4 = r'self.vouchers_table.setItem(row, 5, QTableWidgetItem(f"{data[\'amount_sar\'] if data[\'amount_sar\'] is not None else 0:.3f}"))'
        
        content = re.sub(pattern3, replacement3, content)
        content = re.sub(pattern4, replacement4, content)
        
        # 4. إصلاح مشكلة المسافات البادئة
        content = re.sub(r'total_count \+= 1 +if', 'total_count += 1\n                if', content)
        content = re.sub(r'القائمة +table_data\.append', 'القائمة\n                table_data.append', content)
        
        # 5. كتابة المحتوى المعدل
        with open('reports_screen_fixed_new.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("تم إصلاح الملف بنجاح! النتيجة في reports_screen_fixed_new.py")
        
    except Exception as e:
        print(f"حدث خطأ أثناء إصلاح الملف: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_reports_screen()
