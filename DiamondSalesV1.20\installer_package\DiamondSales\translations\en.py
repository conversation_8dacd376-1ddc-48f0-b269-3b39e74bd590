#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Translation file for English language
"""

translations = {
    # Window titles
    "app_title": "Diamond Sales Management System",
    "login_title": "Diamond Sales Management System - Login",
    "dashboard_title": "Diamond Sales Management System - Dashboard",
    "sales_title": "Diamond Sales Management System - Sales",
    "purchases_title": "Diamond Sales Management System - Purchases",
    "customers_title": "Diamond Sales Management System - Customers",
    "suppliers_title": "Diamond Sales Management System - Suppliers",
    "vouchers_title": "Diamond Sales Management System - Vouchers",
    "reports_title": "Diamond Sales Management System - Reports",
    "settings_title": "Diamond Sales Management System - Settings",
    "users_title": "Diamond Sales Management System - Users Management",
    "categories_units_title": "Diamond Sales Management System - Categories and Units",
    "opening_balances_title": "Diamond Sales Management System - Opening Balances",
    "activation_title": "Program Activation",
    "hardware_id_label": "Hardware ID:",
    "activation_code_label": "Enter activation code in format: XXXX-XXXX-XXXX-XXXX-XXXX",
    "activation_status_label": "Activation Status:",
    "not_activated": "Not Activated",
    "expiry_date_label": "Expiry Date:",
    "days_left_label": "Days Left:",
    "activated_features_label": "Activated Features:",
    "system_title": "Diamond Sales Management System",
    "username_label": "Username:",
    "password_label": "Password:",
    "customer_name_label": "Customer Name:",
    "supplier_name_label": "Supplier Name:",
    
    # User management messages
    "warning": "Warning",
    "error": "Error",
    "success": "Success",
    "username_required": "Username is required",
    "username_format_error": "Username must contain only English letters and numbers",
    "invalid_email": "Invalid email address",
    "password_required": "Password is required",
    "password_mismatch": "Password and confirmation do not match",
    "username_exists": "Username already exists, please choose another username.",
    "user_added_success": "User added successfully.",
    "user_add_error": "Error occurred while adding user:",
    "user_not_found": "User not found.",
    "user_updated_success": "User data updated successfully.",
    "user_update_error": "Error occurred while updating user data:",
    "cannot_change_current_user_status": "Cannot change current user status.",
    "activate": "activate",
    "deactivate": "deactivate",
    "user_status_changed": "Successfully",
    "user_successfully": "user.",
    "user_status_change_error": "Error occurred while changing user status:",
    "cannot_delete_admin": "Cannot delete admin user.",
    "cannot_delete_current_user": "Cannot delete current user.",
    "confirm_delete": "Confirm Delete",
    "confirm_delete_user": "Are you sure you want to delete user",
    "user_deleted_success": "User deleted successfully.",
    "user_delete_error": "Error occurred while deleting user:",
    "current_user_not_found": "Current user not found",
    
    # Sales Screen Messages
    "data_load_error": "Error loading data",
    "categories_load_error": "Error loading categories",
    "units_load_error": "Error loading units",
    "please_select_category": "Please select a category",
    "please_enter_valid_weight": "Please enter a valid carat weight",
    "please_enter_valid_price": "Please enter a valid price per carat",
    "item_add_error": "Error adding item",
    "item_remove_error": "Error removing item",
    "please_add_at_least_one_item": "Please add at least one item to the invoice",
    "please_select_customer": "Please select a customer",
    "initial_payment_exceeds_total": "Initial payment cannot exceed total invoice amount",
    "invoice_saved_successfully": "Invoice saved successfully",
    "items_count": "Items count",
    "total_amount": "Total amount",
    "invoice_save_error": "Error saving invoice",
    "sales_data_load_error": "Error loading sales data",
    "search_results": "Search Results",
    "no_sales_found": "No sales found matching search criteria",
    "sales_search_error": "Error searching sales",
    "please_select_sale_to_view": "Please select a sale to view details",
    "sale_not_found": "Sale not found",
    "details_view_error": "Error viewing details",
    "please_select_sale_to_edit": "Please select a sale to edit",
    "sale_updated_successfully": "Sale updated successfully",
    "sale_update_error": "Error updating sale",
    "please_select_sale_to_delete": "Please select a sale to delete",
    "confirmation": "Confirmation",
    "confirm_delete_sale": "Are you sure you want to delete this sale?",
    "journal_entries_will_be_deleted": "All related journal entries will also be deleted.",
    "sale_deleted_successfully": "Sale deleted successfully",
    "sale_delete_error": "Error deleting sale",
    "please_select_sale_for_receipt": "Please select a sale to create receipt",
    "information": "Information",
    "no_amount_due": "No amount due for this sale",
    "receipt_created_successfully": "Receipt created successfully",
    "receipt_creation_error": "Error creating receipt",
    "please_select_invoice_to_print": "Please select an invoice to print",
    "invoice_not_found": "Invoice not found",
    "invoice_created_and_opened": "Sales invoice created successfully and opened in browser",
    "invoice_print_error": "Error printing invoice",
    "from_date_label": "From Date:",
    "to_date_label": "To Date:",
    "diamond_type_label": "Diamond Type:",
    "add_items_to_invoice": "Add Items to Invoice",
    "zero_sar": "0.00 SAR",
    "invoice_total_usd": "Invoice Total: 0.00 $",
    "invoice_total_sar": "Invoice Total: 0.00 SAR",
    
    # Main menu
    "welcome": "Welcome",
    "sales": "Sales",
    "purchases": "Purchases",
    "customers": "Customers",
    "suppliers": "Suppliers",
    "vouchers": "Vouchers",
    "reports": "Reports",
    "settings": "Settings",
    "users": "Users",
    "logout": "Logout",
    "data_lock": "Data Lock",
    "activation": "Activation",
    "user_permissions": "User Permissions",
    "copyright": "© 2025 Diamond Sales Management System - All Rights Reserved",
    
    # Login screen
    "username": "Username",
    "password": "Password",
    "login": "Login",
    "login_error": "Login Error",
    "invalid_credentials": "Invalid username or password",
    
    # Common tabs
    "list_tab": "List",
    "add_tab": "Add New",
    "edit_tab": "Edit",
    "details_tab": "Details",
    
    # Common buttons
    "save": "Save",
    "cancel": "Cancel",
    "ok": "OK",
    "add": "Add",
    "edit": "Edit",
    "delete": "Delete",
    "search": "Search",
    "print": "Print",
    "close": "Close",
    "browse": "Browse...",
    
    # Common messages
    "success": "Success",
    "error": "Error",
    "warning": "Warning",
    "info": "Information",
    "confirm": "Confirm",
    "confirm_delete": "Are you sure you want to delete?",
    "operation_success": "Operation completed successfully",
    "operation_error": "An error occurred during the operation",
    
    # Sales
    "sales_list": "Sales List",
    "add_sale": "Add New Sale",
    "sale_details": "Sale Details",
    "sale_id": "Sale ID",
    "customer": "Customer",
    "diamond_type": "Diamond Type",
    "carat_weight": "Carat Weight",
    "price_per_carat": "Price per Carat",
    "total_price_usd": "Total (USD)",
    "total_price_sar": "Total (SAR)",
    "sale_date": "Sale Date",
    "amount_paid": "Amount Paid",
    "amount_due": "Amount Due",
    "notes": "Notes",
    "create_receipt": "Create Receipt",
    "print_invoice": "Print Invoice",
    
    # Purchases
    "purchases_list": "Purchases List",
    "add_purchase": "Add New Purchase",
    "purchase_details": "Purchase Details",
    "purchase_id": "Purchase ID",
    "supplier": "Supplier",
    "purchase_date": "Purchase Date",
    "create_payment": "Create Payment",
    
    # Customers
    "customers_list": "Customers List",
    "add_customer": "Add New Customer",
    "customer_details": "Customer Details",
    "customer_id": "Customer ID",
    "customer_name": "Customer Name",
    "id_number": "ID Number",
    "phone": "Phone",
    "address": "Address",
    
    # Suppliers
    "suppliers_list": "Suppliers List",
    "add_supplier": "Add New Supplier",
    "supplier_details": "Supplier Details",
    "supplier_id": "Supplier ID",
    "supplier_name": "Supplier Name",
    
    # Settings
    "company_info": "Company Information",
    "company_name": "Company Name",
    "company_address": "Company Address",
    "tax_number": "Tax/Registration Number",
    "logo": "Logo",
    "system_settings": "System Settings",
    "exchange_rate": "Exchange Rate",
    "default_currency": "Default Currency",
    "language": "Language",
    "arabic": "العربية",
    "english": "English",
    "backup": "Backup",
    "backup_path": "Backup Path",
    "auto_backup": "Auto Backup",
    "create_backup": "Create Backup",
    "restore_backup": "Restore Backup",
    
    # Language
    "language_changed": "Language Changed",
    "restart_required": "Please restart the application to apply changes",
    "change_language": "Change Language",
}
