"""
وحدة إدارة المخزون
تحتوي على الدوال المساعدة لإدارة المخزون وحساب الكميات المتاحة
"""

from sqlalchemy import func
from database import OpeningBalance, InventoryMovement, ReceiptItem, Sale, Purchase
from db_session import session_scope
from logger import log_error, log_info
from datetime import datetime


def calculate_available_quantity(diamond_type, category_id=None, unit_id=None):
    """
    حساب الكمية المتاحة لصنف معين
    
    Args:
        diamond_type (str): نوع الألماس/الصنف
        category_id (int, optional): معرف الفئة
        unit_id (int, optional): معرف الوحدة
    
    Returns:
        float: الكمية المتاحة
    """
    try:
        with session_scope() as session:
            # حساب الرصيد الافتتاحي
            opening_balance = 0
            opening_query = session.query(OpeningBalance).filter_by(diamond_type=diamond_type)
            
            if category_id:
                opening_query = opening_query.filter_by(category_id=category_id)
            if unit_id:
                opening_query = opening_query.filter_by(unit_id=unit_id)
            
            opening_records = opening_query.all()
            for record in opening_records:
                opening_balance += record.quantity
            
            # حساب إجمالي الداخل من حركات المخزون
            total_in_query = session.query(func.sum(InventoryMovement.quantity)).filter(
                InventoryMovement.diamond_type == diamond_type,
                InventoryMovement.movement_type == 'in'
            )
            
            if category_id:
                total_in_query = total_in_query.filter(InventoryMovement.category_id == category_id)
            if unit_id:
                total_in_query = total_in_query.filter(InventoryMovement.unit_id == unit_id)
            
            total_in = total_in_query.scalar() or 0
            
            # حساب إجمالي الخارج من حركات المخزون
            total_out_query = session.query(func.sum(InventoryMovement.quantity)).filter(
                InventoryMovement.diamond_type == diamond_type,
                InventoryMovement.movement_type == 'out'
            )
            
            if category_id:
                total_out_query = total_out_query.filter(InventoryMovement.category_id == category_id)
            if unit_id:
                total_out_query = total_out_query.filter(InventoryMovement.unit_id == unit_id)
            
            total_out = total_out_query.scalar() or 0
            
            # الكمية المتاحة = الرصيد الافتتاحي + الداخل - الخارج
            available_quantity = opening_balance + total_in - total_out
            
            log_info(f"حساب الكمية المتاحة للصنف {diamond_type}: "
                    f"افتتاحي={opening_balance}, داخل={total_in}, خارج={total_out}, متاح={available_quantity}")
            
            return max(0, available_quantity)  # لا يمكن أن تكون الكمية سالبة
            
    except Exception as e:
        log_error(f"خطأ في حساب الكمية المتاحة للصنف {diamond_type}: {str(e)}", e)
        return 0


def validate_quantity_availability(diamond_type, requested_quantity, category_id=None, unit_id=None):
    """
    التحقق من توفر الكمية المطلوبة
    
    Args:
        diamond_type (str): نوع الألماس/الصنف
        requested_quantity (float): الكمية المطلوبة
        category_id (int, optional): معرف الفئة
        unit_id (int, optional): معرف الوحدة
    
    Returns:
        tuple: (bool: هل الكمية متوفرة, float: الكمية المتاحة)
    """
    available_quantity = calculate_available_quantity(diamond_type, category_id, unit_id)
    is_available = requested_quantity <= available_quantity
    
    log_info(f"التحقق من توفر الكمية للصنف {diamond_type}: "
            f"مطلوب={requested_quantity}, متاح={available_quantity}, متوفر={is_available}")
    
    return is_available, available_quantity


def get_inventory_movements_summary(diamond_type=None, date_from=None, date_to=None):
    """
    الحصول على ملخص حركات المخزون
    
    Args:
        diamond_type (str, optional): نوع الألماس للفلترة
        date_from (datetime, optional): تاريخ البداية
        date_to (datetime, optional): تاريخ النهاية
    
    Returns:
        list: قائمة بحركات المخزون
    """
    try:
        with session_scope() as session:
            query = session.query(InventoryMovement)
            
            if diamond_type:
                query = query.filter(InventoryMovement.diamond_type == diamond_type)
            
            if date_from:
                query = query.filter(InventoryMovement.movement_date >= date_from)
            
            if date_to:
                query = query.filter(InventoryMovement.movement_date <= date_to)
            
            movements = query.order_by(InventoryMovement.movement_date.desc()).all()
            
            result = []
            for movement in movements:
                result.append({
                    'id': movement.id,
                    'diamond_type': movement.diamond_type,
                    'movement_type': movement.movement_type,
                    'quantity': movement.quantity,
                    'price_per_unit_usd': movement.price_per_unit_usd,
                    'total_value_usd': movement.total_value_usd,
                    'movement_date': movement.movement_date,
                    'reference_type': movement.reference_type,
                    'reference_id': movement.reference_id,
                    'notes': movement.notes
                })
            
            return result
            
    except Exception as e:
        log_error(f"خطأ في الحصول على ملخص حركات المخزون: {str(e)}", e)
        return []


def get_full_inventory_summary():
    """
    الحصول على ملخص شامل للمخزون
    
    Returns:
        dict: ملخص المخزون مع الكميات والقيم
    """
    try:
        with session_scope() as session:
            # الحصول على جميع أنواع الألماس من الرصيد الافتتاحي
            opening_balances = session.query(OpeningBalance).all()
            
            # الحصول على جميع أنواع الألماس من حركات المخزون
            movement_types = session.query(InventoryMovement.diamond_type).distinct().all()
            
            # دمج جميع الأنواع
            all_types = set()
            for opening in opening_balances:
                all_types.add(opening.diamond_type)
            for movement in movement_types:
                all_types.add(movement[0])
            
            inventory_summary = {}
            
            for diamond_type in all_types:
                # حساب الكمية المتاحة
                available_qty = calculate_available_quantity(diamond_type)
                
                # الحصول على الرصيد الافتتاحي
                opening_qty = 0
                opening_value = 0
                opening_records = session.query(OpeningBalance).filter_by(diamond_type=diamond_type).all()
                for record in opening_records:
                    opening_qty += record.quantity
                    opening_value += record.total_value_usd
                
                # حساب إجمالي الحركات
                total_in = session.query(func.sum(InventoryMovement.quantity)).filter(
                    InventoryMovement.diamond_type == diamond_type,
                    InventoryMovement.movement_type == 'in'
                ).scalar() or 0
                
                total_out = session.query(func.sum(InventoryMovement.quantity)).filter(
                    InventoryMovement.diamond_type == diamond_type,
                    InventoryMovement.movement_type == 'out'
                ).scalar() or 0
                
                # حساب متوسط السعر
                avg_price = 0
                if available_qty > 0:
                    total_value = session.query(func.sum(InventoryMovement.total_value_usd)).filter(
                        InventoryMovement.diamond_type == diamond_type,
                        InventoryMovement.movement_type == 'in',
                        InventoryMovement.total_value_usd.isnot(None)
                    ).scalar() or 0
                    
                    total_value += opening_value
                    total_quantity = total_in + opening_qty
                    
                    if total_quantity > 0:
                        avg_price = total_value / total_quantity
                
                inventory_summary[diamond_type] = {
                    'diamond_type': diamond_type,
                    'opening_quantity': opening_qty,
                    'opening_value': opening_value,
                    'total_in': total_in,
                    'total_out': total_out,
                    'available_quantity': available_qty,
                    'average_price': avg_price,
                    'total_value': available_qty * avg_price if avg_price > 0 else 0,
                    'status': get_inventory_status(available_qty, opening_qty)
                }
            
            return inventory_summary
            
    except Exception as e:
        log_error(f"خطأ في الحصول على ملخص المخزون الشامل: {str(e)}", e)
        return {}


def get_inventory_status(available_qty, opening_qty):
    """
    تحديد حالة المخزون
    
    Args:
        available_qty (float): الكمية المتاحة
        opening_qty (float): الكمية الافتتاحية
    
    Returns:
        str: حالة المخزون
    """
    if available_qty <= 0:
        return "نفدت الكمية"
    elif opening_qty > 0 and available_qty < opening_qty * 0.2:
        return "كمية قليلة"
    elif opening_qty > 0 and available_qty < opening_qty * 0.5:
        return "كمية متوسطة"
    else:
        return "متوفر"


def create_inventory_movement(diamond_type, movement_type, quantity, price_per_unit=None,
                            category_id=None, unit_id=None, reference_type=None,
                            reference_id=None, customer_id=None, supplier_id=None,
                            notes=None, created_by=None, session=None):
    """
    إنشاء حركة مخزون جديدة

    Args:
        diamond_type (str): نوع الألماس/الصنف
        movement_type (str): نوع الحركة ('in' أو 'out')
        quantity (float): الكمية
        price_per_unit (float, optional): سعر الوحدة
        category_id (int, optional): معرف الفئة
        unit_id (int, optional): معرف الوحدة
        reference_type (str, optional): نوع المرجع
        reference_id (int, optional): معرف المرجع
        customer_id (int, optional): معرف العميل
        supplier_id (int, optional): معرف المورد
        notes (str, optional): ملاحظات
        created_by (int, optional): معرف المستخدم
        session (Session, optional): جلسة قاعدة البيانات

    Returns:
        bool: True إذا تم الإنشاء بنجاح
    """
    try:
        # استخدام session موجودة أو إنشاء جديدة
        if session:
            # حساب القيمة الإجمالية
            total_value_usd = None
            total_value_sar = None
            exchange_rate = None

            if price_per_unit:
                total_value_usd = quantity * price_per_unit
                # يمكن إضافة حساب القيمة بالريال هنا إذا لزم الأمر

            # إنشاء حركة المخزون
            movement = InventoryMovement(
                diamond_type=diamond_type,
                category_id=category_id,
                unit_id=unit_id,
                movement_type=movement_type,
                quantity=quantity,
                price_per_unit_usd=price_per_unit,
                total_value_usd=total_value_usd,
                total_value_sar=total_value_sar,
                exchange_rate=exchange_rate,
                movement_date=datetime.now(),
                reference_type=reference_type,
                reference_id=reference_id,
                customer_id=customer_id,
                supplier_id=supplier_id,
                notes=notes,
                created_by=created_by
            )

            session.add(movement)
            log_info(f"تم إنشاء حركة مخزون جديدة: {movement_type} - {diamond_type} - {quantity}")
            return True
        else:
            # إنشاء session جديدة
            with session_scope() as new_session:
                return create_inventory_movement(
                    diamond_type, movement_type, quantity, price_per_unit,
                    category_id, unit_id, reference_type, reference_id,
                    customer_id, supplier_id, notes, created_by, new_session
                )

    except Exception as e:
        log_error(f"خطأ في إنشاء حركة المخزون: {str(e)}", e)
        return False
