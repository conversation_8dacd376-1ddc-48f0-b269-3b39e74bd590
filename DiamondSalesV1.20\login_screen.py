import socket
from PyQt6.QtWidgets import (Q<PERSON><PERSON>t, QVBoxLayout, QLabel, QLineEdit,
                            QPushButton, QGridLayout, QMessageBox, QComboBox,
                            QCheckBox, QHBoxLayout, QDialog, QFormLayout, QApplication)
from PyQt6.QtGui import QFont, QIcon
from PyQt6.QtCore import Qt
import os
import json

from database import User
from dashboard import DashboardWindow
from db_session import session_scope
from logger import log_error, log_info, log_login_attempt
from security import rate_limit
import app_state
from ui_utils import center_window
from translations import get_translation as _

# استيراد صفحة استعادة كلمة المرور الجديدة
try:
    from reset_password import ResetPasswordApp
except ImportError:
    # في حالة عدم وجود الملف
    ResetPasswordApp = None

class PasswordResetDialog(QDialog):
    """نافذة إعادة تعيين كلمة المرور"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("إعادة تعيين كلمة المرور")
        self.setMinimumWidth(400)
        self.setStyleSheet("background-color: #f0f0f0;")

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # إنشاء نموذج الإدخال
        form_layout = QFormLayout()

        # إنشاء الحقول
        self.username_input = QComboBox()
        self.username_input.setStyleSheet("padding: 8px;")

        # تحميل قائمة المستخدمين
        self.load_users()

        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("أدخل البريد الإلكتروني المرتبط بالحساب")
        self.email_input.setStyleSheet("padding: 8px;")

        # إضافة الحقول إلى النموذج
        form_layout.addRow("اسم المستخدم:", self.username_input)
        form_layout.addRow("البريد الإلكتروني:", self.email_input)

        # إنشاء أزرار التحكم
        button_layout = QHBoxLayout()

        self.reset_button = QPushButton("إعادة تعيين")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.reset_button.clicked.connect(self.reset_password)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.reset_button)

        # إضافة العناصر إلى التخطيط الرئيسي
        layout.addLayout(form_layout)
        layout.addLayout(button_layout)

        # تعيين اتجاه التخطيط من اليمين إلى اليسار
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    def load_users(self):
        """تحميل قائمة المستخدمين من قاعدة البيانات"""
        try:
            with session_scope() as session:
                # الحصول على جميع المستخدمين النشطين
                users = session.query(User).filter_by(is_active=True).all()

                # إضافة المستخدمين إلى القائمة المنسدلة
                for user in users:
                    self.username_input.addItem(user.username)
        except Exception as e:
            log_error(f"خطأ في تحميل قائمة المستخدمين: {str(e)}", e)
            QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء تحميل قائمة المستخدمين")

    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        username = self.username_input.currentText()
        email = self.email_input.text().strip()

        if not username:
            QMessageBox.warning(self, "خطأ", "الرجاء اختيار اسم المستخدم")
            return

        if not email:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال البريد الإلكتروني")
            return

        try:
            with session_scope() as session:
                # البحث عن المستخدم في قاعدة البيانات
                user = session.query(User).filter_by(username=username).first()

                if not user:
                    QMessageBox.warning(self, "خطأ", "اسم المستخدم غير موجود")
                    return

                # التحقق من البريد الإلكتروني
                if not user.email or user.email.lower() != email.lower():
                    QMessageBox.warning(self, "خطأ", "البريد الإلكتروني غير مطابق للمستخدم")
                    return

                # توليد كلمة مرور جديدة
                new_password = self.generate_random_password()

                # تعيين كلمة المرور الجديدة
                try:
                    user.set_password(new_password)
                    session.commit()

                    # عرض كلمة المرور الجديدة للمستخدم
                    QMessageBox.information(
                        self,
                        "تم إعادة تعيين كلمة المرور",
                        f"تم إعادة تعيين كلمة المرور بنجاح.\n\nكلمة المرور الجديدة هي:\n{new_password}\n\nيرجى تغييرها بعد تسجيل الدخول."
                    )

                    log_info(f"تم إعادة تعيين كلمة المرور للمستخدم {username}")

                    # إغلاق النافذة
                    self.accept()

                except ValueError as ve:
                    # خطأ في تعيين كلمة المرور (غير قوية)
                    QMessageBox.warning(self, "خطأ", str(ve))

        except Exception as e:
            log_error(f"خطأ في إعادة تعيين كلمة المرور: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إعادة تعيين كلمة المرور: {str(e)}")

    def generate_random_password(self):
        """توليد كلمة مرور عشوائية قوية"""
        import random
        import string

        # تعريف مجموعات الأحرف
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*"

        # التأكد من وجود حرف واحد على الأقل من كل مجموعة
        password = [
            random.choice(lowercase),
            random.choice(uppercase),
            random.choice(digits),
            random.choice(special)
        ]

        # إضافة 6 أحرف عشوائية أخرى
        all_chars = lowercase + uppercase + digits + special
        password.extend(random.choice(all_chars) for _ in range(6))

        # خلط الأحرف
        random.shuffle(password)

        return ''.join(password)

class LoginWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle(_("login_title", "نظام إدارة مبيعات الألماس - تسجيل الدخول"))
        self.setGeometry(300, 300, 400, 250)
        self.setWindowIcon(QIcon('assets/diamond_icon.png'))
        self.setStyleSheet("background-color: #f0f0f0;")

        # Create main layout
        main_layout = QVBoxLayout()
        form_layout = QGridLayout()

        # Create labels and input fields
        title_label = QLabel(_("system_title", "نظام إدارة مبيعات الألماس"))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")

        username_label = QLabel(_("username_label", "اسم المستخدم:"))
        username_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        # Crear un ComboBox para seleccionar el usuario en lugar de un LineEdit
        self.username_input = QComboBox()
        self.username_input.setStyleSheet("padding: 8px;")

        # Cargar la lista de usuarios desde la base de datos
        self.load_users()

        password_label = QLabel(_("password_label", "كلمة المرور:"))
        password_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        # إنشاء حقل كلمة المرور مع زر لإظهار/إخفاء كلمة المرور
        password_container = QWidget()
        password_layout = QHBoxLayout(password_container)
        password_layout.setContentsMargins(0, 0, 0, 0)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setStyleSheet("padding: 8px;")

        # إضافة زر لإظهار/إخفاء كلمة المرور
        self.toggle_password_button = QPushButton()
        self.toggle_password_button.setIcon(QIcon("assets/eye_closed.png"))
        self.toggle_password_button.setFixedSize(30, 30)
        self.toggle_password_button.setStyleSheet("""
            QPushButton {
                border: none;
                background-color: transparent;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        self.toggle_password_button.clicked.connect(self.toggle_password_visibility)

        password_layout.addWidget(self.password_input)
        password_layout.addWidget(self.toggle_password_button)

        # إضافة خيار "تذكرني"
        self.remember_me = QCheckBox("تذكرني")
        self.remember_me.setStyleSheet("margin-top: 5px;")

        # إضافة زر "نسيت كلمة المرور"
        self.forgot_password_button = QPushButton("نسيت كلمة المرور؟")
        self.forgot_password_button.setStyleSheet("""
            QPushButton {
                background: none;
                border: none;
                color: #3498db;
                text-decoration: underline;
                text-align: left;
            }
            QPushButton:hover {
                color: #2980b9;
            }
        """)
        self.forgot_password_button.clicked.connect(self.forgot_password)

        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.login_button.clicked.connect(self.login)

        # Add widgets to layouts
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_input, 0, 1)
        form_layout.addWidget(password_label, 1, 0)
        form_layout.addWidget(password_container, 1, 1)
        form_layout.addWidget(self.remember_me, 2, 1, Qt.AlignmentFlag.AlignRight)

        # إضافة زر نسيت كلمة المرور في صف منفصل
        forgot_layout = QHBoxLayout()
        forgot_layout.addStretch()
        forgot_layout.addWidget(self.forgot_password_button)

        main_layout.addWidget(title_label)
        main_layout.addLayout(form_layout)
        main_layout.addLayout(forgot_layout)
        main_layout.addWidget(self.login_button)

        # تحميل بيانات تذكر المستخدم إذا كانت موجودة
        self.load_remembered_user()

        # Set window layout
        self.setLayout(main_layout)

        # Set RTL layout direction for Arabic
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    def load_users(self):
        """تحميل قائمة المستخدمين من قاعدة البيانات"""
        try:
            with session_scope() as session:
                # الحصول على جميع المستخدمين النشطين
                users = session.query(User).filter_by(is_active=True).all()

                # إضافة المستخدمين إلى القائمة المنسدلة
                for user in users:
                    self.username_input.addItem(user.username)

            log_info("تم تحميل قائمة المستخدمين بنجاح")
        except Exception as e:
            log_error("خطأ في تحميل قائمة المستخدمين", e)
            QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء تحميل قائمة المستخدمين")

    def load_remembered_user(self):
        """تحميل بيانات المستخدم المحفوظة"""
        try:
            # التحقق من وجود ملف بيانات تذكر المستخدم
            if os.path.exists('remember_me.json'):
                with open('remember_me.json', 'r') as f:
                    data = json.load(f)

                    # التحقق من صحة البيانات
                    if 'username' in data:
                        # تعيين اسم المستخدم في القائمة المنسدلة
                        index = self.username_input.findText(data['username'])
                        if index >= 0:
                            self.username_input.setCurrentIndex(index)

                            # تحديد خيار تذكرني
                            self.remember_me.setChecked(True)

                            # التحقق من وجود رمز تذكر
                            if 'token' in data:
                                # التحقق من صحة الرمز
                                with session_scope() as session:
                                    user = session.query(User).filter_by(username=data['username']).first()
                                    if user and user.remember_token == data['token']:
                                        # تركيز على زر تسجيل الدخول
                                        self.login_button.setFocus()
                                        return

                # إذا وصلنا إلى هنا، فإن البيانات غير صالحة
                self.clear_remembered_user()

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المستخدم المحفوظة: {str(e)}", e)
            self.clear_remembered_user()

    def save_remembered_user(self, username, token):
        """حفظ بيانات المستخدم للتذكر"""
        try:
            data = {
                'username': username,
                'token': token
            }

            with open('remember_me.json', 'w') as f:
                json.dump(data, f)

            log_info(f"تم حفظ بيانات تذكر المستخدم {username}")
        except Exception as e:
            log_error(f"خطأ في حفظ بيانات تذكر المستخدم: {str(e)}", e)

    def clear_remembered_user(self):
        """مسح بيانات المستخدم المحفوظة"""
        try:
            if os.path.exists('remember_me.json'):
                os.remove('remember_me.json')
                log_info("تم مسح بيانات تذكر المستخدم")
        except Exception as e:
            log_error(f"خطأ في مسح بيانات تذكر المستخدم: {str(e)}", e)

    def toggle_password_visibility(self):
        """تبديل حالة إظهار/إخفاء كلمة المرور"""
        if self.password_input.echoMode() == QLineEdit.EchoMode.Password:
            # إظهار كلمة المرور
            self.password_input.setEchoMode(QLineEdit.EchoMode.Normal)
            self.toggle_password_button.setIcon(QIcon("assets/eye_open.png"))
        else:
            # إخفاء كلمة المرور
            self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.toggle_password_button.setIcon(QIcon("assets/eye_closed.png"))

    def forgot_password(self):
        """فتح نافذة إعادة تعيين كلمة المرور"""
        try:
            # استخدام نافذة إعادة تعيين كلمة المرور
            dialog = PasswordResetDialog(self)
            result = dialog.exec()
            
            if result == QDialog.DialogCode.Accepted:
                log_info("تم إكمال عملية إعادة تعيين كلمة المرور بنجاح")
            else:
                log_info("تم إلغاء عملية إعادة تعيين كلمة المرور")
                
        except Exception as e:
            error_msg = f"خطأ في فتح نافذة استعادة كلمة المرور: {str(e)}"
            log_error(error_msg, e)
            QMessageBox.critical(self, "خطأ", error_msg)

    def login(self):
        """تسجيل الدخول إلى النظام"""
        username = self.username_input.currentText()
        password = self.password_input.text()

        # الحصول على عنوان IP للمستخدم (للتسجيل فقط)
        try:
            hostname = socket.gethostname()
            ip_address = socket.gethostbyname(hostname)
        except:
            ip_address = "غير معروف"

        if not username or not password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            log_login_attempt(username, False, ip_address, "بيانات غير مكتملة")
            return

        try:
            with session_scope() as session:
                # البحث عن المستخدم في قاعدة البيانات
                user = session.query(User).filter_by(username=username).first()

                if not user:
                    QMessageBox.warning(self, "خطأ", "اسم المستخدم غير موجود")
                    log_login_attempt(username, False, ip_address, "اسم المستخدم غير موجود")
                    return

                if not user.is_active:
                    QMessageBox.warning(self, "خطأ", "هذا الحساب معطل")
                    log_login_attempt(username, False, ip_address, "الحساب معطل")
                    return

                # التحقق من كلمة المرور
                if user.check_password(password):
                    # تسجيل الدخول ناجح
                    log_login_attempt(username, True, ip_address)
                    log_info(f"تم تسجيل دخول المستخدم {username} بنجاح")

                    # تحديث وقت آخر تسجيل دخول
                    user.update_last_login()

                    # إذا تم تحديد خيار "تذكرني"
                    if self.remember_me.isChecked():
                        # توليد رمز تذكر جديد
                        token = user.generate_remember_token()
                        # حفظ بيانات المستخدم
                        self.save_remembered_user(username, token)
                    else:
                        # مسح بيانات المستخدم المحفوظة
                        self.clear_remembered_user()
                        # مسح رمز التذكر من قاعدة البيانات
                        user.clear_remember_token()

                    session.commit()

                    # تعيين المستخدم الحالي في حالة التطبيق
                    app_state.set_current_user(username=username)

                    self.hide()
                    self.dashboard = DashboardWindow()
                    center_window(self.dashboard)
                    self.dashboard.show()
                else:
                    # زيادة عدد محاولات تسجيل الدخول الفاشلة
                    user.increment_failed_login()
                    session.commit()

                    # التحقق من عدد محاولات تسجيل الدخول الفاشلة
                    if user.failed_login_attempts >= 5:
                        warning_msg = "تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى الاتصال بالمسؤول."
                        QMessageBox.warning(self, "تحذير أمني", warning_msg)
                        log_login_attempt(username, False, ip_address, f"تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول ({user.failed_login_attempts})")
                    else:
                        QMessageBox.warning(self, "خطأ", "كلمة المرور غير صحيحة")
                        log_login_attempt(username, False, ip_address, f"كلمة المرور غير صحيحة (المحاولة رقم {user.failed_login_attempts})")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء تسجيل الدخول: {str(e)}"
            log_error(error_msg, e)
            log_login_attempt(username, False, ip_address, f"خطأ في النظام: {str(e)}")
            QMessageBox.critical(self, "خطأ في تسجيل الدخول", error_msg)