"""
سكريبت لترقية قاعدة البيانات
"""

import sqlite3
import os
import shutil
from datetime import datetime
from logger import log_error, log_info

def backup_database():
    """
    إنشاء نسخة احتياطية من قاعدة البيانات قبل الترقية

    Returns:
        bool: True إذا تم إنشاء النسخة الاحتياطية بنجاح، False خلاف ذلك
    """
    try:
        # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجودًا
        if not os.path.exists('backups'):
            os.makedirs('backups')

        # إنشاء اسم الملف بالتاريخ والوقت الحاليين
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join('backups', f"diamond_sales_backup_before_migration_{timestamp}.db")

        # نسخ قاعدة البيانات إلى الملف الجديد
        shutil.copy2("diamond_sales.db", backup_file)

        log_info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات في: {backup_file}")
        return True
    except Exception as e:
        log_error(f"خطأ في إنشاء نسخة احتياطية من قاعدة البيانات: {str(e)}", e)
        return False

def migrate_database():
    """
    ترقية قاعدة البيانات

    Returns:
        bool: True إذا تمت الترقية بنجاح، False خلاف ذلك
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()

        # التحقق من وجود الأعمدة الجديدة في جدول المستخدمين
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]

        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        if 'full_name' not in columns:
            log_info("إضافة عمود full_name إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN full_name TEXT")

        if 'email' not in columns:
            log_info("إضافة عمود email إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN email TEXT")

        if 'phone' not in columns:
            log_info("إضافة عمود phone إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN phone TEXT")

        if 'last_login' not in columns:
            log_info("إضافة عمود last_login إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN last_login TIMESTAMP")

        if 'created_at' not in columns:
            log_info("إضافة عمود created_at إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN created_at TIMESTAMP")
            # تحديث القيم الحالية
            cursor.execute("UPDATE users SET created_at = datetime('now') WHERE created_at IS NULL")

        if 'updated_at' not in columns:
            log_info("إضافة عمود updated_at إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN updated_at TIMESTAMP")
            # تحديث القيم الحالية
            cursor.execute("UPDATE users SET updated_at = datetime('now') WHERE updated_at IS NULL")

        if 'failed_login_attempts' not in columns:
            log_info("إضافة عمود failed_login_attempts إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN failed_login_attempts INTEGER DEFAULT 0")

        if 'remember_token' not in columns:
            log_info("إضافة عمود remember_token إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN remember_token TEXT")

        if 'reset_password_token' not in columns:
            log_info("إضافة عمود reset_password_token إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN reset_password_token TEXT")

        if 'reset_password_expires' not in columns:
            log_info("إضافة عمود reset_password_expires إلى جدول users")
            cursor.execute("ALTER TABLE users ADD COLUMN reset_password_expires TIMESTAMP")

        # إنشاء فهرس على اسم المستخدم إذا لم يكن موجودًا
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_username'")
        if not cursor.fetchone():
            log_info("إنشاء فهرس idx_username على جدول users")
            cursor.execute("CREATE INDEX idx_username ON users (username)")

        # إنشاء فهرس على رمز إعادة تعيين كلمة المرور إذا لم يكن موجودًا
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_reset_token'")
        if not cursor.fetchone():
            log_info("إنشاء فهرس idx_reset_token على جدول users")
            cursor.execute("CREATE INDEX idx_reset_token ON users (reset_password_token)")

        # حفظ التغييرات
        conn.commit()

        # إغلاق الاتصال
        conn.close()

        log_info("تمت ترقية قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        log_error(f"خطأ في ترقية قاعدة البيانات: {str(e)}", e)
        return False

if __name__ == "__main__":
    print("=== أداة ترقية قاعدة البيانات ===")
    print("سيتم ترقية قاعدة البيانات لتتوافق مع الإصدار الجديد من البرنامج.")
    print("سيتم إنشاء نسخة احتياطية قبل المتابعة.")

    confirmation = input("هل أنت متأكد من أنك تريد المتابعة؟ (ن/لا): ")

    if confirmation.lower() not in ['ن', 'نعم', 'y', 'yes']:
        print("تم إلغاء العملية.")
    else:
        # إنشاء نسخة احتياطية
        if backup_database():
            # ترقية قاعدة البيانات
            if migrate_database():
                print("\nتمت ترقية قاعدة البيانات بنجاح.")
                print("يمكنك الآن تشغيل البرنامج.")
            else:
                print("فشلت عملية ترقية قاعدة البيانات. يرجى مراجعة سجل الأخطاء.")
        else:
            print("تعذر إنشاء نسخة احتياطية. تم إلغاء العملية.")
