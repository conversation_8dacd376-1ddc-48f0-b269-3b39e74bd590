import sys
import os
import webbrowser
from datetime import datetime
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                             QTableWidget, QTableWidgetItem, QPushButton, QHeaderView, QMessageBox,
                             QComboBox, QDialog, QFormLayout, QDialogButtonBox, QSizePolicy,
                             QDoubleSpinBox, QDateEdit, QTextEdit, QFileDialog)
from PyQt6.QtCore import Qt, pyqtSignal, QDate
from PyQt6.QtGui import QIcon, QFont
from database import Category, Unit, OpeningBalance
from db_session import session_scope
from logger import log_error, log_info
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from ui_utils import center_window, style_dialog_buttons
from translations import get_translation as _
try:
    import xlsxwriter
    XLSXWRITER_AVAILABLE = True
except ImportError:
    XLSXWRITER_AVAILABLE = False
    log_error("xlsxwriter not available. Excel export functionality will be disabled.")

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    log_error("openpyxl not available. Excel import functionality will be disabled.")

# استخدام نموذج قاعدة البيانات المُعرَّف في database.py

class OpeningBalanceDialog(QDialog):
    """حوار إضافة/تعديل الأرصدة الافتتاحية"""
    
    def __init__(self, parent=None, opening_balance=None, exchange_rate=3.75):
        super().__init__(parent)
        self.opening_balance = opening_balance
        self.exchange_rate = exchange_rate
        self.init_ui()
        
        if opening_balance:
            self.load_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إضافة/تعديل رصيد افتتاحي" if not self.opening_balance else "تعديل رصيد افتتاحي")
        self.setModal(True)
        self.setMinimumSize(500, 400)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # تعيين الخط
        font = QFont()
        font.setPointSize(10)
        font.setBold(True)
        self.setFont(font)
        
        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout()
        
        # إنشاء نموذج الإدخال
        form_layout = QFormLayout()
        
        # الصنف
        self.category_combo = QComboBox()
        self.load_categories()
        self.category_combo.currentIndexChanged.connect(self.on_category_changed)
        form_layout.addRow("الصنف:", self.category_combo)
        
        # الوحدة
        self.unit_combo = QComboBox()
        self.load_units()
        form_layout.addRow("الوحدة:", self.unit_combo)
        
        # الكمية (بالقيراط)
        self.quantity_spinbox = QDoubleSpinBox()
        self.quantity_spinbox.setDecimals(3)
        self.quantity_spinbox.setMaximum(999999.999)
        self.quantity_spinbox.setSuffix("")
        self.quantity_spinbox.valueChanged.connect(self.calculate_totals)
        form_layout.addRow("الكمية:", self.quantity_spinbox)
        
        # سعر القيراط بالدولار
        self.price_per_carat_spinbox = QDoubleSpinBox()
        self.price_per_carat_spinbox.setDecimals(3)
        self.price_per_carat_spinbox.setMaximum(999999.999)
        self.price_per_carat_spinbox.setSuffix(" $")
        self.price_per_carat_spinbox.valueChanged.connect(self.calculate_totals)
        form_layout.addRow("سعر الوحدة ($):", self.price_per_carat_spinbox)
        
        # سعر الصرف
        self.exchange_rate_spinbox = QDoubleSpinBox()
        self.exchange_rate_spinbox.setDecimals(4)
        self.exchange_rate_spinbox.setMaximum(999.9999)
        self.exchange_rate_spinbox.setValue(self.exchange_rate)
        self.exchange_rate_spinbox.setSuffix(" ريال")
        self.exchange_rate_spinbox.valueChanged.connect(self.calculate_totals)
        form_layout.addRow("سعر الصرف:", self.exchange_rate_spinbox)
        
        # سعر الوحدة بالريال (للقراءة فقط)
        self.price_per_carat_sar_label = QLabel("0.000 ريال")
        self.price_per_carat_sar_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc; }")
        form_layout.addRow("سعر الوحدة (ريال):", self.price_per_carat_sar_label)
        
        # إجمالي القيمة بالدولار (للقراءة فقط)
        self.total_usd_label = QLabel("0.000 $")
        self.total_usd_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc; }")
        form_layout.addRow("إجمالي القيمة ($):", self.total_usd_label)
        
        # إجمالي القيمة بالريال (للقراءة فقط)
        self.total_sar_label = QLabel("0.000 ريال")
        self.total_sar_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc; }")
        form_layout.addRow("إجمالي القيمة (ريال):", self.total_sar_label)
        
        # تاريخ الإنشاء
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("التاريخ:", self.date_edit)
        
        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        form_layout.addRow("الملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار الحوار
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # تخصيص النصوص
        button_box.button(QDialogButtonBox.StandardButton.Ok).setText(_("save", "حفظ"))
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText(_("cancel", "إلغاء"))
        
        # تطبيق التنسيق
        style_dialog_buttons(button_box)
        
        layout.addWidget(button_box)
        
        self.setLayout(layout)
        center_window(self)
    
    def load_categories(self):
        """تحميل الأصناف"""
        try:
            with session_scope() as session:
                categories = session.query(Category).all()
                self.category_combo.addItem("-- اختر الصنف --", None)
                for category in categories:
                    self.category_combo.addItem(category.name, category.id)
        except Exception as e:
            log_error(f"خطأ في تحميل الأصناف: {str(e)}", e)
    
    def load_units(self, category_id=None):
        """تحميل الوحدات المرتبطة بالصنف المختار"""
        try:
            with session_scope() as session:
                # تحميل الوحدات المرتبطة بالصنف المحدد
                if category_id:
                    units = session.query(Unit).filter_by(category_id=category_id).all()
                else:
                    units = session.query(Unit).all()
                
                # مسح القائمة وإضافة الوحدات الجديدة
                self.unit_combo.clear()
                self.unit_combo.addItem("-- اختر الوحدة --", None)
                for unit in units:
                    display_text = f"{unit.name}"
                    if unit.symbol:
                        display_text += f" ({unit.symbol})"
                    self.unit_combo.addItem(display_text, unit.id)
                
                # إرجاع عدد الوحدات التي تم تحميلها
                return len(units)
        except Exception as e:
            log_error(f"خطأ في تحميل الوحدات: {str(e)}", e)
            return 0
    

    def on_category_changed(self, index):
        """تحديث الوحدات عند تغيير الصنف"""
        category_id = self.category_combo.currentData()
        
        # تحميل الوحدات المرتبطة بالصنف
        units_count = self.load_units(category_id)
        
        # اختيار الوحدة تلقائياً إذا كان هناك وحدة واحدة فقط مرتبطة بالصنف
        if category_id and units_count == 1:
            self.unit_combo.setCurrentIndex(1)  # اختيار الوحدة الوحيدة
     
    def calculate_totals(self):
        """حساب الإجماليات"""
        quantity = self.quantity_spinbox.value()
        price_per_carat = self.price_per_carat_spinbox.value()
        exchange_rate = self.exchange_rate_spinbox.value()
        
        price_per_carat_sar = price_per_carat * exchange_rate
        total_usd = quantity * price_per_carat
        total_sar = total_usd * exchange_rate
        
        self.price_per_carat_sar_label.setText(f"{price_per_carat_sar:.3f} ريال")
        self.total_usd_label.setText(f"{total_usd:.3f} $")
        self.total_sar_label.setText(f"{total_sar:.3f} ريال")
    
    def load_data(self):
        """تحميل البيانات للتعديل"""
        if self.opening_balance:
            self.quantity_spinbox.setValue(self.opening_balance['quantity'])
            self.price_per_carat_spinbox.setValue(self.opening_balance['price_per_carat_usd'])
            self.exchange_rate_spinbox.setValue(self.opening_balance['exchange_rate'])
            
            # تعيين الصنف
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == self.opening_balance['category_id']:
                    self.category_combo.setCurrentIndex(i)
                    # تحديث الوحدات المرتبطة بالصنف
                    self.on_category_changed(i)
                    break
            
            # تعيين الوحدة بعد تحديث قائمة الوحدات
            for i in range(self.unit_combo.count()):
                if self.unit_combo.itemData(i) == self.opening_balance['unit_id']:
                    self.unit_combo.setCurrentIndex(i)
                    break
            
            if self.opening_balance['date_created']:
                # التعامل مع التاريخ سواء كان string أو datetime object
                if isinstance(self.opening_balance['date_created'], str):
                    self.date_edit.setDate(QDate.fromString(self.opening_balance['date_created'], "yyyy-MM-dd"))
                else:
                    self.date_edit.setDate(QDate.fromString(self.opening_balance['date_created'].strftime("%Y-%m-%d"), "yyyy-MM-dd"))
            
            self.notes_edit.setPlainText(self.opening_balance['notes'] or "")
            
            # حساب الإجماليات
            self.calculate_totals()
    
    def get_data(self):
        """الحصول على البيانات من النموذج"""
        quantity = self.quantity_spinbox.value()
        price_per_carat = self.price_per_carat_spinbox.value()
        exchange_rate = self.exchange_rate_spinbox.value()
        
        return {
            'quantity': quantity,
            'price_per_carat_usd': price_per_carat,
            'total_value_usd': quantity * price_per_carat,
            'total_value_sar': quantity * price_per_carat * exchange_rate,
            'exchange_rate': exchange_rate,
            'diamond_type': self.category_combo.currentText(),
            'category_id': self.category_combo.currentData(),
            'unit_id': self.unit_combo.currentData(),
            'date_created': self.date_edit.date().toPyDate(),
            'notes': self.notes_edit.toPlainText()
        }

class OpeningBalancesScreen(QWidget):
    """شاشة إدارة الأرصدة الافتتاحية للأصناف"""
    
    # إشارة لتحديث البيانات في الشاشات الأخرى
    data_updated = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.opening_balances = []  # قائمة الأرصدة الافتتاحية
        self.exchange_rate = 3.75  # سعر الصرف الافتراضي
        self.init_ui()
        self.load_exchange_rate()
        self.load_opening_balances()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # تعيين عنوان النافذة وحجمها
        self.setWindowTitle(_("opening_balances_title", "نظام إدارة مبيعات الألماس - الأرصدة الافتتاحية للأصناف"))
        self.setGeometry(100, 100, 1000, 700)
        self.setMinimumSize(1000, 700)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # تعيين الخط للواجهة بشكل عام
        font = QFont()
        font.setPointSize(10)
        font.setBold(True)
        self.setFont(font)
        
        # إنشاء تخطيط رئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # عنوان الشاشة
        title_label = QLabel("الأرصدة الافتتاحية للأصناف")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("QLabel { color: #2c3e50; padding: 10px; }")
        main_layout.addWidget(title_label)
        
        # إنشاء أزرار الإجراءات
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(20)
        actions_layout.setContentsMargins(5, 5, 5, 15)
        
        # تنسيق الأزرار
        btn_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12pt;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """
        
        self.add_btn = QPushButton("إضافة رصيد افتتاحي")
        self.add_btn.clicked.connect(self.add_opening_balance)
        self.add_btn.setMinimumWidth(180)
        self.add_btn.setMinimumHeight(45)
        self.add_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.add_btn.setStyleSheet(btn_style.replace("3498db", "27ae60"))
        
        self.edit_btn = QPushButton("تعديل الرصيد")
        self.edit_btn.clicked.connect(self.edit_opening_balance)
        self.edit_btn.setMinimumWidth(150)
        self.edit_btn.setMinimumHeight(45)
        self.edit_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.edit_btn.setStyleSheet(btn_style)
        
        self.delete_btn = QPushButton("حذف الرصيد")
        self.delete_btn.clicked.connect(self.delete_opening_balance)
        self.delete_btn.setMinimumWidth(150)
        self.delete_btn.setMinimumHeight(45)
        self.delete_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.delete_btn.setStyleSheet(btn_style.replace("3498db", "e74c3c"))
        
        self.refresh_btn = QPushButton("تحديث البيانات")
        self.refresh_btn.clicked.connect(self.load_opening_balances)
        self.refresh_btn.setMinimumWidth(150)
        self.refresh_btn.setMinimumHeight(45)
        self.refresh_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.refresh_btn.setStyleSheet(btn_style.replace("3498db", "f39c12"))
        
        # أزرار الطباعة والتصدير والاستيراد
        self.print_btn = QPushButton("طباعة التقرير")
        self.print_btn.clicked.connect(self.print_report)
        self.print_btn.setMinimumWidth(150)
        self.print_btn.setMinimumHeight(45)
        self.print_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.print_btn.setStyleSheet(btn_style.replace("3498db", "9b59b6"))
        
        self.export_btn = QPushButton("تصدير Excel")
        self.export_btn.clicked.connect(self.export_to_excel)
        self.export_btn.setMinimumWidth(150)
        self.export_btn.setMinimumHeight(45)
        self.export_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.export_btn.setStyleSheet(btn_style.replace("3498db", "16a085"))
        self.export_btn.setEnabled(XLSXWRITER_AVAILABLE)
        
        self.import_btn = QPushButton("استيراد Excel")
        self.import_btn.clicked.connect(self.import_from_excel)
        self.import_btn.setMinimumWidth(150)
        self.import_btn.setMinimumHeight(45)
        self.import_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.import_btn.setStyleSheet(btn_style.replace("3498db", "e67e22"))
        self.import_btn.setEnabled(OPENPYXL_AVAILABLE)
        
        actions_layout.addWidget(self.add_btn)
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addWidget(self.refresh_btn)
        
        # إضافة صف ثاني للأزرار الجديدة
        actions_layout2 = QHBoxLayout()
        actions_layout2.setSpacing(20)
        actions_layout2.setContentsMargins(5, 5, 5, 15)
        
        actions_layout2.addWidget(self.print_btn)
        actions_layout2.addWidget(self.export_btn)
        actions_layout2.addWidget(self.import_btn)
        
        main_layout.addLayout(actions_layout)
        main_layout.addLayout(actions_layout2)
        
        # إنشاء جدول الأرصدة الافتتاحية
        self.table = QTableWidget()
        self.table.setColumnCount(10)
        self.table.setHorizontalHeaderLabels([
            "الرقم", "الكمية", "سعر القيراط ($)",
            "سعر القيراط (ريال)", "إجمالي القيمة ($)", "سعر الصرف", 
            "إجمالي القيمة (ريال)", "الصنف", "الوحدة", "التاريخ"
        ])
        
        # تخصيص خط لرأس الجدول
        header_font = QFont()
        header_font.setPointSize(10)
        header_font.setBold(True)
        self.table.horizontalHeader().setFont(header_font)
        
        # تعيين خط للجدول
        table_font = QFont()
        table_font.setPointSize(10)
        self.table.setFont(table_font)
        
        # ضبط حجم الأعمدة
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        # تلوين الصفوف بالتناوب
        self.table.setAlternatingRowColors(True)
        self.table.setStyleSheet("QTableWidget { gridline-color: #d0d0d0; alternate-background-color: #f5f5f5; }")
        
        # ضبط ارتفاع الصفوف
        self.table.verticalHeader().setDefaultSectionSize(30)
        self.table.verticalHeader().setVisible(True)
        
        # تمكين التحديد للصف الكامل
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        main_layout.addWidget(self.table)
        
        # إضافة ملصقات الإجماليات
        totals_layout = QHBoxLayout()
        
        self.total_quantity_label = QLabel("إجمالي الكمية: 0.000")
        self.total_value_usd_label = QLabel("إجمالي القيمة: 0.000 $")
        self.total_value_sar_label = QLabel("إجمالي القيمة: 0.000 ريال")
        
        # تنسيق ملصقات الإجماليات
        total_style = "QLabel { background-color: #ecf0f1; padding: 8px; border: 1px solid #bdc3c7; font-weight: bold; }"
        self.total_quantity_label.setStyleSheet(total_style)
        self.total_value_usd_label.setStyleSheet(total_style)
        self.total_value_sar_label.setStyleSheet(total_style)
        
        totals_layout.addWidget(self.total_quantity_label)
        totals_layout.addWidget(self.total_value_usd_label)
        totals_layout.addWidget(self.total_value_sar_label)
        
        main_layout.addLayout(totals_layout)
        
        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)
        
        # توسيط النافذة في الشاشة
        center_window(self)
    
    def load_exchange_rate(self):
        """تحميل سعر الصرف من الإعدادات"""
        try:
            from database import Setting
            with session_scope() as session:
                setting = session.query(Setting).first()
                if setting:
                    self.exchange_rate = setting.exchange_rate
        except Exception as e:
            log_error(f"خطأ في تحميل سعر الصرف: {str(e)}", e)
            self.exchange_rate = 3.75  # القيمة الافتراضية
    
    def load_opening_balances(self):
        """تحميل الأرصدة الافتتاحية من قاعدة البيانات"""
        try:
            with session_scope() as session:
                balances = session.query(OpeningBalance).all()
                # تحويل البيانات إلى قواميس لتجنب مشكلة detached instances
                self.opening_balances = []
                for balance in balances:
                    balance_dict = {
                        'id': balance.id,
                        'diamond_type': balance.diamond_type,
                        'quantity': balance.quantity,
                        'price_per_carat_usd': balance.price_per_carat_usd,
                        'total_value_usd': balance.total_value_usd,
                        'total_value_sar': balance.total_value_sar,
                        'exchange_rate': balance.exchange_rate,
                        'category_id': balance.category_id,
                        'unit_id': balance.unit_id,
                        'date_created': balance.date_created.strftime("%Y-%m-%d") if balance.date_created else None,
                        'notes': balance.notes
                    }
                    self.opening_balances.append(balance_dict)
            
            # تحديث الجدول
            self.update_table()
            
            log_info("تم تحميل الأرصدة الافتتاحية بنجاح")
            
        except Exception as e:
            log_error(f"خطأ في تحميل الأرصدة الافتتاحية: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الأرصدة الافتتاحية: {str(e)}")
    
    def update_table(self):
        """تحديث جدول الأرصدة الافتتاحية"""
        try:
            self.table.setRowCount(len(self.opening_balances))
            
            total_quantity = 0
            total_value_usd = 0
            total_value_sar = 0
            
            for row, balance in enumerate(self.opening_balances):
                # حساب سعر الوحدة بالريال
                price_per_carat_sar = balance['price_per_carat_usd'] * balance['exchange_rate']
                
                self.table.setItem(row, 0, QTableWidgetItem(str(balance['id'] or row + 1)))
                self.table.setItem(row, 1, QTableWidgetItem(f"{balance['quantity']:.3f}"))
                self.table.setItem(row, 2, QTableWidgetItem(f"{balance['price_per_carat_usd']:.3f}"))
                self.table.setItem(row, 3, QTableWidgetItem(f"{price_per_carat_sar:.3f}"))
                self.table.setItem(row, 4, QTableWidgetItem(f"{balance['total_value_usd']:.3f}"))
                self.table.setItem(row, 5, QTableWidgetItem(f"{balance['exchange_rate']:.4f}"))
                self.table.setItem(row, 6, QTableWidgetItem(f"{balance['total_value_sar']:.3f}"))
                
                # الحصول على أسماء الصنف والوحدة
                category_name = self.get_category_name(balance['category_id'])
                unit_name = self.get_unit_name(balance['unit_id'])
                
                self.table.setItem(row, 7, QTableWidgetItem(category_name))
                self.table.setItem(row, 8, QTableWidgetItem(unit_name))
                self.table.setItem(row, 9, QTableWidgetItem(balance['date_created'] or ""))
                
                # تحديث الإجماليات
                total_quantity += balance['quantity']
                total_value_usd += balance['total_value_usd']
                total_value_sar += balance['total_value_sar']
            
            # تحديث ملصقات الإجماليات
            self.total_quantity_label.setText(f"إجمالي الكمية: {total_quantity:.3f}")
            self.total_value_usd_label.setText(f"إجمالي القيمة: {total_value_usd:.3f} $")
            self.total_value_sar_label.setText(f"إجمالي القيمة: {total_value_sar:.3f} ريال")
            
        except Exception as e:
            log_error(f"خطأ في تحديث الجدول: {str(e)}", e)
    
    def get_category_name(self, category_id):
        """الحصول على اسم الصنف"""
        if not category_id:
            return "-"
        try:
            with session_scope() as session:
                category = session.query(Category).filter_by(id=category_id).first()
                return category.name if category else "-"
        except:
            return "-"
    
    def get_unit_name(self, unit_id):
        """الحصول على اسم الوحدة"""
        if not unit_id:
            return "-"
        try:
            with session_scope() as session:
                unit = session.query(Unit).filter_by(id=unit_id).first()
                return unit.name if unit else "-"
        except:
            return "-"
    
    def add_opening_balance(self):
        """إضافة رصيد افتتاحي جديد"""
        try:
            dialog = OpeningBalanceDialog(self, exchange_rate=self.exchange_rate)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                data = dialog.get_data()
                
                # حفظ في قاعدة البيانات
                with session_scope() as session:
                    # إنشاء رصيد افتتاحي جديد داخل الجلسة
                    new_balance = OpeningBalance(
                        diamond_type=data['diamond_type'],
                        quantity=data['quantity'],
                        price_per_carat_usd=data['price_per_carat_usd'],
                        total_value_usd=data['total_value_usd'],
                        total_value_sar=data['total_value_sar'],
                        exchange_rate=data['exchange_rate'],
                        category_id=data['category_id'],
                        unit_id=data['unit_id'],
                        date_created=data['date_created'],
                        notes=data['notes']
                    )
                    session.add(new_balance)
                    # حفظ القيمة قبل انتهاء الجلسة
                    quantity_value = new_balance.quantity
                
                # إعادة تحميل البيانات
                self.load_opening_balances()
                
                # إرسال إشارة تحديث البيانات
                self.data_updated.emit()
                
                QMessageBox.information(self, "نجاح", "تم إضافة الرصيد الافتتاحي بنجاح")
                log_info(f"تم إضافة رصيد افتتاحي جديد بكمية: {quantity_value}")
                
        except Exception as e:
            log_error(f"خطأ في إضافة الرصيد الافتتاحي: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الرصيد الافتتاحي: {str(e)}")
    
    def edit_opening_balance(self):
        """تعديل رصيد افتتاحي"""
        try:
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار رصيد افتتاحي للتعديل")
                return
            
            balance = self.opening_balances[current_row]
            dialog = OpeningBalanceDialog(self, balance, self.exchange_rate)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                data = dialog.get_data()
                
                # تحديث البيانات في قاعدة البيانات
                with session_scope() as session:
                    db_balance = session.query(OpeningBalance).filter_by(id=balance['id']).first()
                    if db_balance:
                        db_balance.diamond_type = data['diamond_type']
                        db_balance.quantity = data['quantity']
                        db_balance.price_per_carat_usd = data['price_per_carat_usd']
                        db_balance.total_value_usd = data['total_value_usd']
                        db_balance.total_value_sar = data['total_value_sar']
                        db_balance.exchange_rate = data['exchange_rate']
                        db_balance.category_id = data['category_id']
                        db_balance.unit_id = data['unit_id']
                        db_balance.date_created = data['date_created']
                        db_balance.notes = data['notes']
                        session.commit()
                
                # إعادة تحميل البيانات
                self.load_opening_balances()
                
                # إرسال إشارة تحديث البيانات
                self.data_updated.emit()
                
                QMessageBox.information(self, "نجاح", "تم تعديل الرصيد الافتتاحي بنجاح")
                log_info(f"تم تعديل الرصيد الافتتاحي: {balance['diamond_type']}")
                
        except Exception as e:
            log_error(f"خطأ في تعديل الرصيد الافتتاحي: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل الرصيد الافتتاحي: {str(e)}")
    
    def delete_opening_balance(self):
        """حذف رصيد افتتاحي"""
        try:
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار رصيد افتتاحي للحذف")
                return
            
            balance = self.opening_balances[current_row]
            
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الرصيد الافتتاحي لـ '{balance['diamond_type']}'؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # حذف الرصيد الافتتاحي من قاعدة البيانات
                with session_scope() as session:
                    db_balance = session.query(OpeningBalance).filter_by(id=balance['id']).first()
                    if db_balance:
                        session.delete(db_balance)
                        session.commit()
                
                # إعادة تحميل البيانات
                self.load_opening_balances()
                
                # إرسال إشارة تحديث البيانات
                self.data_updated.emit()
                
                QMessageBox.information(self, "نجاح", "تم حذف الرصيد الافتتاحي بنجاح")
                log_info(f"تم حذف الرصيد الافتتاحي: {balance['diamond_type']}")
                
        except Exception as e:
            log_error(f"خطأ في حذف الرصيد الافتتاحي: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الرصيد الافتتاحي: {str(e)}")
    
    def get_opening_balances_for_report(self):
        """الحصول على الأرصدة الافتتاحية لاستخدامها في التقارير"""
        return self.opening_balances.copy()
    
    def print_report(self):
        """طباعة تقرير الأرصدة الافتتاحية"""
        try:
            # إنشاء اسم ملف للتقرير
            now = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"opening_balances_report_{now}.html"
            
            # الحصول على معلومات الشركة
            try:
                from database import CompanyInfo
                with session_scope() as session:
                    company_info = session.query(CompanyInfo).first()
                    company_name = company_info.name if company_info else "نظام إدارة مبيعات الألماس"
                    company_logo = company_info.logo_path if company_info and company_info.logo_path else ""
            except:
                company_name = "نظام إدارة مبيعات الألماس"
                company_logo = ""
            
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # إنشاء محتوى HTML
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير الأرصدة الافتتاحية</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
                    
                    body {{
                        font-family: 'Tajawal', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background-color: #f9f9f9;
                        color: #333;
                    }}
                    
                    .container {{
                        max-width: 1200px;
                        margin: 0 auto;
                        background-color: #fff;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                    }}
                    
                    .header {{
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }}
                    
                    .logo {{
                        max-width: 150px;
                        margin-bottom: 15px;
                    }}
                    
                    .title {{
                        font-size: 28px;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 10px;
                    }}
                    
                    .subtitle {{
                        font-size: 20px;
                        font-weight: 500;
                        color: #3498db;
                        margin: 10px 0;
                    }}
                    
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 25px 0;
                        font-size: 14px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
                        border-radius: 5px;
                        overflow: hidden;
                    }}
                    
                    th, td {{
                        padding: 12px 8px;
                        text-align: center;
                        border: 1px solid #ddd;
                    }}
                    
                    th {{
                        background-color: #3498db;
                        color: white;
                        font-weight: 500;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }}
                    
                    tr:nth-child(even) {{
                        background-color: #f2f2f2;
                    }}
                    
                    tr:hover {{
                        background-color: #e9f7fe;
                    }}
                    
                    .totals {{
                        margin-top: 30px;
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                    }}
                    
                    .total-row {{
                        display: flex;
                        justify-content: space-between;
                        padding: 8px 0;
                        border-bottom: 1px solid #eee;
                        font-size: 16px;
                    }}
                    
                    .total-row:last-child {{
                        border-bottom: none;
                        font-weight: 700;
                        font-size: 18px;
                        color: #2c3e50;
                    }}
                    
                    .total-label {{
                        font-weight: 700;
                    }}
                    
                    .footer {{
                        margin-top: 40px;
                        text-align: center;
                        font-size: 14px;
                        color: #95a5a6;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                    }}
                    
                    .print-button {{
                        background-color: #3498db;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        font-size: 16px;
                        border-radius: 5px;
                        cursor: pointer;
                        margin: 20px auto;
                        display: block;
                    }}
                    
                    .print-button:hover {{
                        background-color: #2980b9;
                    }}
                    
                    @media print {{
                        .print-button {{
                            display: none;
                        }}
                        body {{
                            background-color: white;
                        }}
                        .container {{
                            box-shadow: none;
                            padding: 0;
                        }}
                    }}
                </style>
                <script>
                    function printReport() {{
                        window.print();
                    }}
                </script>
            </head>
            <body>
                <div class="container">
                    <button onclick="printReport()" class="print-button">طباعة التقرير</button>
                    
                    <div class="header">
                        {f'<img src="{company_logo}" alt="Company Logo" class="logo">' if company_logo else ''}
                        <div class="title">{company_name}</div>
                        <div class="subtitle">تقرير الأرصدة الافتتاحية</div>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة ($)</th>
                                <th>سعر الوحدة (ريال)</th>
                                <th>إجمالي القيمة ($)</th>
                                <th>إجمالي القيمة (ريال)</th>
                                <th>سعر الصرف</th>
                                <th>الصنف</th>
                                <th>الوحدة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
            """
            
            # إضافة بيانات الجدول
            total_quantity = 0
            total_value_usd = 0
            total_value_sar = 0
            
            for i in range(self.table.rowCount()):
                html_content += "<tr>"
                for j in range(self.table.columnCount()):
                    item = self.table.item(i, j)
                    cell_text = item.text() if item else ""
                    html_content += f"<td>{cell_text}</td>"
                    
                    # حساب الإجماليات
                    if j == 1:  # الكمية
                        try:
                            total_quantity += float(cell_text)
                        except:
                            pass
                    elif j == 4:  # إجمالي القيمة ($)
                        try:
                            total_value_usd += float(cell_text)
                        except:
                            pass
                    elif j == 6:  # إجمالي القيمة (ريال)
                        try:
                            total_value_sar += float(cell_text)
                        except:
                            pass
                
                html_content += "</tr>"
            
            html_content += f"""
                        </tbody>
                    </table>
                    
                    <div class="totals">
                        <div class="total-row">
                            <div class="total-label">إجمالي الكمية:</div>
                            <div>{total_quantity:.3f}</div>
                        </div>
                        <div class="total-row">
                            <div class="total-label">إجمالي القيمة ($):</div>
                            <div>{total_value_usd:.3f} $</div>
                        </div>
                        <div class="total-row">
                            <div class="total-label">إجمالي القيمة (ريال):</div>
                            <div>{total_value_sar:.3f} ريال</div>
                        </div>
                    </div>
                    
                    <div class="footer">
                        <p>{company_name}</p>
                        <p>تم إنشاء هذا التقرير في {current_date}</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # كتابة محتوى HTML إلى ملف
            with open(report_filename, 'w', encoding='utf-8') as html_file:
                html_file.write(html_content)
            
            # فتح الملف في المتصفح
            file_path = os.path.abspath(report_filename)
            webbrowser.open('file://' + file_path, new=2)
            
            # رسالة نجاح
            QMessageBox.information(self, "نجاح", "تم إنشاء تقرير الأرصدة الافتتاحية بنجاح وفتحه في المتصفح")
            
            log_info("تم طباعة تقرير الأرصدة الافتتاحية")
            
        except Exception as e:
            log_error(f"خطأ في طباعة التقرير: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")
    
    def export_to_excel(self):
        """تصدير البيانات إلى ملف Excel"""
        if not XLSXWRITER_AVAILABLE:
            QMessageBox.warning(self, "تنبيه", "مكتبة xlsxwriter غير متوفرة. يرجى تثبيتها لاستخدام هذه الميزة.")
            return
        
        try:
            # اختيار مكان حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف Excel",
                f"opening_balances_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if not file_path:
                return
            
            # إنشاء ملف Excel
            workbook = xlsxwriter.Workbook(file_path)
            worksheet = workbook.add_worksheet('الأرصدة الافتتاحية')
            
            # تنسيق الخلايا
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#3498db',
                'font_color': 'white',
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            cell_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            number_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter',
                'border': 1,
                'num_format': '0.000'
            })
            
            # كتابة العناوين
            headers = [
                "الرقم", "الكمية", "سعر الوحدة ($)",
                "سعر الوحدة (ريال)", "إجمالي القيمة ($)", "سعر الصرف",
                "إجمالي القيمة (ريال)", "الصنف", "الوحدة", "التاريخ"
            ]
            
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)
            
            # كتابة البيانات
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item:
                        value = item.text()
                        # تحديد نوع التنسيق حسب العمود
                        if col in [1, 2, 3, 4, 5, 6]:  # الأعمدة الرقمية
                            try:
                                worksheet.write(row + 1, col, float(value), number_format)
                            except ValueError:
                                worksheet.write(row + 1, col, value, cell_format)
                        else:
                            worksheet.write(row + 1, col, value, cell_format)
            
            # ضبط عرض الأعمدة
            worksheet.set_column(0, 9, 15)
            
            # إضافة الإجماليات
            total_row = self.table.rowCount() + 2
            worksheet.write(total_row, 0, "الإجماليات", header_format)
            
            # حساب الإجماليات
            total_quantity = sum(float(self.table.item(i, 1).text()) for i in range(self.table.rowCount()) if self.table.item(i, 1))
            total_value_usd = sum(float(self.table.item(i, 4).text()) for i in range(self.table.rowCount()) if self.table.item(i, 4))
            total_value_sar = sum(float(self.table.item(i, 6).text()) for i in range(self.table.rowCount()) if self.table.item(i, 6))
            
            worksheet.write(total_row, 1, total_quantity, number_format)
            worksheet.write(total_row, 4, total_value_usd, number_format)
            worksheet.write(total_row, 6, total_value_sar, number_format)
            
            workbook.close()
            
            QMessageBox.information(self, "نجاح", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
            log_info(f"تم تصدير الأرصدة الافتتاحية إلى Excel: {file_path}")
            
        except Exception as e:
            log_error(f"خطأ في تصدير Excel: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")
    
    def import_from_excel(self):
        """استيراد البيانات من ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            QMessageBox.warning(self, "تنبيه", "مكتبة openpyxl غير متوفرة. يرجى تثبيتها لاستخدام هذه الميزة.")
            return
        
        try:
            # اختيار ملف Excel
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار ملف Excel",
                "",
                "Excel Files (*.xlsx *.xls)"
            )
            
            if not file_path:
                return
            
            # قراءة ملف Excel
            workbook = openpyxl.load_workbook(file_path)
            worksheet = workbook.active
            
            # التحقق من وجود البيانات
            if worksheet.max_row < 2:
                QMessageBox.warning(self, "تنبيه", "الملف لا يحتوي على بيانات للاستيراد")
                return
            
            # تأكيد الاستيراد
            reply = QMessageBox.question(
                self, "تأكيد الاستيراد",
                "هل أنت متأكد من استيراد البيانات؟ سيتم إضافة البيانات الجديدة إلى قاعدة البيانات.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            imported_count = 0
            errors = []
            
            # استيراد البيانات
            with session_scope() as session:
                for row_num in range(2, worksheet.max_row + 1):
                    try:
                        # قراءة البيانات من الصف
                        quantity = float(worksheet.cell(row_num, 2).value or 0)
                        price_per_carat_usd = float(worksheet.cell(row_num, 3).value or 0)
                        exchange_rate = float(worksheet.cell(row_num, 6).value or self.exchange_rate)
                        diamond_type = str(worksheet.cell(row_num, 8).value or "")
                        unit_name = str(worksheet.cell(row_num, 9).value or "")
                        date_str = worksheet.cell(row_num, 10).value
                        
                        # تحويل التاريخ
                        if isinstance(date_str, str):
                            date_created = datetime.strptime(date_str, "%Y-%m-%d").date()
                        elif hasattr(date_str, 'date'):
                            date_created = date_str.date()
                        else:
                            date_created = datetime.now().date()
                        
                        # البحث عن الصنف والوحدة
                        category = session.query(Category).filter_by(name=diamond_type).first()
                        unit = session.query(Unit).filter_by(name=unit_name).first()
                        
                        if not category:
                            errors.append(f"الصف {row_num}: الصنف '{diamond_type}' غير موجود")
                            continue
                        
                        if not unit:
                            errors.append(f"الصف {row_num}: الوحدة '{unit_name}' غير موجودة")
                            continue
                        
                        # إنشاء رصيد افتتاحي جديد
                        new_balance = OpeningBalance(
                            diamond_type=diamond_type,
                            quantity=quantity,
                            price_per_carat_usd=price_per_carat_usd,
                            total_value_usd=quantity * price_per_carat_usd,
                            total_value_sar=quantity * price_per_carat_usd * exchange_rate,
                            exchange_rate=exchange_rate,
                            category_id=category.id,
                            unit_id=unit.id,
                            date_created=date_created,
                            notes="مستورد من Excel"
                        )
                        
                        session.add(new_balance)
                        imported_count += 1
                        
                    except Exception as e:
                        errors.append(f"الصف {row_num}: {str(e)}")
                        continue
            
            # إعادة تحميل البيانات
            self.load_opening_balances()
            
            # إرسال إشارة تحديث البيانات
            self.data_updated.emit()
            
            # عرض نتائج الاستيراد
            message = f"تم استيراد {imported_count} رصيد افتتاحي بنجاح"
            if errors:
                message += f"\n\nالأخطاء ({len(errors)}):\n" + "\n".join(errors[:10])
                if len(errors) > 10:
                    message += f"\n... و {len(errors) - 10} أخطاء أخرى"
            
            QMessageBox.information(self, "نتائج الاستيراد", message)
            log_info(f"تم استيراد {imported_count} رصيد افتتاحي من Excel")
            
        except Exception as e:
            log_error(f"خطأ في استيراد Excel: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء استيراد البيانات: {str(e)}")