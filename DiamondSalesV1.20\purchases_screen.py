from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QPushButton, QLabel, QLineEdit, QComboBox,
                            QTableWidget, QTableWidgetItem, QFormLayout,
                            QDateEdit, QDoubleSpinBox, QMessageBox, QSpinBox,
                            QHeaderView, QDialog, QDialogButtonBox, QTextEdit,
                            QSizePolicy)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from sqlalchemy import create_engine, desc
from sqlalchemy.orm import sessionmaker
from datetime import datetime

from database import Supplier, Purchase, JournalEntry, ChartOfAccounts, Payment, Receipt, Category, Unit, صندوق_النقدية, حركة_نقدية
from db_session import session_scope
from logger import log_error, log_info
from ui_utils import style_button, style_dialog_buttons
from translations import get_translation as _

class PurchasesScreen(QWidget):
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_ui()
        self.load_purchases_data()

    def init_ui(self):
        self.setWindowTitle(_("purchases_title", "نظام إدارة مبيعات الألماس - شاشة المشتريات"))
        self.setGeometry(100, 100, 1000, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.purchases_tab = QWidget()
        self.new_purchase_tab = QWidget()

        # Setup tabs
        self.setup_purchases_tab()
        self.setup_new_purchase_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.purchases_tab, "قائمة المشتريات")
        self.tab_widget.addTab(self.new_purchase_tab, "إضافة عملية شراء جديدة")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_purchases_tab(self):
        # Create layout for purchases tab
        layout = QVBoxLayout(self.purchases_tab)

        # Create search controls
        search_layout = QHBoxLayout()

        self.search_supplier = QLineEdit()
        self.search_supplier.setPlaceholderText("بحث باسم المورد")

        self.search_date_from = QDateEdit()
        self.search_date_from.setDisplayFormat("dd/MM/yyyy")
        self.search_date_from.setCalendarPopup(True)
        self.search_date_from.setDate(QDate.currentDate().addDays(-30))  # Last 30 days by default

        self.search_date_to = QDateEdit()
        self.search_date_to.setDisplayFormat("dd/MM/yyyy")
        self.search_date_to.setCalendarPopup(True)
        self.search_date_to.setDate(QDate.currentDate())

        # Inicializar el combo de búsqueda por نوع الصنف
        self.search_diamond_type = QComboBox()
        self.search_diamond_type.addItem("جميع الأنواع")        # تحميل الأصناف في قائمة البحث
        with session_scope() as session:
            categories = session.query(Category).all()
            for category in categories:
                self.search_diamond_type.addItem(category.name)
                
        search_button = QPushButton("بحث")
        search_button.clicked.connect(self.search_purchases)
        style_button(search_button, "info", min_width=120, min_height=35)

        search_layout.addWidget(QLabel(_("supplier_name_label", "اسم المورد:")))
        search_layout.addWidget(self.search_supplier)
        search_layout.addWidget(QLabel(_("from_date_label", "من تاريخ:")))
        search_layout.addWidget(self.search_date_from)
        search_layout.addWidget(QLabel(_("to_date_label", "إلى تاريخ:")))
        search_layout.addWidget(self.search_date_to)
        search_layout.addWidget(QLabel(_("diamond_type_label", "نوع الألماس:")))
        search_layout.addWidget(self.search_diamond_type)
        search_layout.addWidget(search_button)

        # Create purchases table
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(10)
        self.purchases_table.setHorizontalHeaderLabels([
            "رقم المشتريات", "اسم المورد", "نوع الألماس", "الوزن بالقيراط",
            "السعر/قيراط ($)", "الإجمالي ($)", "الإجمالي (ريال)",
            "تاريخ الشراء", "المبلغ المدفوع", "المبلغ المتبقي"
        ])
        header = self.purchases_table.horizontalHeader()
        for i in range(10):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)        # Create action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(20)  # زيادة المسافة بين الأزرار
        action_layout.setContentsMargins(5, 5, 5, 15)  # هوامش لمجموعة الأزرار

        view_button = QPushButton("عرض التفاصيل")
        view_button.clicked.connect(self.view_purchase_details)
        style_button(view_button, "info")

        edit_button = QPushButton("تعديل")
        edit_button.clicked.connect(self.edit_purchase)
        style_button(edit_button, "edit")

        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_purchase)
        style_button(delete_button, "delete")

        create_payment_button = QPushButton("إنشاء سند صرف")
        create_payment_button.clicked.connect(self.create_payment)
        style_button(create_payment_button, "success")

        print_button = QPushButton("طباعة فاتورة")
        style_button(print_button, "warning")
        print_button.clicked.connect(self.print_invoice)

        action_layout.addWidget(view_button)
        action_layout.addWidget(edit_button)
        action_layout.addWidget(delete_button)
        action_layout.addWidget(create_payment_button)
        action_layout.addWidget(print_button)

        # Add widgets to layout
        layout.addLayout(search_layout)
        layout.addWidget(self.purchases_table)
        layout.addLayout(action_layout)

    def setup_new_purchase_tab(self):
        # Create layout for new purchase tab
        layout = QVBoxLayout(self.new_purchase_tab)

        # Create form layout for purchase details
        form_layout = QFormLayout()

        # Supplier selection
        self.supplier_combo = QComboBox()
        self.load_suppliers()

        # Category selection
        self.category_combo = QComboBox()
        self.category_combo.addItem("-- اختر الصنف --", None)
        self.load_categories()

        # Connect category selection to update units
        self.category_combo.currentIndexChanged.connect(self.on_category_changed)

        # Unit selection
        self.unit_combo = QComboBox()
        self.unit_combo.addItem("-- اختر الوحدة --", None)
        self.load_units()

        # نستخدم الصنف بدلاً من نوع الألماس

        # Carat weight input
        self.carat_weight_spin = QDoubleSpinBox()
        self.carat_weight_spin.setRange(0.01, 1000.0)
        self.carat_weight_spin.setDecimals(3)
        self.carat_weight_spin.setSingleStep(0.01)

        # Price per carat input
        self.price_per_carat_spin = QDoubleSpinBox()
        self.price_per_carat_spin.setRange(0.01, 1000000.0)
        self.price_per_carat_spin.setDecimals(3)
        self.price_per_carat_spin.setSingleStep(10.0)
        self.price_per_carat_spin.setPrefix("$ ")

        # Calculated fields
        self.total_usd_label = QLabel("0.00 $")
        self.total_sar_label = QLabel(_("zero_sar", "0.00 ريال"))

        # Connect signals to update total prices
        self.carat_weight_spin.valueChanged.connect(self.calculate_totals)
        self.price_per_carat_spin.valueChanged.connect(self.calculate_totals)

        # Date field
        self.purchase_date = QDateEdit()
        self.purchase_date.setDisplayFormat("dd/MM/yyyy")
        self.purchase_date.setCalendarPopup(True)
        self.purchase_date.setDate(QDate.currentDate())

        # Initial payment
        self.initial_payment_spin = QDoubleSpinBox()
        self.initial_payment_spin.setRange(0.0, 1000000.0)
        self.initial_payment_spin.setDecimals(3)
        self.initial_payment_spin.setSingleStep(100.0)
        self.initial_payment_spin.setPrefix("$ ")

        # Create exchange rate input
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.01, 10.0)
        self.exchange_rate_spin.setDecimals(3)
        self.exchange_rate_spin.setSingleStep(0.01)
        self.exchange_rate_spin.setValue(3.75)  # Default SAR to USD rate
        self.exchange_rate_spin.valueChanged.connect(self.calculate_totals)

        # Add fields to form layout
        form_layout.addRow("المورد:", self.supplier_combo)
        form_layout.addRow("الصنف:", self.category_combo)
        form_layout.addRow("الوحدة:", self.unit_combo)
        form_layout.addRow("الوزن بالقيراط:", self.carat_weight_spin)
        form_layout.addRow("السعر لكل قيراط:", self.price_per_carat_spin)
        form_layout.addRow("سعر الصرف (ريال/دولار):", self.exchange_rate_spin)
        form_layout.addRow("الإجمالي بالدولار:", self.total_usd_label)
        form_layout.addRow("الإجمالي بالريال:", self.total_sar_label)
        form_layout.addRow("تاريخ الشراء:", self.purchase_date)
        form_layout.addRow("المبلغ المدفوع مقدماً (دولار):", self.initial_payment_spin)        # Create button to save purchase
        save_button = QPushButton("حفظ عملية الشراء")
        save_button.clicked.connect(self.save_purchase)
        style_button(save_button, "add", min_width=200, min_height=45)

        # Add widgets to layout
        layout.addLayout(form_layout)
        layout.addWidget(save_button)

    def load_suppliers(self):
        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get all suppliers
            suppliers = session.query(Supplier).all()

            # Add suppliers to combo box
            self.supplier_combo.clear()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الموردين: {str(e)}")
        finally:
            session.close()

    def load_categories(self):
        """تحميل بيانات الأصناف في القائمة المنسدلة"""
        try:
            with session_scope() as session:
                # Get all categories
                categories = session.query(Category).all()

                # Add categories to combo box
                self.category_combo.clear()
                self.category_combo.addItem("-- اختر الصنف --", None)
                for category in categories:
                    self.category_combo.addItem(category.name, category.id)

                log_info(f"تم تحميل {len(categories)} صنف في القائمة المنسدلة")
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الأصناف: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الأصناف: {str(e)}")

    def load_units(self, category_id=None):
        """تحميل بيانات الوحدات في القائمة المنسدلة"""
        try:
            with session_scope() as session:
                # Get units filtered by category if specified
                if category_id:
                    units = session.query(Unit).filter_by(category_id=category_id).all()
                else:
                    units = session.query(Unit).all()

                # Add units to combo box
                self.unit_combo.clear()
                self.unit_combo.addItem("-- اختر الوحدة --", None)
                for unit in units:
                    display_text = f"{unit.name}"
                    if unit.symbol:
                        display_text += f" ({unit.symbol})"
                    self.unit_combo.addItem(display_text, unit.id)

                log_info(f"تم تحميل {len(units)} وحدة في القائمة المنسدلة")

                # إرجاع عدد الوحدات التي تم تحميلها
                return len(units)
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الوحدات: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الوحدات: {str(e)}")
            return 0

    def on_category_changed(self, index):
        """تحديث الوحدات عند تغيير الصنف"""
        category_id = self.category_combo.currentData()

        # تحميل الوحدات المرتبطة بالصنف
        units_count = self.load_units(category_id)

        # اختيار الوحدة تلقائياً إذا كان هناك وحدة واحدة فقط مرتبطة بالصنف
        if category_id and units_count == 1:
            self.unit_combo.setCurrentIndex(1)  # اختيار الوحدة الوحيدة

    def calculate_totals(self):
        # Get input values
        carat_weight = self.carat_weight_spin.value()
        price_per_carat = self.price_per_carat_spin.value()
        exchange_rate = self.exchange_rate_spin.value()

        # Calculate totals
        total_usd = carat_weight * price_per_carat
        total_sar = total_usd * exchange_rate

        # Update labels
        self.total_usd_label.setText(f"{total_usd:.3f} $")
        self.total_sar_label.setText(f"{total_sar:.3f} ريال")

        # Update initial payment maximum
        self.initial_payment_spin.setMaximum(total_usd)

    def save_purchase(self):
        try:
            # Get form data
            supplier_id = self.supplier_combo.currentData()
            category_id = self.category_combo.currentData()
            unit_id = self.unit_combo.currentData()

            # استخدام اسم الصنف كنوع الألماس
            diamond_type = self.category_combo.currentText()
            if diamond_type == "-- اختر الصنف --":
                diamond_type = ""

            carat_weight = self.carat_weight_spin.value()
            price_per_carat = self.price_per_carat_spin.value()
            exchange_rate = self.exchange_rate_spin.value()
            total_usd = carat_weight * price_per_carat
            total_sar = total_usd * exchange_rate
            purchase_date = self.purchase_date.date().toPyDate()
            initial_payment = self.initial_payment_spin.value()

            # Validate inputs
            if supplier_id is None:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار المورد")
                return

            if carat_weight <= 0:
                QMessageBox.warning(self, "تحذير", "الرجاء إدخال وزن صالح بالقيراط")
                return

            if price_per_carat <= 0:
                QMessageBox.warning(self, "تحذير", "الرجاء إدخال سعر صالح للقيراط")
                return

            # Connect to database
            engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Create new purchase
                purchase = Purchase(
                    supplier_id=supplier_id,
                    category_id=category_id,
                    unit_id=unit_id,
                    diamond_type=diamond_type,
                    carat_weight=carat_weight,
                    price_per_carat_usd=price_per_carat,
                    total_price_usd=total_usd,
                    total_price_sar=total_sar,
                    purchase_date=purchase_date,
                    exchange_rate=exchange_rate,
                    amount_due=total_usd - initial_payment,
                    amount_paid=initial_payment
                )

                # Add purchase to database
                session.add(purchase)
                session.flush()  # To get the purchase ID

                # Create journal entries
                if total_usd > 0:
                    # Get account IDs
                    purchases_account = session.query(ChartOfAccounts).filter_by(name="Purchases").first()
                    suppliers_account = session.query(ChartOfAccounts).filter_by(name="Suppliers").first()
                    cash_account = session.query(ChartOfAccounts).filter_by(name="Cash").first()

                    if purchases_account and suppliers_account and cash_account:
                        # Debit Purchases account
                        purchases_entry = JournalEntry(
                            account_id=purchases_account.id,
                            debit=total_usd,
                            credit=0,
                            date=purchase_date,
                            description=f"مشتريات ألماس {diamond_type} - {carat_weight} قيراط",
                            purchase_id=purchase.id
                        )
                        session.add(purchases_entry)

                        # If payment is full, credit cash, otherwise split between cash and supplier
                        if initial_payment >= total_usd:
                            # Full cash payment
                            cash_entry = JournalEntry(
                                account_id=cash_account.id,
                                debit=0,
                                credit=total_usd,
                                date=purchase_date,
                                description=f"مشتريات ألماس {diamond_type} - {carat_weight} قيراط",
                                purchase_id=purchase.id
                            )
                            session.add(cash_entry)
                        else:
                            # Partial payment - split between cash and payable
                            if initial_payment > 0:
                                cash_entry = JournalEntry(
                                    account_id=cash_account.id,
                                    debit=0,
                                    credit=initial_payment,
                                    date=purchase_date,
                                    description=f"مشتريات ألماس {diamond_type} - دفعة أولى",
                                    purchase_id=purchase.id
                                )
                                session.add(cash_entry)

                            # Record remaining in accounts payable
                            supplier_entry = JournalEntry(
                                account_id=suppliers_account.id,
                                debit=0,
                                credit=total_usd - initial_payment,
                                date=purchase_date,
                                description=f"مشتريات ألماس {diamond_type} - ذمم دائنة",
                                purchase_id=purchase.id
                            )
                            session.add(supplier_entry)

                # Commit changes
                session.commit()

                # Show success message
                QMessageBox.information(self, "نجاح", "تم إضافة عملية الشراء بنجاح")

                # Reset form
                self.category_combo.setCurrentIndex(0)  # Reset to "-- اختر الصنف --"
                self.unit_combo.setCurrentIndex(0)      # Reset to "-- اختر الوحدة --"
                self.carat_weight_spin.setValue(0)
                self.price_per_carat_spin.setValue(0)
                self.initial_payment_spin.setValue(0)
                self.purchase_date.setDate(QDate.currentDate())

                # Reload purchases data
                self.load_purchases_data()

                # Switch to purchases tab
                self.tab_widget.setCurrentIndex(0)

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ عملية الشراء: {str(e)}")
            finally:
                session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def load_purchases_data(self):
        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get all purchases with supplier information
            purchases = session.query(Purchase, Supplier.name).join(Supplier).order_by(desc(Purchase.purchase_date)).all()

            # Update table
            self.purchases_table.setRowCount(len(purchases))

            for i, (purchase, supplier_name) in enumerate(purchases):
                self.purchases_table.setItem(i, 0, QTableWidgetItem(str(purchase.id)))
                self.purchases_table.setItem(i, 1, QTableWidgetItem(supplier_name))
                self.purchases_table.setItem(i, 2, QTableWidgetItem(purchase.diamond_type))
                self.purchases_table.setItem(i, 3, QTableWidgetItem(f"{purchase.carat_weight:.3f}"))
                self.purchases_table.setItem(i, 4, QTableWidgetItem(f"{purchase.price_per_carat_usd:.3f}"))
                self.purchases_table.setItem(i, 5, QTableWidgetItem(f"{purchase.total_price_usd:.3f}"))
                self.purchases_table.setItem(i, 6, QTableWidgetItem(f"{purchase.total_price_sar:.3f}"))

                # Format date
                date_str = purchase.purchase_date.strftime("%d/%m/%Y") if purchase.purchase_date else ""
                self.purchases_table.setItem(i, 7, QTableWidgetItem(date_str))

                self.purchases_table.setItem(i, 8, QTableWidgetItem(f"{purchase.amount_paid:.3f}"))
                self.purchases_table.setItem(i, 9, QTableWidgetItem(f"{purchase.amount_due:.3f}"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات المشتريات: {str(e)}")
        finally:
            session.close()

    def search_purchases(self):
        # Get search criteria
        supplier_name = self.search_supplier.text().strip()
        date_from = self.search_date_from.date().toPyDate()
        date_to = self.search_date_to.date().toPyDate()
        diamond_type = self.search_diamond_type.currentText()

        # Connect to database
        from db_session import session_scope
        from logger import log_error, log_info

        try:
            with session_scope() as session:
                # Build query
                query = session.query(Purchase, Supplier.name).join(Supplier)

                # Apply filters
                if supplier_name:
                    query = query.filter(Supplier.name.like(f'%{supplier_name}%'))

                # Convert date_from to datetime with time at 00:00:00
                if date_from:
                    from datetime import datetime
                    date_from_dt = datetime.combine(date_from, datetime.min.time())
                    query = query.filter(Purchase.purchase_date >= date_from_dt)

                # Convert date_to to datetime with time at 23:59:59
                if date_to:
                    from datetime import datetime, time
                    date_to_dt = datetime.combine(date_to, time(23, 59, 59))
                    query = query.filter(Purchase.purchase_date <= date_to_dt)

                if diamond_type != "جميع الأنواع":
                    # استخدام like للمقارنة المرنة
                    query = query.filter(Purchase.diamond_type.like(f"%{diamond_type}%"))

                # Order by date
                query = query.order_by(desc(Purchase.purchase_date))

                # Execute query
                purchases = query.all()

                # Log search results
                log_info(f"تم العثور على {len(purchases)} عملية شراء تطابق معايير البحث")

                # Update table
                self.purchases_table.setRowCount(len(purchases))

                # Fill table with results
                for i, (purchase, supplier_name) in enumerate(purchases):
                    self.purchases_table.setItem(i, 0, QTableWidgetItem(str(purchase.id)))
                    self.purchases_table.setItem(i, 1, QTableWidgetItem(supplier_name))
                    self.purchases_table.setItem(i, 2, QTableWidgetItem(purchase.diamond_type))
                    self.purchases_table.setItem(i, 3, QTableWidgetItem(f"{purchase.carat_weight:.3f}"))
                    self.purchases_table.setItem(i, 4, QTableWidgetItem(f"{purchase.price_per_carat_usd:.3f}"))
                    self.purchases_table.setItem(i, 5, QTableWidgetItem(f"{purchase.total_price_usd:.3f}"))
                    self.purchases_table.setItem(i, 6, QTableWidgetItem(f"{purchase.total_price_sar:.3f}"))

                    # Format date
                    date_str = purchase.purchase_date.strftime("%d/%m/%Y") if purchase.purchase_date else ""
                    self.purchases_table.setItem(i, 7, QTableWidgetItem(date_str))

                    self.purchases_table.setItem(i, 8, QTableWidgetItem(f"{purchase.amount_paid:.3f}"))
                    self.purchases_table.setItem(i, 9, QTableWidgetItem(f"{purchase.amount_due:.3f}"))

                # Show message if no results found
                if len(purchases) == 0:
                    QMessageBox.information(self, "نتائج البحث", "لم يتم العثور على أي مشتريات تطابق معايير البحث")

        except Exception as e:
            log_error(f"خطأ في البحث عن المشتريات: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def view_purchase_details(self):
        # Get selected row
        selected_row = self.purchases_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عملية شراء لعرضها")
            return

        # Get purchase ID
        purchase_id = int(self.purchases_table.item(selected_row, 0).text())

        # Show details in a dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("تفاصيل عملية الشراء")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get purchase with supplier information
            purchase_info = session.query(Purchase, Supplier).join(Supplier).filter(Purchase.id == purchase_id).first()

            if not purchase_info:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على عملية الشراء")
                return

            purchase, supplier = purchase_info

            # Create labels with purchase information
            info_text = f"""
            <h3>تفاصيل عملية الشراء رقم {purchase.id}</h3>
            <p><b>المورد:</b> {supplier.name}</p>
            <p><b>رقم هوية المورد:</b> {supplier.id_number}</p>
            <p><b>هاتف المورد:</b> {supplier.phone}</p>
            <p><b>نوع الألماس:</b> {purchase.diamond_type}</p>
            <p><b>الوزن بالقيراط:</b> {purchase.carat_weight:.3f}</p>
            <p><b>السعر لكل قيراط:</b> {purchase.price_per_carat_usd:.3f} $</p>
            <p><b>الإجمالي بالدولار:</b> {purchase.total_price_usd:.3f} $</p>
            <p><b>سعر الصرف:</b> {purchase.exchange_rate:.3f} ريال/دولار</p>
            <p><b>الإجمالي بالريال:</b> {purchase.total_price_sar:.3f} ريال</p>
            <p><b>تاريخ الشراء:</b> {purchase.purchase_date.strftime("%d/%m/%Y") if purchase.purchase_date else ""}</p>
            <p><b>المبلغ المدفوع:</b> {purchase.amount_paid:.3f} $</p>
            <p><b>المبلغ المتبقي:</b> {purchase.amount_due:.3f} $</p>
            """

            info_label = QLabel(info_text)
            info_label.setTextFormat(Qt.TextFormat.RichText)
            layout.addWidget(info_label)            # Add a close button
            buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
            buttons.rejected.connect(dialog.reject)
            style_dialog_buttons(buttons)
            layout.addWidget(buttons)

            # Show the dialog
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التفاصيل: {str(e)}")
        finally:
            session.close()

    def edit_purchase(self):
        # Get selected row
        selected_row = self.purchases_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عملية شراء لتعديلها")
            return

        # Get purchase ID
        purchase_id = int(self.purchases_table.item(selected_row, 0).text())

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get purchase with supplier information
            purchase_info = session.query(Purchase, Supplier).join(Supplier).filter(Purchase.id == purchase_id).first()

            if not purchase_info:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على عملية الشراء")
                return

            purchase, supplier = purchase_info

            # Create edit dialog
            dialog = QDialog(self)
            dialog.setWindowTitle(f"تعديل عملية الشراء رقم {purchase.id}")
            dialog.setMinimumWidth(500)

            layout = QVBoxLayout(dialog)

            # Create form layout for purchase details
            form_layout = QFormLayout()

            # Supplier selection (read-only)
            supplier_label = QLabel(supplier.name)
            form_layout.addRow("المورد:", supplier_label)

            # Category selection
            category_combo = QComboBox()
            with session_scope() as category_session:
                categories = category_session.query(Category).all()
                for category in categories:
                    category_combo.addItem(category.name, category.id)

            # Set current category based on diamond_type
            index = category_combo.findText(purchase.diamond_type)
            if index >= 0:
                category_combo.setCurrentIndex(index)
            form_layout.addRow("الصنف:", category_combo)

            # Unit selection
            unit_combo = QComboBox()
            # We'll populate this when category changes
            form_layout.addRow("الوحدة:", unit_combo)

            # Load units for the current category
            def load_edit_units(category_id=None):
                unit_combo.clear()
                with session_scope() as unit_session:
                    if category_id:
                        units = unit_session.query(Unit).filter_by(category_id=category_id).all()
                    else:
                        units = unit_session.query(Unit).all()

                    for unit in units:
                        display_text = f"{unit.name}"
                        if unit.symbol:
                            display_text += f" ({unit.symbol})"
                        unit_combo.addItem(display_text, unit.id)

                    # Select the current unit if possible
                    if purchase.unit_id:
                        for i in range(unit_combo.count()):
                            if unit_combo.itemData(i) == purchase.unit_id:
                                unit_combo.setCurrentIndex(i)
                                break

            # Connect category change to unit update
            def on_edit_category_changed(index):
                category_id = category_combo.itemData(index)
                load_edit_units(category_id)

            category_combo.currentIndexChanged.connect(on_edit_category_changed)

            # Initial load of units
            category_id = category_combo.currentData()
            load_edit_units(category_id)

            # Carat weight input
            carat_weight_spin = QDoubleSpinBox()
            carat_weight_spin.setRange(0.01, 1000.0)
            carat_weight_spin.setDecimals(3)
            carat_weight_spin.setSingleStep(0.01)
            carat_weight_spin.setValue(purchase.carat_weight)
            form_layout.addRow("الوزن بالقيراط:", carat_weight_spin)

            # Price per carat input
            price_per_carat_spin = QDoubleSpinBox()
            price_per_carat_spin.setRange(0.01, 1000000.0)
            price_per_carat_spin.setDecimals(3)
            price_per_carat_spin.setSingleStep(10.0)
            price_per_carat_spin.setPrefix("$ ")
            price_per_carat_spin.setValue(purchase.price_per_carat_usd)
            form_layout.addRow("السعر لكل قيراط:", price_per_carat_spin)

            # Exchange rate input
            exchange_rate_spin = QDoubleSpinBox()
            exchange_rate_spin.setRange(0.01, 10.0)
            exchange_rate_spin.setDecimals(3)
            exchange_rate_spin.setSingleStep(0.01)
            exchange_rate_spin.setValue(purchase.exchange_rate)
            form_layout.addRow("سعر الصرف (ريال/دولار):", exchange_rate_spin)

            # Calculated fields
            total_usd_label = QLabel(f"{purchase.total_price_usd:.3f} $")
            total_sar_label = QLabel(f"{purchase.total_price_sar:.3f} ريال")
            form_layout.addRow("الإجمالي بالدولار:", total_usd_label)
            form_layout.addRow("الإجمالي بالريال:", total_sar_label)

            # Date field
            purchase_date = QDateEdit()
            purchase_date.setDisplayFormat("dd/MM/yyyy")
            purchase_date.setCalendarPopup(True)
            purchase_date.setDate(QDate.fromString(purchase.purchase_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
            form_layout.addRow("تاريخ الشراء:", purchase_date)

            # Payment information
            amount_paid_spin = QDoubleSpinBox()
            amount_paid_spin.setRange(0.0, 1000000.0)
            amount_paid_spin.setDecimals(3)
            amount_paid_spin.setSingleStep(100.0)
            amount_paid_spin.setPrefix("$ ")
            amount_paid_spin.setValue(purchase.amount_paid)
            form_layout.addRow("المبلغ المدفوع:", amount_paid_spin)

            # Function to update totals
            def update_totals():
                carat_weight = carat_weight_spin.value()
                price_per_carat = price_per_carat_spin.value()
                exchange_rate = exchange_rate_spin.value()

                total_usd = carat_weight * price_per_carat
                total_sar = total_usd * exchange_rate

                total_usd_label.setText(f"{total_usd:.3f} $")
                total_sar_label.setText(f"{total_sar:.3f} ريال")

                # Update maximum payment amount
                amount_paid_spin.setMaximum(total_usd)

            # Connect signals to update total prices
            carat_weight_spin.valueChanged.connect(update_totals)
            price_per_carat_spin.valueChanged.connect(update_totals)
            exchange_rate_spin.valueChanged.connect(update_totals)

            # Add form layout to main layout
            layout.addLayout(form_layout)            # Add buttons
            buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
            buttons.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
            buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            style_dialog_buttons(buttons)
            layout.addWidget(buttons)

            # Show dialog
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Get form data
                category_id = category_combo.currentData()
                unit_id = unit_combo.currentData()
                diamond_type = category_combo.currentText()  # Use category name as diamond type
                carat_weight = carat_weight_spin.value()
                price_per_carat = price_per_carat_spin.value()
                exchange_rate = exchange_rate_spin.value()
                purchase_date_value = purchase_date.date().toPyDate()
                amount_paid = amount_paid_spin.value()

                # Calculate totals
                total_usd = carat_weight * price_per_carat
                total_sar = total_usd * exchange_rate
                amount_due = total_usd - amount_paid

                # Update purchase object
                purchase.category_id = category_id
                purchase.unit_id = unit_id
                purchase.diamond_type = diamond_type
                purchase.carat_weight = carat_weight
                purchase.price_per_carat_usd = price_per_carat
                purchase.total_price_usd = total_usd
                purchase.total_price_sar = total_sar
                purchase.purchase_date = purchase_date_value
                purchase.exchange_rate = exchange_rate
                purchase.amount_paid = amount_paid
                purchase.amount_due = amount_due

                # Commit changes
                session.commit()

                # Show success message
                QMessageBox.information(self, "نجاح", "تم تعديل عملية الشراء بنجاح")

                # Reload purchases data
                self.load_purchases_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل عملية الشراء: {str(e)}")
        finally:
            session.close()

    def delete_purchase(self):
        # Get selected row
        selected_row = self.purchases_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عملية شراء لحذفها")
            return

        # Get purchase ID
        purchase_id = int(self.purchases_table.item(selected_row, 0).text())

        # Confirm deletion
        reply = QMessageBox.question(self, "تأكيد",
                                   "هل أنت متأكد من رغبتك في حذف عملية الشراء هذه؟\n"
                                   "سيتم حذف جميع القيود المحاسبية المرتبطة بها أيضاً.",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # Connect to database
            engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Delete journal entries first
                session.query(JournalEntry).filter_by(purchase_id=purchase_id).delete()

                # Delete purchase
                session.query(Purchase).filter_by(id=purchase_id).delete()

                # Commit changes
                session.commit()

                # Show success message
                QMessageBox.information(self, "نجاح", "تم حذف عملية الشراء بنجاح")

                # Reload purchases data
                self.load_purchases_data()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف عملية الشراء: {str(e)}")
            finally:
                session.close()

    def create_payment(self):
        # Get selected row
        selected_row = self.purchases_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عملية شراء لإنشاء سند صرف لها")
            return

        # Get purchase ID and remaining amount due
        purchase_id = int(self.purchases_table.item(selected_row, 0).text())

        # استخدام session_scope بدلاً من إنشاء اتصال مباشر بقاعدة البيانات
        from db_session import session_scope
        from logger import log_error, log_info

        try:
            with session_scope() as session:
                # Get purchase with supplier information
                purchase_info = session.query(Purchase, Supplier).join(Supplier).filter(Purchase.id == purchase_id).first()

                if not purchase_info:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على عملية الشراء")
                    return

                purchase, supplier = purchase_info

                # Check if there is still an amount due
                if purchase.amount_due <= 0:
                    QMessageBox.information(self, "معلومات", "تم دفع المبلغ المستحق بالكامل مسبقاً")
                    return

                # Create payment dialog
                dialog = QDialog(self)
                dialog.setWindowTitle("إنشاء سند صرف")
                dialog.setMinimumSize(400, 500)

                # Create layout for dialog
                layout = QFormLayout(dialog)
                layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
                layout.setFormAlignment(Qt.AlignmentFlag.AlignRight)

                # Add payment details fields
                purchase_id_label = QLabel(str(purchase_id))
                layout.addRow("رقم المشتريات:", purchase_id_label)

                supplier_name_label = QLabel(supplier.name)
                layout.addRow("اسم المورد:", supplier_name_label)

                date_edit = QDateEdit()
                date_edit.setCalendarPopup(True)
                date_edit.setDate(QDate.currentDate())
                layout.addRow("تاريخ الدفع:", date_edit)

                amount_due_label = QLabel(f"{purchase.amount_due:.3f} $")
                layout.addRow("المبلغ المستحق:", amount_due_label)

                payment_amount_input = QDoubleSpinBox()
                payment_amount_input.setMaximum(purchase.amount_due)
                payment_amount_input.setValue(purchase.amount_due)
                payment_amount_input.setDecimals(3)
                layout.addRow("مبلغ الدفع ($):", payment_amount_input)

                # Exchange rate
                exchange_rate_spin = QDoubleSpinBox()
                exchange_rate_spin.setRange(0.01, 10.0)
                exchange_rate_spin.setDecimals(3)
                exchange_rate_spin.setSingleStep(0.01)
                exchange_rate_spin.setValue(purchase.exchange_rate)
                layout.addRow("سعر الصرف (ريال/دولار):", exchange_rate_spin)

                # SAR amount display
                amount_sar_label = QLabel(f"{purchase.amount_due * purchase.exchange_rate:.3f} ريال")

                def update_sar_amount():
                    amount_sar = payment_amount_input.value() * exchange_rate_spin.value()
                    amount_sar_label.setText(f"{amount_sar:.3f} ريال")

                payment_amount_input.valueChanged.connect(update_sar_amount)
                exchange_rate_spin.valueChanged.connect(update_sar_amount)

                layout.addRow("المبلغ بالريال:", amount_sar_label)

                # Add payment method dropdown
                payment_method_combo = QComboBox()
                payment_method_combo.addItems(["نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "آخر"])
                layout.addRow("طريقة الدفع:", payment_method_combo)

                # Add reference number field
                ref_number_input = QLineEdit()
                layout.addRow("رقم المرجع:", ref_number_input)

                # Add notes field
                notes_input = QTextEdit()
                layout.addRow("ملاحظات:", notes_input)

                # Add buttons
                button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
                button_box.accepted.connect(dialog.accept)
                button_box.rejected.connect(dialog.close)
                layout.addRow(button_box)

                # Show dialog and get result
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    payment_date = date_edit.date().toPyDate()
                    payment_amount = payment_amount_input.value()
                    exchange_rate = exchange_rate_spin.value()
                    amount_sar = payment_amount * exchange_rate
                    payment_method = payment_method_combo.currentText()
                    reference_number = ref_number_input.text()
                    notes = notes_input.toPlainText()

                    # Update purchase record with payment
                    purchase.amount_paid += payment_amount
                    purchase.amount_due -= payment_amount

                    # Create payment record
                    new_payment = Payment(
                        purchase_id=purchase_id,
                        payment_date=payment_date,
                        amount_usd=payment_amount,
                        amount_sar=amount_sar,
                        payment_method=payment_method,
                        reference=reference_number,
                        description=notes
                    )
                    session.add(new_payment)

                    # Create receipt record (for vouchers screen)
                    new_receipt = Receipt(
                        sale_id=None,
                        purchase_id=purchase_id,
                        receipt_type="CashOut",  # تحديد نوع السند كـ صرف
                        amount_usd=payment_amount,
                        amount_sar=amount_sar,
                        issue_date=payment_date
                    )
                    session.add(new_receipt)
                    session.flush()  # للحصول على معرف السند الجديد

                    # Create journal entries for the payment
                    cash_account = session.query(ChartOfAccounts).filter_by(name="Cash").first()
                    suppliers_account = session.query(ChartOfAccounts).filter_by(name="Suppliers").first()

                    if cash_account and suppliers_account:
                        # Debit Suppliers (reduce liability)
                        supplier_entry = JournalEntry(
                            account_id=suppliers_account.id,
                            debit=payment_amount,
                            credit=0,
                            date=payment_date,
                            description=f"سند صرف رقم {new_receipt.id} للمورد - {supplier.name}",
                            purchase_id=purchase_id,
                            receipt_id=new_receipt.id
                        )
                        session.add(supplier_entry)

                        # Credit Cash (reduce cash)
                        cash_entry = JournalEntry(
                            account_id=cash_account.id,
                            debit=0,
                            credit=payment_amount,
                            date=payment_date,
                            description=f"سند صرف رقم {new_receipt.id} للمورد - {supplier.name}",
                            purchase_id=purchase_id,
                            receipt_id=new_receipt.id
                        )
                        session.add(cash_entry)
                        
                        # تحديث صندوق النقدية
                        cash_box = session.query(صندوق_النقدية).first()
                        if not cash_box:
                            cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                            session.add(cash_box)
                            session.flush()
                            
                        # تحديث رصيد الصندوق (خصم قيمة السند من الصندوق)
                        new_balance = cash_box.balance - payment_amount
                        cash_box.balance = new_balance
                        cash_box.last_updated = datetime.now()
                        
                        # إضافة حركة نقدية
                        transaction = حركة_نقدية(
                            cash_box_id=cash_box.id,
                            transaction_type="withdraw",
                            amount=payment_amount,
                            balance_after=new_balance,
                            transaction_date=payment_date,
                            reference=f"سند صرف رقم {new_receipt.id}",
                            description=f"سند صرف للمورد {supplier.name} - مشتريات رقم {purchase_id}",
                            created_by=self.user.id if hasattr(self, 'user') else None
                        )
                        session.add(transaction)

                    # تسجيل العملية
                    log_info(f"تم إنشاء سند صرف جديد رقم {new_receipt.id} بقيمة {payment_amount:.3f}$ للمشتريات رقم {purchase_id}")

                    # Show confirmation
                    QMessageBox.information(self, "نجاح", f"تم إنشاء سند صرف بقيمة {payment_amount:.3f} $ بنجاح")

                    # Refresh purchases table
                    self.load_purchases_data()

        except Exception as e:
            log_error(f"خطأ في إنشاء سند الصرف: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء سند الصرف: {str(e)}")

    def print_invoice(self):
        # Get selected row
        selected_row = self.purchases_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عملية شراء لطباعة فاتورتها")
            return

        # Get purchase ID
        purchase_id = int(self.purchases_table.item(selected_row, 0).text())

        # Connect to database to get purchase details
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get purchase with supplier information
            purchase_info = session.query(Purchase, Supplier).join(Supplier).filter(Purchase.id == purchase_id).first()

            if not purchase_info:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على عملية الشراء")
                return

            purchase, supplier = purchase_info

            try:
                import os
                from datetime import datetime
                import webbrowser

                # Create invoice filename
                now = datetime.now().strftime('%Y%m%d_%H%M%S')
                invoice_filename = f"purchase_invoice_{purchase_id}_{now}.html"
                current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # Get company info if available
                try:
                    from database import CompanyInfo
                    company_info = session.query(CompanyInfo).first()
                    company_name = company_info.name if company_info else "نظام إدارة مبيعات الألماس"
                    company_logo = company_info.logo_path if company_info and company_info.logo_path else ""
                except:
                    company_name = "نظام إدارة مبيعات الألماس"
                    company_logo = ""

                # Build HTML content
                html_content = f"""
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>فاتورة مشتريات - {purchase_id}</title>
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

                        body {{
                            font-family: 'Tajawal', Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                            direction: rtl;
                            background-color: #f9f9f9;
                            color: #333;
                        }}

                        .container {{
                            max-width: 1000px;
                            margin: 0 auto;
                            background-color: #fff;
                            padding: 30px;
                            border-radius: 10px;
                            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                        }}

                        .header {{
                            text-align: center;
                            margin-bottom: 30px;
                            padding-bottom: 20px;
                            border-bottom: 2px solid #eee;
                        }}

                        .logo {{
                            max-width: 150px;
                            margin-bottom: 15px;
                        }}

                        .title {{
                            font-size: 28px;
                            font-weight: 700;
                            color: #2c3e50;
                            margin-bottom: 10px;
                        }}

                        .subtitle {{
                            font-size: 20px;
                            font-weight: 500;
                            color: #3498db;
                            margin: 10px 0;
                        }}

                        .invoice-details {{
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 30px;
                            flex-wrap: wrap;
                        }}

                        .supplier-details, .invoice-info {{
                            width: 48%;
                            background-color: #f8f9fa;
                            padding: 15px;
                            border-radius: 5px;
                            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                        }}

                        @media (max-width: 768px) {{
                            .supplier-details, .invoice-info {{
                                width: 100%;
                                margin-bottom: 15px;
                            }}
                        }}

                        .section-title {{
                            color: #3498db;
                            border-bottom: 1px solid #eee;
                            padding-bottom: 5px;
                            margin-bottom: 15px;
                            font-size: 18px;
                        }}

                        .detail-row {{
                            display: flex;
                            margin-bottom: 8px;
                        }}

                        .detail-label {{
                            font-weight: 700;
                            width: 40%;
                            color: #7f8c8d;
                        }}

                        .detail-value {{
                            width: 60%;
                        }}

                        table {{
                            width: 100%;
                            border-collapse: collapse;
                            margin: 25px 0;
                            font-size: 16px;
                            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
                            border-radius: 5px;
                            overflow: hidden;
                        }}

                        th, td {{
                            padding: 12px 15px;
                            text-align: right;
                        }}

                        th {{
                            background-color: #3498db;
                            color: white;
                            font-weight: 500;
                            text-transform: uppercase;
                            letter-spacing: 1px;
                        }}

                        tr:nth-child(even) {{
                            background-color: #f2f2f2;
                        }}

                        tr:hover {{
                            background-color: #e9f7fe;
                        }}

                        .totals {{
                            margin-top: 30px;
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 5px;
                            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                        }}

                        .total-row {{
                            display: flex;
                            justify-content: space-between;
                            padding: 8px 0;
                            border-bottom: 1px solid #eee;
                        }}

                        .total-row:last-child {{
                            border-bottom: none;
                            font-weight: 700;
                            font-size: 18px;
                            color: #2c3e50;
                        }}

                        .total-label {{
                            font-weight: 700;
                        }}

                        .signatures {{
                            display: flex;
                            justify-content: space-between;
                            margin-top: 50px;
                        }}

                        .signature {{
                            width: 200px;
                            text-align: center;
                            border-top: 1px solid #000;
                            padding-top: 5px;
                        }}

                        .footer {{
                            margin-top: 40px;
                            text-align: center;
                            font-size: 14px;
                            color: #95a5a6;
                            padding-top: 20px;
                            border-top: 1px solid #eee;
                        }}

                        .print-button {{
                            background-color: #3498db;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            font-size: 16px;
                            border-radius: 5px;
                            cursor: pointer;
                            margin: 20px auto;
                            display: block;
                        }}

                        .print-button:hover {{
                            background-color: #2980b9;
                        }}

                        @media print {{
                            .print-button {{
                                display: none;
                            }}
                            body {{
                                background-color: white;
                            }}
                            .container {{
                                box-shadow: none;
                                padding: 0;
                            }}
                        }}
                    </style>
                    <script>
                        function printInvoice() {{
                            window.print();
                        }}
                    </script>
                </head>
                <body>
                    <div class="container">
                        <button onclick="printInvoice()" class="print-button">طباعة الفاتورة</button>

                        <div class="header">
                            {f'<img src="{company_logo}" alt="Company Logo" class="logo">' if company_logo else ''}
                            <div class="title">{company_name}</div>
                            <div class="subtitle">فاتورة مشتريات</div>
                            <div>Purchase Invoice #{purchase_id}</div>
                        </div>

                        <div class="invoice-details">
                            <div class="supplier-details">
                                <div class="section-title">بيانات المورد</div>
                                <div class="detail-row">
                                    <div class="detail-label">الاسم:</div>
                                    <div class="detail-value">{supplier.name}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">رقم الهاتف:</div>
                                    <div class="detail-value">{supplier.phone or '-'}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">العنوان:</div>
                                    <div class="detail-value">{supplier.address or '-'}</div>
                                </div>
                            </div>

                            <div class="invoice-info">
                                <div class="section-title">بيانات الفاتورة</div>
                                <div class="detail-row">
                                    <div class="detail-label">رقم الفاتورة:</div>
                                    <div class="detail-value">{purchase_id}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">تاريخ الفاتورة:</div>
                                    <div class="detail-value">{purchase.purchase_date.strftime("%Y-%m-%d")}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">تاريخ الطباعة:</div>
                                    <div class="detail-value">{current_date}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">سعر الصرف:</div>
                                    <div class="detail-value">{purchase.exchange_rate:.3f} ريال/دولار</div>
                                </div>
                            </div>
                        </div>

                        <table>
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>نوع الألماس</th>
                                    <th>الوزن (قيراط)</th>
                                    <th>سعر القيراط ($)</th>
                                    <th>الإجمالي ($)</th>
                                    <th>الإجمالي (ريال)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>{purchase.diamond_type}</td>
                                    <td>{purchase.carat_weight:.3f}</td>
                                    <td>{purchase.price_per_carat_usd:.3f}</td>
                                    <td>{purchase.total_price_usd:.3f}</td>
                                    <td>{purchase.total_price_sar:.3f}</td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="totals">
                            <div class="total-row">
                                <div class="total-label">المبلغ الإجمالي ($):</div>
                                <div>{purchase.total_price_usd:.3f} $</div>
                            </div>
                            <div class="total-row">
                                <div class="total-label">المبلغ الإجمالي (ريال):</div>
                                <div>{purchase.total_price_sar:.3f} ريال</div>
                            </div>
                            <div class="total-row">
                                <div class="total-label">المبلغ المدفوع ($):</div>
                                <div>{purchase.amount_paid:.3f} $</div>
                            </div>
                            <div class="total-row">
                                <div class="total-label">المبلغ المتبقي ($):</div>
                                <div>{purchase.amount_due:.3f} $</div>
                            </div>
                        </div>

                        <div class="signatures">
                            <div class="signature">توقيع المورد</div>
                            <div class="signature">توقيع المسؤول</div>
                        </div>

                        <div class="footer">
                            <p>شكراً للتعامل معنا</p>
                            <p>{company_name}</p>
                            <p>تم إنشاء هذه الفاتورة في {current_date}</p>
                        </div>
                    </div>
                </body>
                </html>
                """

                # Write HTML content to file
                with open(invoice_filename, 'w', encoding='utf-8') as html_file:
                    html_file.write(html_content)

                # Open the HTML file in the default browser
                file_path = os.path.abspath(invoice_filename)
                webbrowser.open('file://' + file_path, new=2)

                # Show success message
                QMessageBox.information(self, "نجاح", f"تم إنشاء فاتورة المشتريات بنجاح\nالملف: {invoice_filename}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء الفاتورة: {str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء استرجاع بيانات المشتريات: {str(e)}")
        finally:
            session.close()