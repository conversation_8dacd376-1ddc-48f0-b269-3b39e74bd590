#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت بسيط لإعادة تعيين عدد محاولات تسجيل الدخول الفاشلة للمستخدم الافتراضي (admin)
"""

import sqlite3
import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import Qt
from logger import log_error, log_info

def reset_admin_login_attempts():
    """
    إعادة تعيين عدد محاولات تسجيل الدخول الفاشلة للمستخدم الافتراضي (admin)
    
    Returns:
        bool: True إذا تمت العملية بنجاح، False خلاف ذلك
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        # إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم admin
        cursor.execute("UPDATE users SET failed_login_attempts = 0 WHERE username = 'admin'")
        
        # حفظ التغييرات
        conn.commit()
        
        # إغلاق الاتصال
        conn.close()
        
        log_info("تم إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم الافتراضي (admin) بنجاح")
        print("تم إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم الافتراضي (admin) بنجاح")
        return True
    except Exception as e:
        error_msg = f"خطأ في إعادة تعيين محاولات تسجيل الدخول الفاشلة: {str(e)}"
        log_error(error_msg)
        print(error_msg)
        return False

class QuickResetAdminApp(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("إعادة تعيين محاولات تسجيل الدخول للمسؤول")
        self.setGeometry(300, 300, 400, 200)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # إنشاء التخطيط الرئيسي
        self.layout = QVBoxLayout(self)
        
        # إضافة عنوان
        title_label = QLabel("أداة إعادة تعيين محاولات تسجيل الدخول للمسؤول")
        title_font = title_label.font()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(title_label)
        
        # إضافة وصف
        description_label = QLabel("هذه الأداة ستقوم بإعادة تعيين عداد محاولات تسجيل الدخول الفاشلة لحساب المسؤول (admin)")
        description_label.setWordWrap(True)
        description_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(description_label)
        
        # إضافة زر الإعادة تعيين
        self.reset_button = QPushButton("إعادة تعيين محاولات تسجيل الدخول")
        self.reset_button.clicked.connect(self.reset_admin)
        self.layout.addWidget(self.reset_button)
    
    def reset_admin(self):
        """تنفيذ عملية إعادة تعيين محاولات المسؤول وعرض النتيجة"""
        if reset_admin_login_attempts():
            QMessageBox.information(self, "نجاح", "تم إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم الافتراضي (admin) بنجاح")
        else:
            QMessageBox.critical(self, "خطأ", "فشلت عملية إعادة تعيين محاولات تسجيل الدخول")


if __name__ == "__main__":
    # تأكد من وجود قاعدة البيانات
    if not os.path.exists('diamond_sales.db'):
        # محاولة البحث عن قاعدة البيانات في مستوى المجلد الأعلى
        parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        if os.path.exists(os.path.join(parent_dir, 'diamond_sales.db')):
            # تغيير الدليل الحالي إلى المجلد الأعلى
            os.chdir(parent_dir)
        else:
            # إذا لم يتم العثور على قاعدة البيانات
            QMessageBox.critical(None, "خطأ", "لم يتم العثور على قاعدة البيانات (diamond_sales.db).\nيرجى التأكد من تشغيل الأداة في مجلد البرنامج الصحيح.")
            sys.exit(1)
    
    # بدء تشغيل التطبيق
    app = QApplication(sys.argv)
    
    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    window = QuickResetAdminApp()
    window.show()
    sys.exit(app.exec())
