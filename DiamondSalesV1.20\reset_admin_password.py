"""
سكريبت لإعادة تعيين كلمة مرور المسؤول
"""

import sqlite3
import bcrypt
from logger import log_error, log_info

def reset_admin_password():
    """
    إعادة تعيين كلمة مرور المسؤول إلى القيمة الافتراضية
    
    Returns:
        bool: True إذا تمت العملية بنجاح، False خلاف ذلك
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        # كلمة المرور الجديدة
        new_password = '1'
        
        # تشفير كلمة المرور
        hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # تحديث كلمة مرور المسؤول
        cursor.execute("UPDATE users SET password_hash = ?, failed_login_attempts = 0 WHERE username = 'admin'", (hashed_password,))
        
        # حفظ التغييرات
        conn.commit()
        
        # إغلاق الاتصال
        conn.close()
        
        log_info("تم إعادة تعيين كلمة مرور المسؤول بنجاح")
        return True
    except Exception as e:
        log_error(f"خطأ في إعادة تعيين كلمة مرور المسؤول: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== أداة إعادة تعيين كلمة مرور المسؤول ===")
    print("سيتم إعادة تعيين كلمة مرور المسؤول إلى القيمة الافتراضية: 1")
    
    confirmation = input("هل أنت متأكد من أنك تريد المتابعة؟ (ن/لا): ")
    
    if confirmation.lower() not in ['ن', 'نعم', 'y', 'yes']:
        print("تم إلغاء العملية.")
    else:
        if reset_admin_password():
            print("\nتم إعادة تعيين كلمة مرور المسؤول بنجاح.")
            print("يمكنك الآن تسجيل الدخول باستخدام:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: Admin@123")
        else:
            print("فشلت عملية إعادة تعيين كلمة مرور المسؤول. يرجى مراجعة سجل الأخطاء.")
