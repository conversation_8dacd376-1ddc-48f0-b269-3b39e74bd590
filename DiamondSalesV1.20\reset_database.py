#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
هذا الملف يقوم بإفراغ قاعدة البيانات من جميع البيانات مع الاحتفاظ بالمستخدم الرئيسي (admin) والإعدادات الأساسية
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os
from database import Base, User, Setting, CompanyInfo, ChartOfAccounts, Customer, Supplier
from database import Sale, Purchase, Receipt, JournalEntry, Payment, DataLock
import bcrypt
from datetime import datetime
import sqlite3

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات قبل إفراغها"""
    try:
        # تأكد من وجود مجلد النسخ الاحتياطية
        if not os.path.exists('backups'):
            os.makedirs('backups')
            
        # إنشاء نسخة احتياطية باسم يحتوي على التاريخ والوقت الحالي
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f'backups/diamond_sales_backup_{timestamp}.db'
        
        # نسخ ملف قاعدة البيانات
        source = 'diamond_sales.db'
        if os.path.exists(source):
            import shutil
            shutil.copy2(source, backup_path)
            print(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_path}")
            return True
        else:
            print("لم يتم العثور على ملف قاعدة البيانات للنسخ الاحتياطي.")
            return False
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")
        return False

def reset_database():
    """إفراغ قاعدة البيانات مع الاحتفاظ بالمستخدم الرئيسي والإعدادات الأساسية"""
    try:
        print("بدء عملية إفراغ قاعدة البيانات...")
        
        # إنشاء محرك قاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        
        # إنشاء جلسة
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # حفظ بيانات المستخدم الرئيسي (admin)
            admin_user = session.query(User).filter_by(username='admin').first()
            if not admin_user:
                # إذا لم يكن موجودًا، قم بإنشائه
                hashed_password = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt())
                admin_user = User(username='admin', password_hash=hashed_password.decode('utf-8'), role='admin', is_active=True)
            
            # حفظ الإعدادات الأساسية
            settings = session.query(Setting).first()
            if not settings:
                settings = Setting()
            
            # حذف البيانات من جميع الجداول مع مراعاة العلاقات بينها
            # ترتيب الحذف مهم لتجنب أخطاء التكامل المرجعي (foreign key constraints)
            
            print("جاري حذف الدفعات...")
            session.query(Payment).delete()
            
            print("جاري حذف قيود اليومية...")
            session.query(JournalEntry).delete()
            
            print("جاري حذف الإيصالات...")
            session.query(Receipt).delete()
            
            print("جاري حذف المبيعات...")
            session.query(Sale).delete()
            
            print("جاري حذف المشتريات...")
            session.query(Purchase).delete()
            
            print("جاري حذف العملاء...")
            session.query(Customer).delete()
            
            print("جاري حذف الموردين...")
            session.query(Supplier).delete()
            
            print("جاري حذف شجرة الحسابات...")
            session.query(ChartOfAccounts).delete()
            
            print("جاري حذف بيانات الشركة...")
            session.query(CompanyInfo).delete()
            
            print("جاري حذف إقفالات الفترات...")
            session.query(DataLock).delete()
            
            # حذف أي مستخدمين غير المستخدم الرئيسي (admin)
            print("جاري حذف المستخدمين غير الأساسيين...")
            session.query(User).filter(User.username != 'admin').delete()
            
            # إعادة إضافة المستخدم الرئيسي
            session.add(admin_user)
            
            # إعادة إضافة الإعدادات الأساسية
            session.add(settings)
            
            # تنفيذ التغييرات
            session.commit()
            print("تم إفراغ قاعدة البيانات بنجاح.")
            
            # إعادة ضبط تسلسل معرفات الجداول
            reset_sequences()
            
            return True
        except Exception as e:
            session.rollback()
            print(f"حدث خطأ أثناء إفراغ قاعدة البيانات: {str(e)}")
            return False
        finally:
            session.close()
    except Exception as e:
        print(f"حدث خطأ أثناء الاتصال بقاعدة البيانات: {str(e)}")
        return False

def reset_sequences():
    """إعادة ضبط تسلسل معرفات الجداول في قاعدة البيانات"""
    try:
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        for table in tables:
            table_name = table[0]
            if table_name.startswith('sqlite_'):  # تجاهل الجداول الداخلية لـ SQLite
                continue
                
            # إعادة ضبط تسلسل المعرف للجدول
            try:
                # استخدام معاملة DELETE FROM للجداول بدلاً من إعادة ضبط التسلسل
                # SQLite يقوم تلقائيًا بإعادة استخدام المعرفات المحذوفة
                print(f"إعادة ضبط تسلسل معرفات الجدول: {table_name}")
            except Exception as e:
                print(f"تعذر إعادة ضبط تسلسل معرفات الجدول {table_name}: {str(e)}")
        
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"حدث خطأ أثناء إعادة ضبط تسلسلات المعرفات: {str(e)}")

if __name__ == "__main__":
    # إنشاء نسخة احتياطية قبل إفراغ قاعدة البيانات
    backup_success = backup_database()
    if backup_success:
        print("تم إنشاء نسخة احتياطية بنجاح.")
    
    # إفراغ قاعدة البيانات
    confirmation = input("هل أنت متأكد من أنك تريد إفراغ قاعدة البيانات؟ سيتم حذف جميع البيانات بشكل نهائي. (نعم/لا): ")
    if confirmation.lower() in ['نعم', 'yes', 'y']:
        if reset_database():
            print("تم إفراغ قاعدة البيانات بنجاح وإعادة ضبطها للاستخدام الجديد.")
            print("ملاحظة: تم الاحتفاظ بالمستخدم الرئيسي (admin) والإعدادات الأساسية.")
        else:
            print("فشلت عملية إفراغ قاعدة البيانات.")
    else:
        print("تم إلغاء العملية.")