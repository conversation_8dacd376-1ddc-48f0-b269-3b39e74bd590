#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت لإعادة تعيين عدد محاولات تسجيل الدخول الفاشلة للمستخدم
"""

import sqlite3
import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt6.QtWidgets import QLabel, QComboBox, QMessageBox, QTableWidget, QTableWidgetItem, QHeaderView
from PyQt6.QtCore import Qt
from logger import log_error, log_info

def reset_login_attempts(username=None):
    """
    إعادة تعيين عدد محاولات تسجيل الدخول الفاشلة للمستخدم
    
    Args:
        username (str, optional): اسم المستخدم. إذا لم يتم تحديده، سيتم إعادة تعيين محاولات جميع المستخدمين.
        
    Returns:
        bool: True إذا تمت العملية بنجاح، False خلاف ذلك
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        if username:
            # التحقق من وجود المستخدم
            cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (username,))
            if cursor.fetchone()[0] == 0:
                print(f"خطأ: المستخدم {username} غير موجود.")
                return False
                
            # إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم المحدد
            cursor.execute("UPDATE users SET failed_login_attempts = 0 WHERE username = ?", (username,))
            log_info(f"تم إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم: {username}")
            print(f"تم إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم: {username}")
        else:
            # إعادة تعيين محاولات تسجيل الدخول الفاشلة لجميع المستخدمين
            cursor.execute("UPDATE users SET failed_login_attempts = 0")
            log_info("تم إعادة تعيين محاولات تسجيل الدخول الفاشلة لجميع المستخدمين")
            print("تم إعادة تعيين محاولات تسجيل الدخول الفاشلة لجميع المستخدمين")
        
        # حفظ التغييرات
        conn.commit()
        
        # إغلاق الاتصال
        conn.close()
        return True
    except Exception as e:
        error_msg = f"خطأ في إعادة تعيين محاولات تسجيل الدخول الفاشلة: {str(e)}"
        log_error(error_msg)
        print(error_msg)
        return False

def list_users():
    """
    عرض قائمة المستخدمين وعدد محاولات تسجيل الدخول الفاشلة لكل منهم
    
    Returns:
        bool: True إذا تمت العملية بنجاح، False خلاف ذلك
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        # الحصول على قائمة المستخدمين وعدد محاولات تسجيل الدخول الفاشلة
        cursor.execute("SELECT username, failed_login_attempts FROM users ORDER BY username")
        users = cursor.fetchall()
        
        # عرض القائمة
        print("\nقائمة المستخدمين ومحاولات تسجيل الدخول الفاشلة:")
        print("=" * 50)
        print(f"{'اسم المستخدم':<20} {'عدد المحاولات الفاشلة':<20}")
        print("-" * 50)
        for user in users:
            username, attempts = user
            print(f"{username:<20} {attempts:<20}")
        print("=" * 50)
        
        # إغلاق الاتصال
        conn.close()
        return True
    except Exception as e:
        error_msg = f"خطأ في عرض قائمة المستخدمين: {str(e)}"
        log_error(error_msg)
        print(error_msg)
        return False

class ResetLoginAttemptsApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("أداة إعادة تعيين محاولات تسجيل الدخول الفاشلة")
        self.setGeometry(300, 300, 600, 400)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # إنشاء الكائن المركزي
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # إنشاء التخطيط الرئيسي
        self.layout = QVBoxLayout(self.central_widget)
        
        # إضافة عنوان
        title_label = QLabel("أداة إعادة تعيين محاولات تسجيل الدخول الفاشلة")
        title_font = title_label.font()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(title_label)
        
        # إنشاء جدول لعرض المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(2)
        self.users_table.setHorizontalHeaderLabels(["اسم المستخدم", "عدد المحاولات الفاشلة"])
        self.users_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.users_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.layout.addWidget(self.users_table)
        
        # إضافة قائمة منسدلة للمستخدمين
        self.user_label = QLabel("اختر المستخدم:")
        self.layout.addWidget(self.user_label)
        
        self.user_combo = QComboBox()
        self.layout.addWidget(self.user_combo)
        
        # إضافة أزرار العمليات
        self.reset_selected_button = QPushButton("إعادة تعيين محاولات المستخدم المحدد")
        self.reset_selected_button.clicked.connect(self.reset_selected_user)
        self.layout.addWidget(self.reset_selected_button)
        
        self.reset_all_button = QPushButton("إعادة تعيين محاولات جميع المستخدمين")
        self.reset_all_button.clicked.connect(self.reset_all_users)
        self.layout.addWidget(self.reset_all_button)
        
        # جلب قائمة المستخدمين وعرضها
        self.load_users()
        
    def load_users(self):
        """تحميل قائمة المستخدمين وعرضها في الجدول والقائمة المنسدلة"""
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect('diamond_sales.db')
            cursor = conn.cursor()
            
            # الحصول على قائمة المستخدمين وعدد محاولات تسجيل الدخول الفاشلة
            cursor.execute("SELECT username, failed_login_attempts FROM users ORDER BY username")
            users = cursor.fetchall()
            
            # تفريغ الجدول والقائمة المنسدلة
            self.users_table.setRowCount(0)
            self.user_combo.clear()
            
            # ملء الجدول والقائمة المنسدلة
            for row, user in enumerate(users):
                username, attempts = user
                self.users_table.insertRow(row)
                self.users_table.setItem(row, 0, QTableWidgetItem(username))
                self.users_table.setItem(row, 1, QTableWidgetItem(str(attempts)))
                
                # إضافة المستخدم إلى القائمة المنسدلة
                self.user_combo.addItem(username)
            
            # إغلاق الاتصال
            conn.close()
        except Exception as e:
            error_msg = f"خطأ في تحميل قائمة المستخدمين: {str(e)}"
            log_error(error_msg)
            QMessageBox.critical(self, "خطأ", error_msg)
    
    def reset_selected_user(self):
        """إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم المحدد"""
        username = self.user_combo.currentText()
        if not username:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مستخدم من القائمة")
            return
            
        # تأكيد العملية
        reply = QMessageBox.question(
            self, "تأكيد", 
            f"هل أنت متأكد من إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم {username}؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if reset_login_attempts(username):
                QMessageBox.information(self, "نجاح", f"تم إعادة تعيين محاولات تسجيل الدخول الفاشلة للمستخدم {username}")
                # إعادة تحميل قائمة المستخدمين
                self.load_users()
            else:
                QMessageBox.critical(self, "خطأ", "فشلت عملية إعادة تعيين محاولات تسجيل الدخول")
    
    def reset_all_users(self):
        """إعادة تعيين محاولات تسجيل الدخول الفاشلة لجميع المستخدمين"""
        # تأكيد العملية
        reply = QMessageBox.question(
            self, "تأكيد", 
            "هل أنت متأكد من إعادة تعيين محاولات تسجيل الدخول الفاشلة لجميع المستخدمين؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if reset_login_attempts():
                QMessageBox.information(self, "نجاح", "تم إعادة تعيين محاولات تسجيل الدخول الفاشلة لجميع المستخدمين")
                # إعادة تحميل قائمة المستخدمين
                self.load_users()
            else:
                QMessageBox.critical(self, "خطأ", "فشلت عملية إعادة تعيين محاولات تسجيل الدخول")


if __name__ == "__main__":
    # تأكد من وجود قاعدة البيانات
    if not os.path.exists('diamond_sales.db'):
        # محاولة البحث عن قاعدة البيانات في مستوى المجلد الأعلى
        parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        if os.path.exists(os.path.join(parent_dir, 'diamond_sales.db')):
            # تغيير الدليل الحالي إلى المجلد الأعلى
            os.chdir(parent_dir)
        else:
            # إذا لم يتم العثور على قاعدة البيانات
            QMessageBox.critical(None, "خطأ", "لم يتم العثور على قاعدة البيانات (diamond_sales.db).\nيرجى التأكد من تشغيل الأداة في مجلد البرنامج الصحيح.")
            sys.exit(1)

    # بدء تشغيل التطبيق
    app = QApplication(sys.argv)
    
    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    window = ResetLoginAttemptsApp()
    window.show()
    sys.exit(app.exec())
