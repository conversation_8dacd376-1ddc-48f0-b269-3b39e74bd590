#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت لإعادة تعيين كلمة المرور في حالة نسيانها
"""

import sqlite3
import sys
import os
import secrets
import string
import bcrypt
from datetime import datetime, timedelta
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt6.QtWidgets import QLabel, QLineEdit, QComboBox, QMessageBox, QHBoxLayout, QGridLayout
from PyQt6.QtGui import QIcon, QFont
from PyQt6.QtCore import Qt, QSize
from logger import log_error, log_info
from database import is_strong_password, hash_password

# استيراد وحدة البريد الإلكتروني
try:
    from email_utils import send_verification_email
    EMAIL_MODULE_AVAILABLE = True
except ImportError:
    EMAIL_MODULE_AVAILABLE = False
    log_error("فشل في استيراد وحدة البريد الإلكتروني. سيتم عرض رموز التحقق بدلاً من إرسالها.")

def generate_random_password():
    """توليد كلمة مرور عشوائية قوية"""
    # تعريف مجموعات الأحرف
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*"

    # التأكد من وجود حرف واحد على الأقل من كل مجموعة
    password = [
        secrets.choice(lowercase),
        secrets.choice(uppercase),
        secrets.choice(digits),
        secrets.choice(special)
    ]

    # إضافة 6 أحرف عشوائية أخرى
    all_chars = lowercase + uppercase + digits + special
    password.extend(secrets.choice(all_chars) for _ in range(6))

    # خلط الأحرف
    pw_list = list(password)
    secrets.SystemRandom().shuffle(pw_list)

    return ''.join(pw_list)

class ResetPasswordApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.email_verified = False
        self.verification_code = None
        self.selected_username = None
        self.init_ui()
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        try:
            # محاولة استيراد وحدة توسيط النوافذ
            from ui_utils import center_window
            center_window(self)
        except ImportError:
            # إذا لم تكن الوحدة متوفرة، توسيط النافذة يدويًا
            from PyQt6.QtWidgets import QDesktopWidget
            qr = self.frameGeometry()
            cp = QDesktopWidget().availableGeometry().center()
            qr.moveCenter(cp)
            self.move(qr.topLeft())

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("استعادة كلمة المرور")
        self.setGeometry(300, 300, 450, 350)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # محاولة تحميل الأيقونة
        icon_paths = ['assets/login_icon.svg', 'assets/key_icon.ico', 'icons/key_icon.ico']
        icon_loaded = False
        for path in icon_paths:
            try:
                if os.path.exists(path):
                    self.setWindowIcon(QIcon(path))
                    icon_loaded = True
                    break
            except Exception as e:
                log_error(f"فشل في تحميل الأيقونة {path}: {str(e)}")
        
        if not icon_loaded:
            log_info("لم يتم العثور على أي أيقونة، سيتم استخدام الأيقونة الافتراضية")

        # إنشاء الكائن المركزي
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # إنشاء التخطيط الرئيسي
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # إضافة عنوان
        title_label = QLabel("استعادة كلمة المرور")
        title_font = title_label.font()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_layout.addWidget(title_label)
        
        # إضافة وصف
        description_label = QLabel("يرجى إدخال اسم المستخدم والبريد الإلكتروني المسجل به")
        description_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_layout.addWidget(description_label)
        
        # إنشاء نموذج الإدخال
        form_layout = QGridLayout()
        
        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        self.username_combo = QComboBox()
        self.username_combo.setEditable(True)
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_combo, 0, 1)
        
        # البريد الإلكتروني
        email_label = QLabel("البريد الإلكتروني:")
        self.email_input = QLineEdit()
        form_layout.addWidget(email_label, 1, 0)
        form_layout.addWidget(self.email_input, 1, 1)
        
        self.main_layout.addLayout(form_layout)
        
        # زر التحقق من البريد الإلكتروني
        self.verify_email_button = QPushButton("التحقق من البريد الإلكتروني")
        self.verify_email_button.clicked.connect(self.verify_email)
        self.main_layout.addWidget(self.verify_email_button)
        
        # حقل رمز التحقق (مخفي في البداية)
        self.verification_section = QWidget()
        verification_layout = QHBoxLayout(self.verification_section)
        
        verification_code_label = QLabel("رمز التحقق:")
        self.verification_code_input = QLineEdit()
        verification_layout.addWidget(verification_code_label)
        verification_layout.addWidget(self.verification_code_input)
        
        self.verify_code_button = QPushButton("تأكيد الرمز")
        self.verify_code_button.clicked.connect(self.verify_code)
        verification_layout.addWidget(self.verify_code_button)
        
        self.verification_section.setVisible(False)
        self.main_layout.addWidget(self.verification_section)
        
        # حقل كلمة المرور الجديدة (مخفي في البداية)
        self.new_password_section = QWidget()
        password_layout = QGridLayout(self.new_password_section)
        
        new_password_label = QLabel("كلمة المرور الجديدة:")
        self.new_password_input = QLineEdit()
        self.new_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(new_password_label, 0, 0)
        password_layout.addWidget(self.new_password_input, 0, 1)
        
        confirm_password_label = QLabel("تأكيد كلمة المرور:")
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(confirm_password_label, 1, 0)
        password_layout.addWidget(self.confirm_password_input, 1, 1)
        
        # إضافة مؤشر لقوة كلمة المرور
        password_strength_label = QLabel("قوة كلمة المرور:")
        self.password_strength_indicator = QLabel("---")
        password_layout.addWidget(password_strength_label, 2, 0)
        password_layout.addWidget(self.password_strength_indicator, 2, 1)
        
        # إضافة حدث لتتبع تغيرات كلمة المرور
        self.new_password_input.textChanged.connect(self.update_password_strength)
        
        self.generate_password_button = QPushButton("توليد كلمة مرور عشوائية")
        self.generate_password_button.clicked.connect(self.generate_password)
        password_layout.addWidget(self.generate_password_button, 3, 0, 1, 2)
        
        self.new_password_section.setVisible(False)
        self.main_layout.addWidget(self.new_password_section)
        
        # زر إعادة تعيين كلمة المرور
        self.reset_password_button = QPushButton("إعادة تعيين كلمة المرور")
        self.reset_password_button.clicked.connect(self.reset_password)
        self.reset_password_button.setVisible(False)
        self.main_layout.addWidget(self.reset_password_button)
        
        # إضافة زر العودة
        self.back_button = QPushButton("العودة")
        self.back_button.clicked.connect(self.close)
        self.main_layout.addWidget(self.back_button)
        
        # تحميل قائمة المستخدمين
        self.load_users()

    def update_password_strength(self):
        """تحديث مؤشر قوة كلمة المرور"""
        password = self.new_password_input.text()
        if not password:
            self.password_strength_indicator.setText("---")
            self.password_strength_indicator.setStyleSheet("")
            return
        
        # حساب قوة كلمة المرور
        strength = 0
        feedback = []
        
        # التحقق من الطول
        if len(password) >= 8:
            strength += 1
        else:
            feedback.append("قصيرة جداً")
            
        # التحقق من وجود الأحرف الكبيرة
        if any(c.isupper() for c in password):
            strength += 1
        else:
            feedback.append("لا توجد أحرف كبيرة")
            
        # التحقق من وجود الأحرف الصغيرة
        if any(c.islower() for c in password):
            strength += 1
        else:
            feedback.append("لا توجد أحرف صغيرة")
            
        # التحقق من وجود الأرقام
        if any(c.isdigit() for c in password):
            strength += 1
        else:
            feedback.append("لا توجد أرقام")
            
        # التحقق من وجود أحرف خاصة
        if any(c in string.punctuation for c in password):
            strength += 1
        else:
            feedback.append("لا توجد أحرف خاصة")
        
        # تحديد النص واللون بناءً على القوة
        if strength == 0:
            text = "ضعيفة جداً"
            color = "darkred"
        elif strength == 1:
            text = "ضعيفة"
            color = "red"
        elif strength == 2:
            text = "متوسطة"
            color = "orange"
        elif strength == 3:
            text = "جيدة"
            color = "yellowgreen"
        elif strength == 4:
            text = "قوية"
            color = "green"
        else:
            text = "ممتازة"
            color = "darkgreen"
        
        # عرض التقييم
        if feedback:
            text += f" ({', '.join(feedback)})"
        
        self.password_strength_indicator.setText(text)
        self.password_strength_indicator.setStyleSheet(f"color: {color}; font-weight: bold;")

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect('diamond_sales.db')
            cursor = conn.cursor()
            
            # الحصول على قائمة المستخدمين
            cursor.execute("SELECT username FROM users ORDER BY username")
            users = cursor.fetchall()
            
            # إضافة المستخدمين إلى القائمة المنسدلة
            for user in users:
                self.username_combo.addItem(user[0])
            
            # إغلاق الاتصال
            conn.close()
        except Exception as e:
            error_msg = f"خطأ في تحميل قائمة المستخدمين: {str(e)}"
            log_error(error_msg)
            QMessageBox.critical(self, "خطأ", error_msg)

    def verify_email(self):
        """التحقق من البريد الإلكتروني"""
        username = self.username_combo.currentText().strip()
        email = self.email_input.text().strip()

        if not username:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال اسم المستخدم")
            return

        if not email:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال البريد الإلكتروني")
            return

        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect('diamond_sales.db')
            cursor = conn.cursor()
            
            # التحقق من وجود المستخدم والبريد الإلكتروني
            cursor.execute("SELECT email FROM users WHERE username = ?", (username,))
            result = cursor.fetchone()
            
            if not result:
                QMessageBox.warning(self, "تنبيه", "اسم المستخدم غير موجود")
                conn.close()
                return
            
            if not result[0]:
                QMessageBox.warning(self, "تنبيه", "لا يوجد بريد إلكتروني مسجل لهذا المستخدم")
                conn.close()
                return
            
            if result[0].lower() != email.lower():
                QMessageBox.warning(self, "تنبيه", "البريد الإلكتروني غير مطابق للمستخدم")
                conn.close()
                return
            
            # إنشاء رمز التحقق
            self.verification_code = ''.join(secrets.choice(string.digits) for _ in range(6))
            
            # تخزين الرمز في قاعدة البيانات
            expiry_time = datetime.now() + timedelta(minutes=15)  # الرمز صالح لمدة 15 دقيقة
            cursor.execute(
                "UPDATE users SET reset_password_token = ?, reset_password_expires = ? WHERE username = ?",
                (self.verification_code, expiry_time.isoformat(), username)
            )
            conn.commit()
            
            # محاولة إرسال رمز التحقق بالبريد الإلكتروني
            if EMAIL_MODULE_AVAILABLE:
                success, message = send_verification_email(email, self.verification_code, username)
                if success:
                    QMessageBox.information(
                        self,
                        "رمز التحقق",
                        "تم إرسال رمز التحقق إلى بريدك الإلكتروني."
                    )
                else:
                    # إذا فشل الإرسال، نعرض الخطأ ونعرض الرمز للمستخدم
                    QMessageBox.warning(
                        self,
                        "تنبيه",
                        f"تعذر إرسال البريد الإلكتروني: {message}\n\nلكنك لا تزال تستطيع المتابعة باستخدام هذا الرمز: {self.verification_code}"
                    )
            else:
                # إذا كانت وحدة البريد الإلكتروني غير متوفرة، نعرض الرمز مباشرة
                QMessageBox.information(
                    self,
                    "رمز التحقق",
                    f"تم إنشاء رمز التحقق.\n\nملاحظة: بما أنه لا يوجد خادم بريد حقيقي متصل بالنظام، فإليك الرمز: {self.verification_code}"
                )
            
            # حفظ اسم المستخدم المحدد
            self.selected_username = username
            
            # إظهار قسم رمز التحقق
            self.verification_section.setVisible(True)
            
            # إغلاق الاتصال
            conn.close()
            
            log_info(f"تم إرسال رمز التحقق للمستخدم {username}")
            
        except Exception as e:
            error_msg = f"خطأ في التحقق من البريد الإلكتروني: {str(e)}"
            log_error(error_msg)
            QMessageBox.critical(self, "خطأ", error_msg)

    def verify_code(self):
        """التحقق من رمز التأكيد"""
        if not self.verification_code or not self.selected_username:
            QMessageBox.warning(self, "تنبيه", "الرجاء التحقق من البريد الإلكتروني أولاً")
            return
        
        input_code = self.verification_code_input.text().strip()
        
        if not input_code:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رمز التحقق")
            return
        
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect('diamond_sales.db')
            cursor = conn.cursor()
            
            # التحقق من الرمز
            cursor.execute(
                "SELECT reset_password_token, reset_password_expires FROM users WHERE username = ?",
                (self.selected_username,)
            )
            result = cursor.fetchone()
            
            if not result or not result[0]:
                QMessageBox.warning(self, "تنبيه", "رمز التحقق غير صالح")
                conn.close()
                return
            
            stored_code = result[0]
            expiry_time = result[1]
            
            if stored_code != input_code:
                QMessageBox.warning(self, "تنبيه", "رمز التحقق غير صحيح")
                conn.close()
                return
            
            # التحقق من صلاحية الرمز
            if datetime.now() > datetime.fromisoformat(expiry_time):
                QMessageBox.warning(self, "تنبيه", "انتهت صلاحية رمز التحقق")
                conn.close()
                return
            
            # تم التحقق بنجاح
            self.email_verified = True
            
            # إظهار قسم كلمة المرور الجديدة وزر إعادة التعيين
            self.new_password_section.setVisible(True)
            self.reset_password_button.setVisible(True)
            
            # إغلاق الاتصال
            conn.close()
            
            log_info(f"تم التحقق من رمز التأكيد للمستخدم {self.selected_username}")
            
        except Exception as e:
            error_msg = f"خطأ في التحقق من رمز التأكيد: {str(e)}"
            log_error(error_msg)
            QMessageBox.critical(self, "خطأ", error_msg)

    def generate_password(self):
        """توليد كلمة مرور عشوائية"""
        password = generate_random_password()
        self.new_password_input.setText(password)
        self.confirm_password_input.setText(password)
        self.update_password_strength()
        
        # عرض كلمة المرور للمستخدم
        QMessageBox.information(
            self,
            "كلمة المرور المولدة",
            f"تم توليد كلمة مرور عشوائية قوية:\n\n{password}\n\nتأكد من حفظها في مكان آمن."
        )

    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        if not self.email_verified or not self.selected_username:
            QMessageBox.warning(self, "تنبيه", "الرجاء التحقق من البريد الإلكتروني ورمز التأكيد أولاً")
            return
        
        new_password = self.new_password_input.text()
        confirm_password = self.confirm_password_input.text()
        
        if not new_password:
            QMessageBox.warning(self, "تنبيه", "الرجاء إدخال كلمة المرور الجديدة")
            return
        
        if new_password != confirm_password:
            QMessageBox.warning(self, "تنبيه", "كلمتي المرور غير متطابقتين")
            return
        
        try:
            # التحقق من قوة كلمة المرور
            if not is_strong_password(new_password):
                QMessageBox.warning(
                    self,
                    "كلمة مرور ضعيفة",
                    "كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل، وحرف كبير، وحرف صغير، ورقم، وحرف خاص."
                )
                return
            
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect('diamond_sales.db')
            cursor = conn.cursor()
            
            # تحديث كلمة المرور ومسح رمز التحقق
            hashed_password = hash_password(new_password)
            cursor.execute(
                "UPDATE users SET password_hash = ?, reset_password_token = NULL, reset_password_expires = NULL, failed_login_attempts = 0 WHERE username = ?",
                (hashed_password, self.selected_username)
            )
            conn.commit()
            
            # إغلاق الاتصال
            conn.close()
            
            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                "تم بنجاح",
                "تم إعادة تعيين كلمة المرور بنجاح.\nيمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة."
            )
            
            log_info(f"تم إعادة تعيين كلمة المرور للمستخدم {self.selected_username}")
            
            # إغلاق النافذة
            self.close()
            
        except Exception as e:
            error_msg = f"خطأ في إعادة تعيين كلمة المرور: {str(e)}"
            log_error(error_msg)
            QMessageBox.critical(self, "خطأ", error_msg)


if __name__ == "__main__":
    # تأكد من وجود قاعدة البيانات
    if not os.path.exists('diamond_sales.db'):
        # محاولة البحث عن قاعدة البيانات في مستوى المجلد الأعلى
        parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        if os.path.exists(os.path.join(parent_dir, 'diamond_sales.db')):
            # تغيير الدليل الحالي إلى المجلد الأعلى
            os.chdir(parent_dir)
        else:
            # إذا لم يتم العثور على قاعدة البيانات
            print("خطأ: لم يتم العثور على قاعدة البيانات (diamond_sales.db)")
            print("يرجى التأكد من تشغيل الأداة في مجلد البرنامج الصحيح.")
            if QApplication.instance() is None:
                app = QApplication(sys.argv)
                QMessageBox.critical(None, "خطأ", "لم يتم العثور على قاعدة البيانات (diamond_sales.db).\nيرجى التأكد من تشغيل الأداة في مجلد البرنامج الصحيح.")
            sys.exit(1)

    # بدء تشغيل التطبيق
    app = QApplication(sys.argv)
    
    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    window = ResetPasswordApp()
    window.show()
    sys.exit(app.exec())