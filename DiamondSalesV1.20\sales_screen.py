from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QPushButton, QLabel, QLineEdit, QComboBox,
                            QTableWidget, QTableWidgetItem, QFormLayout,
                            QDateEdit, QDoubleSpinBox, QMessageBox, QSpinBox,
                            QHeaderView, QDialog, QDialogButtonBox, QTextEdit,
                            QSizePolicy)
from PyQt6.QtCore import Qt, QDate, QTimer
from PyQt6.QtGui import QFont
from sqlalchemy import desc
from datetime import datetime

from database import Customer, Sale, JournalEntry, ChartOfAccounts, Receipt, Category, Unit, صندوق_النقدية, حركة_نقدية
from db_session import session_scope
from logger import log_error, log_info
from ui_utils import style_button, style_dialog_buttons
from translations import get_translation as _

class SalesScreen(QWidget):
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.unsaved_changes = False  # متغير لتتبع التغييرات غير المحفوظة
        self.init_ui()
        self.load_sales_data()

    def init_ui(self):
        self.setWindowTitle(_("sales_title", "نظام إدارة مبيعات الألماس - شاشة المبيعات"))
        self.setGeometry(100, 100, 1000, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.sales_tab = QWidget()
        self.new_sale_tab = QWidget()

        # Setup tabs
        self.setup_sales_tab()
        self.setup_new_sale_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.sales_tab, "قائمة المبيعات")
        self.tab_widget.addTab(self.new_sale_tab, "إضافة عملية بيع جديدة")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_sales_tab(self):
        # Create layout for sales tab
        layout = QVBoxLayout(self.sales_tab)

        # Create search controls
        search_layout = QHBoxLayout()

        self.search_customer = QLineEdit()
        self.search_customer.setPlaceholderText("بحث باسم العميل")

        self.search_date_from = QDateEdit()
        self.search_date_from.setDisplayFormat("dd/MM/yyyy")
        self.search_date_from.setCalendarPopup(True)
        self.search_date_from.setDate(QDate.currentDate().addDays(-30))  # Last 30 days by default

        self.search_date_to = QDateEdit()
        self.search_date_to.setDisplayFormat("dd/MM/yyyy")
        self.search_date_to.setCalendarPopup(True)
        self.search_date_to.setDate(QDate.currentDate())

        # Inicializar el combo de búsqueda por نوع الصنف
        self.search_diamond_type = QComboBox()
        self.search_diamond_type.addItem("جميع الأنواع")        # تحميل الأصناف في قائمة البحث
        with session_scope() as session:
            categories = session.query(Category).all()
            for category in categories:
                self.search_diamond_type.addItem(category.name)
                
        search_button = QPushButton("بحث")
        search_button.clicked.connect(self.search_sales)
        style_button(search_button, "info", min_width=120, min_height=35)

        search_layout.addWidget(QLabel(_("customer_name_label", "اسم العميل:")))
        search_layout.addWidget(self.search_customer)
        search_layout.addWidget(QLabel(_("from_date_label", "من تاريخ:")))
        search_layout.addWidget(self.search_date_from)
        search_layout.addWidget(QLabel(_("to_date_label", "إلى تاريخ:")))
        search_layout.addWidget(self.search_date_to)
        search_layout.addWidget(QLabel(_("diamond_type_label", "نوع الألماس:")))
        search_layout.addWidget(self.search_diamond_type)
        search_layout.addWidget(search_button)

        # Create sales table
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(11)
        self.sales_table.setHorizontalHeaderLabels([
            "رقم المبيعات", "اسم العميل", "نوع الألماس", "الوزن بالقيراط",
            "السعر/قيراط ($)", "الإجمالي ($)", "الإجمالي (ريال)",
            "تاريخ البيع", "المبلغ المدفوع", "المبلغ المتبقي", "ملاحظات"
        ])
        header = self.sales_table.horizontalHeader()
        for i in range(11):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)        # Create action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(20)  # زيادة المسافة بين الأزرار
        action_layout.setContentsMargins(5, 5, 5, 15)  # هوامش لمجموعة الأزرار

        view_button = QPushButton("عرض التفاصيل")
        view_button.clicked.connect(self.view_sale_details)
        style_button(view_button, "info")

        edit_button = QPushButton("تعديل")
        edit_button.clicked.connect(self.edit_sale)
        style_button(edit_button, "edit")

        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_sale)
        style_button(delete_button, "delete")

        create_receipt_button = QPushButton("إنشاء سند قبض")
        create_receipt_button.clicked.connect(self.create_receipt)
        style_button(create_receipt_button, "success")

        print_button = QPushButton("طباعة فاتورة")
        print_button.clicked.connect(self.print_invoice)
        style_button(print_button, "warning")

        action_layout.addWidget(view_button)
        action_layout.addWidget(edit_button)
        action_layout.addWidget(delete_button)
        action_layout.addWidget(create_receipt_button)
        action_layout.addWidget(print_button)

        # Add widgets to layout
        layout.addLayout(search_layout)
        layout.addWidget(self.sales_table)
        layout.addLayout(action_layout)

    def setup_new_sale_tab(self):
        # Create layout for new sale tab
        layout = QVBoxLayout(self.new_sale_tab)

        # Create form layout for sale details
        form_layout = QFormLayout()

        # Customer selection
        self.customer_combo = QComboBox()
        self.load_customers()

        # Date field
        self.sale_date = QDateEdit()
        self.sale_date.setDisplayFormat("dd/MM/yyyy")
        self.sale_date.setCalendarPopup(True)
        self.sale_date.setDate(QDate.currentDate())

        # Create exchange rate input
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.01, 10.0)
        self.exchange_rate_spin.setDecimals(3)
        self.exchange_rate_spin.setSingleStep(0.01)
        self.exchange_rate_spin.setValue(3.75)  # Default SAR to USD rate
        self.exchange_rate_spin.valueChanged.connect(self.calculate_invoice_totals)

        # Notes field
        self.notes_text = QTextEdit()
        self.notes_text.setPlaceholderText("أدخل ملاحظات إضافية حول عملية البيع (اختياري)")
        self.notes_text.setMaximumHeight(100)

        # Add basic fields to form layout
        form_layout.addRow("العميل:", self.customer_combo)
        form_layout.addRow("تاريخ البيع:", self.sale_date)
        form_layout.addRow("سعر الصرف (ريال/دولار):", self.exchange_rate_spin)
        form_layout.addRow("ملاحظات:", self.notes_text)

        # Add form to layout
        layout.addLayout(form_layout)

        # Create section for adding items
        items_group = QWidget()
        items_layout = QVBoxLayout(items_group)
        
        # Title for items section
        items_title = QLabel(_("add_items_to_invoice", "إضافة الأصناف للفاتورة"))
        items_title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        items_layout.addWidget(items_title)

        # Create form for adding individual items
        item_form_layout = QFormLayout()

        # Category selection
        self.category_combo = QComboBox()
        self.category_combo.addItem("-- اختر الصنف --", None)
        self.load_categories()

        # Connect category selection to update units
        self.category_combo.currentIndexChanged.connect(self.on_category_changed)

        # Unit selection
        self.unit_combo = QComboBox()
        self.unit_combo.addItem("-- اختر الوحدة --", None)
        self.load_units()

        # Carat weight input
        self.carat_weight_spin = QDoubleSpinBox()
        self.carat_weight_spin.setRange(0.01, 1000.0)
        self.carat_weight_spin.setDecimals(3)
        self.carat_weight_spin.setSingleStep(0.01)

        # Price per carat input
        self.price_per_carat_spin = QDoubleSpinBox()
        self.price_per_carat_spin.setRange(0.01, 1000000.0)
        self.price_per_carat_spin.setDecimals(3)
        self.price_per_carat_spin.setSingleStep(10.0)
        self.price_per_carat_spin.setPrefix("$ ")

        # Calculated fields for current item
        self.item_total_usd_label = QLabel("0.00 $")
        self.item_total_sar_label = QLabel(_("zero_sar", "0.00 ريال"))

        # Connect signals to update item total prices
        self.carat_weight_spin.valueChanged.connect(self.calculate_item_totals)
        self.price_per_carat_spin.valueChanged.connect(self.calculate_item_totals)

        # Add item fields to form layout
        item_form_layout.addRow("الصنف:", self.category_combo)
        item_form_layout.addRow("الوحدة:", self.unit_combo)
        item_form_layout.addRow("الوزن بالقيراط:", self.carat_weight_spin)
        item_form_layout.addRow("السعر لكل قيراط:", self.price_per_carat_spin)
        item_form_layout.addRow("إجمالي الصنف (دولار):", self.item_total_usd_label)
        item_form_layout.addRow("إجمالي الصنف (ريال):", self.item_total_sar_label)

        items_layout.addLayout(item_form_layout)

        # Button to add item to invoice
        add_item_button = QPushButton("إضافة الصنف للفاتورة")
        add_item_button.clicked.connect(self.add_item_to_invoice)
        style_button(add_item_button, "add", min_width=200, min_height=35)
        items_layout.addWidget(add_item_button)

        layout.addWidget(items_group)

        # Create table for invoice items
        self.invoice_items_table = QTableWidget()
        self.invoice_items_table.setColumnCount(7)
        self.invoice_items_table.setHorizontalHeaderLabels([
            "الصنف", "الوحدة", "الوزن (قيراط)", "السعر/قيراط ($)", 
            "الإجمالي ($)", "الإجمالي (ريال)", "حذف"
        ])
        
        # Set table properties
        header = self.invoice_items_table.horizontalHeader()
        for i in range(7):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)
        
        self.invoice_items_table.setMaximumHeight(200)
        layout.addWidget(self.invoice_items_table)

        # Initialize invoice items list
        self.invoice_items = []

        # Create totals section
        totals_layout = QHBoxLayout()
        
        # Invoice totals labels
        self.invoice_total_usd_label = QLabel(_("invoice_total_usd", "إجمالي الفاتورة: 0.00 $"))
        self.invoice_total_sar_label = QLabel(_("invoice_total_sar", "إجمالي الفاتورة: 0.00 ريال"))
        
        # Style the totals labels
        font = QFont("Arial", 12, QFont.Weight.Bold)
        self.invoice_total_usd_label.setFont(font)
        self.invoice_total_sar_label.setFont(font)
        
        totals_layout.addWidget(self.invoice_total_usd_label)
        totals_layout.addWidget(self.invoice_total_sar_label)
        
        layout.addLayout(totals_layout)

        # Initial payment
        payment_layout = QFormLayout()
        self.initial_payment_spin = QDoubleSpinBox()
        self.initial_payment_spin.setRange(0.0, 1000000.0)
        self.initial_payment_spin.setDecimals(3)
        self.initial_payment_spin.setSingleStep(100.0)
        self.initial_payment_spin.setPrefix("$ ")
        payment_layout.addRow("المبلغ المدفوع مقدماً (دولار):", self.initial_payment_spin)
        layout.addLayout(payment_layout)

        # Create button to save sale
        save_button = QPushButton("حفظ فاتورة البيع")
        save_button.clicked.connect(self.save_invoice)
        style_button(save_button, "add", min_width=200, min_height=45)
        layout.addWidget(save_button)

    def load_customers(self):
        """تحميل بيانات العملاء في القائمة المنسدلة"""
        try:
            with session_scope() as session:
                # Get all customers
                customers = session.query(Customer).all()

                # Add customers to combo box
                self.customer_combo.clear()
                for customer in customers:
                    self.customer_combo.addItem(customer.name, customer.id)

                log_info(f"تم تحميل {len(customers)} عميل في القائمة المنسدلة")
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات العملاء: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('data_load_error')}: {str(e)}")

    def load_categories(self):
        """تحميل بيانات الأصناف في القائمة المنسدلة"""
        try:
            with session_scope() as session:
                # Get all categories
                categories = session.query(Category).all()

                # Add categories to combo box
                self.category_combo.clear()
                self.category_combo.addItem("-- اختر الصنف --", None)
                for category in categories:
                    self.category_combo.addItem(category.name, category.id)

                log_info(f"تم تحميل {len(categories)} صنف في القائمة المنسدلة")
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الأصناف: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('categories_load_error')}: {str(e)}")

    def load_units(self, category_id=None):
        """تحميل بيانات الوحدات في القائمة المنسدلة"""
        try:
            with session_scope() as session:
                # Get units filtered by category if specified
                if category_id:
                    units = session.query(Unit).filter_by(category_id=category_id).all()
                else:
                    units = session.query(Unit).all()

                # Add units to combo box
                self.unit_combo.clear()
                self.unit_combo.addItem("-- اختر الوحدة --", None)
                for unit in units:
                    display_text = f"{unit.name}"
                    if unit.symbol:
                        display_text += f" ({unit.symbol})"
                    self.unit_combo.addItem(display_text, unit.id)

                log_info(f"تم تحميل {len(units)} وحدة في القائمة المنسدلة")

                # إرجاع عدد الوحدات التي تم تحميلها
                return len(units)
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الوحدات: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('units_load_error')}: {str(e)}")
            return 0

    def on_category_changed(self, index):
        """تحديث الوحدات عند تغيير الصنف"""
        category_id = self.category_combo.currentData()

        # تحميل الوحدات المرتبطة بالصنف
        units_count = self.load_units(category_id)

        # اختيار الوحدة تلقائياً إذا كان هناك وحدة واحدة فقط مرتبطة بالصنف
        if category_id and units_count == 1:
            self.unit_combo.setCurrentIndex(1)  # اختيار الوحدة الوحيدة

    def calculate_item_totals(self):
        """حساب إجماليات الصنف الحالي"""
        # Get input values
        carat_weight = self.carat_weight_spin.value()
        price_per_carat = self.price_per_carat_spin.value()
        exchange_rate = self.exchange_rate_spin.value()

        # Calculate totals
        total_usd = carat_weight * price_per_carat
        total_sar = total_usd * exchange_rate

        # Update labels
        self.item_total_usd_label.setText(f"{total_usd:.3f} $")
        self.item_total_sar_label.setText(f"{total_sar:.3f} ريال")

    def calculate_invoice_totals(self):
        """حساب إجماليات الفاتورة الكاملة"""
        total_usd = 0.0
        exchange_rate = self.exchange_rate_spin.value()
        
        # Sum all items in the invoice
        for item in self.invoice_items:
            total_usd += item['total_usd']
        
        total_sar = total_usd * exchange_rate
        
        # Update invoice totals labels
        self.invoice_total_usd_label.setText(f"إجمالي الفاتورة: {total_usd:.3f} $")
        self.invoice_total_sar_label.setText(f"إجمالي الفاتورة: {total_sar:.3f} ريال")
        
        # Update initial payment maximum
        self.initial_payment_spin.setMaximum(total_usd)
        
        return total_usd, total_sar

    def add_item_to_invoice(self):
        """إضافة صنف جديد للفاتورة"""
        try:
            # Get form data
            category_id = self.category_combo.currentData()
            unit_id = self.unit_combo.currentData()
            category_name = self.category_combo.currentText()
            unit_name = self.unit_combo.currentText()
            carat_weight = self.carat_weight_spin.value()
            price_per_carat = self.price_per_carat_spin.value()
            exchange_rate = self.exchange_rate_spin.value()

            # Validate inputs
            if category_id is None:
                QMessageBox.warning(self, _('warning'), _('please_select_category'))
                return

            if carat_weight <= 0:
                QMessageBox.warning(self, _('warning'), _('please_enter_valid_weight'))
                return

            if price_per_carat <= 0:
                QMessageBox.warning(self, _('warning'), _('please_enter_valid_price'))
                return

            # Calculate totals for this item
            total_usd = carat_weight * price_per_carat
            total_sar = total_usd * exchange_rate

            # Create item dictionary
            item = {
                'category_id': category_id,
                'unit_id': unit_id,
                'category_name': category_name,
                'unit_name': unit_name,
                'carat_weight': carat_weight,
                'price_per_carat': price_per_carat,
                'total_usd': total_usd,
                'total_sar': total_sar
            }

            # Add item to invoice items list
            self.invoice_items.append(item)

            # Mark as modified
            self.mark_as_modified()

            # Update the table
            self.update_invoice_items_table()

            # Clear the form for next item
            self.category_combo.setCurrentIndex(0)
            self.unit_combo.setCurrentIndex(0)
            self.carat_weight_spin.setValue(0)
            self.price_per_carat_spin.setValue(0)
            self.item_total_usd_label.setText("0.00 $")
            self.item_total_sar_label.setText("0.00 ريال")

            # Recalculate invoice totals
            self.calculate_invoice_totals()

            log_info(f"تم إضافة صنف جديد للفاتورة: {category_name}, {carat_weight} قيراط")

        except Exception as e:
            log_error(f"خطأ في إضافة الصنف للفاتورة: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('item_add_error')}: {str(e)}")

    def update_invoice_items_table(self):
        """تحديث جدول أصناف الفاتورة"""
        self.invoice_items_table.setRowCount(len(self.invoice_items))
        
        for i, item in enumerate(self.invoice_items):
            self.invoice_items_table.setItem(i, 0, QTableWidgetItem(item['category_name']))
            self.invoice_items_table.setItem(i, 1, QTableWidgetItem(item['unit_name']))
            self.invoice_items_table.setItem(i, 2, QTableWidgetItem(f"{item['carat_weight']:.3f}"))
            self.invoice_items_table.setItem(i, 3, QTableWidgetItem(f"{item['price_per_carat']:.3f}"))
            self.invoice_items_table.setItem(i, 4, QTableWidgetItem(f"{item['total_usd']:.3f}"))
            self.invoice_items_table.setItem(i, 5, QTableWidgetItem(f"{item['total_sar']:.3f}"))
            
            # Add delete button
            delete_button = QPushButton("حذف")
            delete_button.clicked.connect(lambda checked, row=i: self.remove_item_from_invoice(row))
            style_button(delete_button, "delete", min_width=60, min_height=25)
            self.invoice_items_table.setCellWidget(i, 6, delete_button)

    def remove_item_from_invoice(self, row):
        """حذف صنف من الفاتورة"""
        try:
            if 0 <= row < len(self.invoice_items):
                removed_item = self.invoice_items.pop(row)
                # Mark as modified
                self.mark_as_modified()
                self.update_invoice_items_table()
                self.calculate_invoice_totals()
                log_info(f"تم حذف الصنف: {removed_item['category_name']} من الفاتورة")
        except Exception as e:
            log_error(f"خطأ في حذف الصنف من الفاتورة: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('item_remove_error')}: {str(e)}")

    def save_invoice(self):
        """حفظ فاتورة المبيعات مع عدة أصناف"""
        try:
            # Validate that we have items in the invoice
            if not self.invoice_items:
                QMessageBox.warning(self, _('warning'), _('please_add_at_least_one_item'))
                return

            # Get form data
            customer_id = self.customer_combo.currentData()
            sale_date = self.sale_date.date().toPyDate()
            initial_payment = self.initial_payment_spin.value()
            exchange_rate = self.exchange_rate_spin.value()
            notes = self.notes_text.toPlainText().strip()

            # Validate inputs
            if customer_id is None:
                QMessageBox.warning(self, _('warning'), _('please_select_customer'))
                return

            # Calculate invoice totals
            total_usd, total_sar = self.calculate_invoice_totals()
            amount_due = total_usd - initial_payment

            if initial_payment > total_usd:
                QMessageBox.warning(self, _('warning'), _('initial_payment_exceeds_total'))
                return

            # استخدام مدير السياق للتعامل مع جلسة قاعدة البيانات
            with session_scope() as session:
                sale_ids = []
                
                # Insert each item as a separate sale record
                for item in self.invoice_items:
                    # Calculate proportional amounts for this item
                    item_payment = item['total_usd'] * (initial_payment / total_usd) if total_usd > 0 else 0
                    item_due = item['total_usd'] - item_payment
                    
                    sale = Sale(
                        customer_id=customer_id,
                        category_id=item['category_id'],
                        unit_id=item['unit_id'],
                        diamond_type=item['category_name'],
                        carat_weight=item['carat_weight'],
                        price_per_carat_usd=item['price_per_carat'],
                        total_price_usd=item['total_usd'],
                        total_price_sar=item['total_sar'],
                        sale_date=sale_date,
                        exchange_rate=exchange_rate,
                        amount_due=item_due,
                        amount_paid=item_payment,
                        notes=f"فاتورة متعددة الأصناف - {notes}" if notes else "فاتورة متعددة الأصناف"
                    )

                    # Add sale to database
                    session.add(sale)
                    session.flush()  # To get the sale ID
                    sale_ids.append(sale.id)

                # Create journal entries for the total invoice
                if total_usd > 0:
                    # Get account IDs
                    sales_account = session.query(ChartOfAccounts).filter_by(name="Sales").first()
                    customers_account = session.query(ChartOfAccounts).filter_by(name="Customers").first()
                    cash_account = session.query(ChartOfAccounts).filter_by(name="Cash").first()

                    if sales_account and customers_account and cash_account:
                        # If payment is full, debit cash, otherwise split between cash and customer
                        if initial_payment >= total_usd:
                            # Full cash payment
                            cash_entry = JournalEntry(
                                account_id=cash_account.id,
                                debit=total_usd,
                                credit=0,
                                date=sale_date,
                                description=f"مبيعات ألماس - فاتورة أرقام {', '.join(map(str, sale_ids))}",
                                sale_id=sale_ids[0]
                            )
                            session.add(cash_entry)
                        else:
                            # Partial payment - split between cash and receivable
                            if initial_payment > 0:
                                cash_entry = JournalEntry(
                                    account_id=cash_account.id,
                                    debit=initial_payment,
                                    credit=0,
                                    date=sale_date,
                                    description=f"مبيعات ألماس - دفعة أولى - فاتورة أرقام {', '.join(map(str, sale_ids))}",
                                    sale_id=sale_ids[0]
                                )
                                session.add(cash_entry)

                            # Record remaining in accounts receivable
                            customer_entry = JournalEntry(
                                account_id=customers_account.id,
                                debit=total_usd - initial_payment,
                                credit=0,
                                date=sale_date,
                                description=f"مبيعات ألماس - ذمم مدينة - فاتورة أرقام {', '.join(map(str, sale_ids))}",
                                sale_id=sale_ids[0]
                            )
                            session.add(customer_entry)

                        # Credit Sales account
                        sales_entry = JournalEntry(
                            account_id=sales_account.id,
                            debit=0,
                            credit=total_usd,
                            date=sale_date,
                            description=f"مبيعات ألماس - فاتورة أرقام {', '.join(map(str, sale_ids))}",
                            sale_id=sale_ids[0]
                        )
                        session.add(sales_entry)

                # تسجيل نجاح العملية
                log_info(f"تم إضافة فاتورة جديدة: عدد الأصناف {len(self.invoice_items)}, ${total_usd:.2f}, للعميل ID: {customer_id}")

                # Show success message
                QMessageBox.information(self, _('success'), f"{_('invoice_saved_successfully')}\n{_('items_count')}: {len(self.invoice_items)}\n{_('total_amount')}: {total_usd:.3f} {_('dollar')}")

                # Mark as saved (no unsaved changes)
                self.mark_as_saved()

                # Reset form
                self.reset_new_sale_form()

                # Reload sales data with a slight delay to ensure commit is complete
                QTimer.singleShot(100, self.load_sales_data)

                # Switch to sales tab
                self.tab_widget.setCurrentIndex(0)

        except Exception as e:
            log_error(f"خطأ في حفظ الفاتورة: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('invoice_save_error')}: {str(e)}")

    def reset_new_sale_form(self):
        """إعادة تعيين نموذج البيع الجديد"""
        # Clear invoice items
        self.invoice_items = []
        self.update_invoice_items_table()
        
        # Reset form fields
        self.customer_combo.setCurrentIndex(0)
        self.category_combo.setCurrentIndex(0)
        self.unit_combo.setCurrentIndex(0)
        self.carat_weight_spin.setValue(0)
        self.price_per_carat_spin.setValue(0)
        self.initial_payment_spin.setValue(0)
        self.sale_date.setDate(QDate.currentDate())
        self.notes_text.clear()
        
        # Reset totals labels
        self.item_total_usd_label.setText("0.00 $")
        self.item_total_sar_label.setText("0.00 ريال")
        self.invoice_total_usd_label.setText("إجمالي الفاتورة: 0.00 $")
        self.invoice_total_sar_label.setText("إجمالي الفاتورة: 0.00 ريال")

    def load_sales_data(self):
        """تحميل بيانات المبيعات في الجدول"""
        try:
            with session_scope() as session:
                # Get all sales with customer information
                sales = session.query(Sale, Customer.name).join(Customer).order_by(desc(Sale.sale_date)).all()

                # Update table
                self.sales_table.setRowCount(len(sales))

                for i, (sale, customer_name) in enumerate(sales):
                    self.sales_table.setItem(i, 0, QTableWidgetItem(str(sale.id)))
                    self.sales_table.setItem(i, 1, QTableWidgetItem(customer_name))
                    self.sales_table.setItem(i, 2, QTableWidgetItem(sale.diamond_type))
                    self.sales_table.setItem(i, 3, QTableWidgetItem(f"{sale.carat_weight:.3f}"))
                    self.sales_table.setItem(i, 4, QTableWidgetItem(f"{sale.price_per_carat_usd:.3f}"))
                    self.sales_table.setItem(i, 5, QTableWidgetItem(f"{sale.total_price_usd:.3f}"))
                    self.sales_table.setItem(i, 6, QTableWidgetItem(f"{sale.total_price_sar:.3f}"))

                    # Format date
                    date_str = sale.sale_date.strftime("%d/%m/%Y") if sale.sale_date else ""
                    self.sales_table.setItem(i, 7, QTableWidgetItem(date_str))

                    self.sales_table.setItem(i, 8, QTableWidgetItem(f"{sale.amount_paid:.3f}"))
                    self.sales_table.setItem(i, 9, QTableWidgetItem(f"{sale.amount_due:.3f}"))

                    # Add notes (truncated if too long)
                    notes_text = sale.notes if sale.notes else ""
                    if len(notes_text) > 30:
                        notes_text = notes_text[:27] + "..."
                    self.sales_table.setItem(i, 10, QTableWidgetItem(notes_text))

                log_info(f"تم تحميل {len(sales)} عملية بيع في الجدول")
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المبيعات: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('sales_data_load_error')}: {str(e)}")

    def search_sales(self):
        """البحث عن عمليات البيع حسب المعايير المحددة"""
        # Get search criteria
        customer_name = self.search_customer.text().strip()
        date_from = self.search_date_from.date().toPyDate()
        date_to = self.search_date_to.date().toPyDate()
        diamond_type = self.search_diamond_type.currentText()

        try:
            with session_scope() as session:
                # Build query
                query = session.query(Sale, Customer.name).join(Customer)

                # Apply filters
                if customer_name:
                    query = query.filter(Customer.name.like(f'%{customer_name}%'))

                # Convert date_from to datetime with time at 00:00:00
                if date_from:
                    from datetime import datetime, time
                    date_from_dt = datetime.combine(date_from, datetime.min.time())
                    query = query.filter(Sale.sale_date >= date_from_dt)

                # Convert date_to to datetime with time at 23:59:59
                if date_to:
                    date_to_dt = datetime.combine(date_to, time(23, 59, 59))
                    query = query.filter(Sale.sale_date <= date_to_dt)

                if diamond_type != "جميع الأنواع":
                    # استخدام like للمقارنة المرنة
                    query = query.filter(Sale.diamond_type.like(f"%{diamond_type}%"))

                # Order by date
                query = query.order_by(desc(Sale.sale_date))

                # Execute query
                sales = query.all()

                # Log search results
                log_info(f"تم العثور على {len(sales)} عملية بيع تطابق معايير البحث")

                # Update table
                self.sales_table.setRowCount(len(sales))

                # Fill table with results
                for i, (sale, customer_name) in enumerate(sales):
                    self.sales_table.setItem(i, 0, QTableWidgetItem(str(sale.id)))
                    self.sales_table.setItem(i, 1, QTableWidgetItem(customer_name))
                    self.sales_table.setItem(i, 2, QTableWidgetItem(sale.diamond_type))
                    self.sales_table.setItem(i, 3, QTableWidgetItem(f"{sale.carat_weight:.3f}"))
                    self.sales_table.setItem(i, 4, QTableWidgetItem(f"{sale.price_per_carat_usd:.3f}"))
                    self.sales_table.setItem(i, 5, QTableWidgetItem(f"{sale.total_price_usd:.3f}"))
                    self.sales_table.setItem(i, 6, QTableWidgetItem(f"{sale.total_price_sar:.3f}"))

                    # Format date
                    date_str = sale.sale_date.strftime("%d/%m/%Y") if sale.sale_date else ""
                    self.sales_table.setItem(i, 7, QTableWidgetItem(date_str))

                    self.sales_table.setItem(i, 8, QTableWidgetItem(f"{sale.amount_paid:.3f}"))
                    self.sales_table.setItem(i, 9, QTableWidgetItem(f"{sale.amount_due:.3f}"))

                    # Add notes (truncated if too long)
                    notes_text = sale.notes if sale.notes else ""
                    if len(notes_text) > 30:
                        notes_text = notes_text[:27] + "..."
                    self.sales_table.setItem(i, 10, QTableWidgetItem(notes_text))

                # Show message if no results found
                if len(sales) == 0:
                    QMessageBox.information(self, _('search_results'), _('no_sales_found'))
        except Exception as e:
            log_error(f"خطأ في البحث عن المبيعات: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('sales_search_error')}: {str(e)}")

    def view_sale_details(self):
        """عرض تفاصيل عملية البيع المحددة"""
        # Get selected row
        selected_row = self.sales_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, _('warning'), _('please_select_sale_to_view'))
            return

        # Get sale ID
        sale_id = int(self.sales_table.item(selected_row, 0).text())

        # Show details in a dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("تفاصيل عملية البيع")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        try:
            with session_scope() as session:
                # Get sale with customer information
                sale_info = session.query(Sale, Customer).join(Customer).filter(Sale.id == sale_id).first()

                if not sale_info:
                    QMessageBox.warning(self, _('warning'), _('sale_not_found'))
                    return

                sale, customer = sale_info

                # Create labels with sale information
                info_text = f"""
                <h3>تفاصيل عملية البيع رقم {sale.id}</h3>
                <p><b>العميل:</b> {customer.name}</p>
                <p><b>رقم هوية العميل:</b> {customer.id_number}</p>
                <p><b>هاتف العميل:</b> {customer.phone}</p>
                <p><b>نوع الألماس:</b> {sale.diamond_type}</p>
                <p><b>الوزن بالقيراط:</b> {sale.carat_weight:.3f}</p>
                <p><b>السعر لكل قيراط:</b> {sale.price_per_carat_usd:.3f} $</p>
                <p><b>الإجمالي بالدولار:</b> {sale.total_price_usd:.3f} $</p>
                <p><b>سعر الصرف:</b> {sale.exchange_rate:.3f} ريال/دولار</p>
                <p><b>الإجمالي بالريال:</b> {sale.total_price_sar:.3f} ريال</p>
                <p><b>تاريخ البيع:</b> {sale.sale_date.strftime("%d/%m/%Y") if sale.sale_date else ""}</p>
                <p><b>المبلغ المدفوع:</b> {sale.amount_paid:.3f} $</p>
                <p><b>المبلغ المتبقي:</b> {sale.amount_due:.3f} $</p>
                <p><b>ملاحظات:</b> {sale.notes if sale.notes else "لا توجد ملاحظات"}</p>
                """

                info_label = QLabel(info_text)
                info_label.setTextFormat(Qt.TextFormat.RichText)
                layout.addWidget(info_label)                # Add a close button
                buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
                buttons.rejected.connect(dialog.reject)
                style_dialog_buttons(buttons)
                layout.addWidget(buttons)

                # تسجيل العملية
                log_info(f"تم عرض تفاصيل عملية البيع رقم {sale_id}")

                # Show the dialog
                dialog.exec()

        except Exception as e:
            log_error(f"خطأ في عرض تفاصيل عملية البيع: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('details_view_error')}: {str(e)}")

    def edit_sale(self):
        """تعديل عملية البيع المحددة"""
        # Get selected row
        selected_row = self.sales_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, _('warning'), _('please_select_sale_to_edit'))
            return

        # Get sale ID
        sale_id = int(self.sales_table.item(selected_row, 0).text())

        try:
            with session_scope() as session:
                # Get sale with customer information
                sale_info = session.query(Sale, Customer).join(Customer).filter(Sale.id == sale_id).first()

                if not sale_info:
                    QMessageBox.warning(self, _('warning'), _('sale_not_found'))
                    return

                sale, customer = sale_info

                # Create edit dialog
                dialog = QDialog(self)
                dialog.setWindowTitle(f"تعديل عملية البيع رقم {sale.id}")
                dialog.setMinimumWidth(500)

                layout = QVBoxLayout(dialog)

                # Create form layout for sale details
                form_layout = QFormLayout()

                # Customer selection (read-only)
                customer_label = QLabel(customer.name)
                form_layout.addRow("العميل:", customer_label)

                # Category selection
                category_combo = QComboBox()
                with session_scope() as category_session:
                    categories = category_session.query(Category).all()
                    for category in categories:
                        category_combo.addItem(category.name, category.id)

                # Set current category based on diamond_type
                index = category_combo.findText(sale.diamond_type)
                if index >= 0:
                    category_combo.setCurrentIndex(index)
                form_layout.addRow("الصنف:", category_combo)

                # Unit selection
                unit_combo = QComboBox()
                # We'll populate this when category changes
                form_layout.addRow("الوحدة:", unit_combo)

                # Load units for the current category
                def load_edit_units(category_id=None):
                    unit_combo.clear()
                    with session_scope() as unit_session:
                        if category_id:
                            units = unit_session.query(Unit).filter_by(category_id=category_id).all()
                        else:
                            units = unit_session.query(Unit).all()

                        for unit in units:
                            display_text = f"{unit.name}"
                            if unit.symbol:
                                display_text += f" ({unit.symbol})"
                            unit_combo.addItem(display_text, unit.id)

                        # Select the current unit if possible
                        if sale.unit_id:
                            for i in range(unit_combo.count()):
                                if unit_combo.itemData(i) == sale.unit_id:
                                    unit_combo.setCurrentIndex(i)
                                    break

                # Connect category change to unit update
                def on_edit_category_changed(index):
                    category_id = category_combo.itemData(index)
                    load_edit_units(category_id)

                category_combo.currentIndexChanged.connect(on_edit_category_changed)

                # Initial load of units
                category_id = category_combo.currentData()
                load_edit_units(category_id)

                # Carat weight input
                carat_weight_spin = QDoubleSpinBox()
                carat_weight_spin.setRange(0.01, 1000.0)
                carat_weight_spin.setDecimals(3)
                carat_weight_spin.setSingleStep(0.01)
                carat_weight_spin.setValue(sale.carat_weight)
                form_layout.addRow("الوزن بالقيراط:", carat_weight_spin)

                # Price per carat input
                price_per_carat_spin = QDoubleSpinBox()
                price_per_carat_spin.setRange(0.01, 1000000.0)
                price_per_carat_spin.setDecimals(3)
                price_per_carat_spin.setSingleStep(10.0)
                price_per_carat_spin.setPrefix("$ ")
                price_per_carat_spin.setValue(sale.price_per_carat_usd)
                form_layout.addRow("السعر لكل قيراط:", price_per_carat_spin)

                # Exchange rate input
                exchange_rate_spin = QDoubleSpinBox()
                exchange_rate_spin.setRange(0.01, 10.0)
                exchange_rate_spin.setDecimals(3)
                exchange_rate_spin.setSingleStep(0.01)
                exchange_rate_spin.setValue(sale.exchange_rate)
                form_layout.addRow("سعر الصرف (ريال/دولار):", exchange_rate_spin)

                # Calculated fields
                total_usd_label = QLabel(f"{sale.total_price_usd:.3f} $")
                total_sar_label = QLabel(f"{sale.total_price_sar:.3f} ريال")
                form_layout.addRow("الإجمالي بالدولار:", total_usd_label)
                form_layout.addRow("الإجمالي بالريال:", total_sar_label)

                # Date field
                sale_date = QDateEdit()
                sale_date.setDisplayFormat("dd/MM/yyyy")
                sale_date.setCalendarPopup(True)
                sale_date.setDate(QDate.fromString(sale.sale_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
                form_layout.addRow("تاريخ البيع:", sale_date)

                # Payment information
                amount_paid_spin = QDoubleSpinBox()
                amount_paid_spin.setRange(0.0, 1000000.0)
                amount_paid_spin.setDecimals(3)
                amount_paid_spin.setSingleStep(100.0)
                amount_paid_spin.setPrefix("$ ")
                amount_paid_spin.setValue(sale.amount_paid)
                form_layout.addRow("المبلغ المدفوع:", amount_paid_spin)

                # Notes field
                notes_text = QTextEdit()
                notes_text.setPlaceholderText("أدخل ملاحظات إضافية حول عملية البيع (اختياري)")
                notes_text.setMaximumHeight(100)
                if sale.notes:
                    notes_text.setText(sale.notes)
                form_layout.addRow("ملاحظات:", notes_text)

                # Function to update totals
                def update_totals():
                    carat_weight = carat_weight_spin.value()
                    price_per_carat = price_per_carat_spin.value()
                    exchange_rate = exchange_rate_spin.value()

                    total_usd = carat_weight * price_per_carat
                    total_sar = total_usd * exchange_rate

                    total_usd_label.setText(f"{total_usd:.3f} $")
                    total_sar_label.setText(f"{total_sar:.3f} ريال")

                    # Update maximum payment amount
                    amount_paid_spin.setMaximum(total_usd)

                # Connect signals to update total prices
                carat_weight_spin.valueChanged.connect(update_totals)
                price_per_carat_spin.valueChanged.connect(update_totals)
                exchange_rate_spin.valueChanged.connect(update_totals)

                # Add form layout to main layout
                layout.addLayout(form_layout)                # Add buttons
                buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
                buttons.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
                buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
                buttons.accepted.connect(dialog.accept)
                buttons.rejected.connect(dialog.reject)
                style_dialog_buttons(buttons)
                layout.addWidget(buttons)

                # Show dialog
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    # Get form data
                    category_id = category_combo.currentData()
                    unit_id = unit_combo.currentData()
                    diamond_type = category_combo.currentText()  # Use category name as diamond type
                    carat_weight = carat_weight_spin.value()
                    price_per_carat = price_per_carat_spin.value()
                    exchange_rate = exchange_rate_spin.value()
                    sale_date_value = sale_date.date().toPyDate()
                    amount_paid = amount_paid_spin.value()
                    notes = notes_text.toPlainText().strip()

                    # Calculate totals
                    total_usd = carat_weight * price_per_carat
                    total_sar = total_usd * exchange_rate
                    amount_due = total_usd - amount_paid

                    # Update sale object
                    sale.category_id = category_id
                    sale.unit_id = unit_id
                    sale.diamond_type = diamond_type
                    sale.carat_weight = carat_weight
                    sale.price_per_carat_usd = price_per_carat
                    sale.total_price_usd = total_usd
                    sale.total_price_sar = total_sar
                    sale.sale_date = sale_date_value
                    sale.exchange_rate = exchange_rate
                    sale.amount_paid = amount_paid
                    sale.amount_due = amount_due
                    sale.notes = notes if notes else None

                    # Commit changes
                    session.commit()

                    # تسجيل العملية
                    log_info(f"تم تعديل عملية البيع رقم {sale_id}")

                    # Show success message
                    QMessageBox.information(self, _('success'), _('sale_updated_successfully'))

                    # Reload sales data
                    self.load_sales_data()

        except Exception as e:
            log_error(f"خطأ في تعديل عملية البيع: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('sale_update_error')}: {str(e)}")

    def delete_sale(self):
        """حذف عملية البيع المحددة"""
        # Get selected row
        selected_row = self.sales_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, _('warning'), _('please_select_sale_to_delete'))
            return

        # Get sale ID
        sale_id = int(self.sales_table.item(selected_row, 0).text())

        # Confirm deletion
        reply = QMessageBox.question(self, _('confirmation'),
                                   f"{_('confirm_delete_sale')}\n"
                                   f"{_('journal_entries_will_be_deleted')}",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            try:
                with session_scope() as session:
                    # Delete journal entries first
                    session.query(JournalEntry).filter_by(sale_id=sale_id).delete()

                    # Delete sale
                    session.query(Sale).filter_by(id=sale_id).delete()

                    # تسجيل العملية
                    log_info(f"تم حذف عملية البيع رقم {sale_id}")

                    # Show success message
                    QMessageBox.information(self, _('success'), _('sale_deleted_successfully'))

                    # Reload sales data
                    self.load_sales_data()

            except Exception as e:
                log_error(f"خطأ في حذف عملية البيع: {str(e)}", e)
                QMessageBox.critical(self, _('error'), f"{_('sale_delete_error')}: {str(e)}")

    def create_receipt(self):
        """إنشاء سند قبض لعملية البيع المحددة"""
        # Get selected row
        selected_row = self.sales_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, _('warning'), _('please_select_sale_for_receipt'))
            return

        # Get sale ID and due amount
        sale_id = int(self.sales_table.item(selected_row, 0).text())
        amount_due = float(self.sales_table.item(selected_row, 9).text())

        if amount_due <= 0:
            QMessageBox.information(self, _('information'), _('no_amount_due'))
            return

        # Create receipt dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("إنشاء سند قبض")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        try:
            with session_scope() as session:
                # Get sale with customer information
                sale_info = session.query(Sale, Customer).join(Customer).filter(Sale.id == sale_id).first()

                if not sale_info:
                    QMessageBox.warning(self, _('warning'), _('sale_not_found'))
                    return

                sale, customer = sale_info

                # Display sale and customer info
                info_label = QLabel(f"""
                <h3>إنشاء سند قبض</h3>
                <p><b>رقم المبيعات:</b> {sale.id}</p>
                <p><b>العميل:</b> {customer.name}</p>
                <p><b>المبلغ المستحق:</b> {sale.amount_due:.3f} $</p>
                """)
                info_label.setTextFormat(Qt.TextFormat.RichText)
                layout.addWidget(info_label)

                # Create form for receipt
                form_layout = QFormLayout()

                # Amount input
                amount_spin = QDoubleSpinBox()
                amount_spin.setRange(0.01, sale.amount_due)
                amount_spin.setDecimals(3)
                amount_spin.setValue(sale.amount_due)  # Default to full amount
                amount_spin.setPrefix("$ ")

                # Exchange rate
                exchange_rate_spin = QDoubleSpinBox()
                exchange_rate_spin.setRange(0.01, 10.0)
                exchange_rate_spin.setDecimals(3)
                exchange_rate_spin.setSingleStep(0.01)
                exchange_rate_spin.setValue(sale.exchange_rate)  # Use the same rate as the sale

                # Calculate SAR amount
                amount_sar_label = QLabel(f"{sale.amount_due * sale.exchange_rate:.3f} ريال")

                def update_sar_amount():
                    amount_sar = amount_spin.value() * exchange_rate_spin.value()
                    amount_sar_label.setText(f"{amount_sar:.3f} ريال")

                amount_spin.valueChanged.connect(update_sar_amount)
                exchange_rate_spin.valueChanged.connect(update_sar_amount)

                # Date field
                receipt_date = QDateEdit()
                receipt_date.setDisplayFormat("dd/MM/yyyy")
                receipt_date.setCalendarPopup(True)
                receipt_date.setDate(QDate.currentDate())

                # Add fields to form
                form_layout.addRow("المبلغ المستلم (دولار):", amount_spin)
                form_layout.addRow("سعر الصرف:", exchange_rate_spin)
                form_layout.addRow("المبلغ بالريال:", amount_sar_label)
                form_layout.addRow("تاريخ السند:", receipt_date)

                layout.addLayout(form_layout)                # Add buttons
                buttons = QDialogButtonBox()
                save_button = buttons.addButton("حفظ", QDialogButtonBox.ButtonRole.AcceptRole)
                cancel_button = buttons.addButton("إلغاء", QDialogButtonBox.ButtonRole.RejectRole)
                
                # Apply button styling
                save_button.setMinimumWidth(120)
                save_button.setMinimumHeight(40)
                save_button.setStyleSheet("""
                    QPushButton {
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 12pt;
                        font-weight: bold;
                        padding: 5px 15px;
                    }
                    QPushButton:hover {
                        background-color: #2ecc71;
                    }
                    QPushButton:pressed {
                        background-color: #219653;
                    }
                """)
                
                cancel_button.setMinimumWidth(120)
                cancel_button.setMinimumHeight(40)
                cancel_button.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 12pt;
                        font-weight: bold;
                        padding: 5px 15px;
                    }
                    QPushButton:hover {
                        background-color: #f5543f;
                    }
                    QPushButton:pressed {
                        background-color: #c0392b;
                    }
                """)
                
                buttons.accepted.connect(dialog.accept)
                buttons.rejected.connect(dialog.reject)
                layout.addWidget(buttons)

                # Show dialog
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    # Get form data
                    amount_usd = amount_spin.value()
                    exchange_rate = exchange_rate_spin.value()
                    amount_sar = amount_usd * exchange_rate
                    issue_date = receipt_date.date().toPyDate()

                    # Create new receipt
                    receipt = Receipt(
                        sale_id=sale.id,
                        purchase_id=None,
                        receipt_type="CashIn",
                        amount_usd=amount_usd,
                        amount_sar=amount_sar,
                        issue_date=issue_date
                    )

                    # Update sale amount due and paid
                    sale.amount_paid += amount_usd
                    sale.amount_due -= amount_usd

                    # Create journal entries
                    # Get account IDs
                    customers_account = session.query(ChartOfAccounts).filter_by(name="Customers").first()
                    cash_account = session.query(ChartOfAccounts).filter_by(name="Cash").first()

                    if customers_account and cash_account:
                        # Debit cash, credit customers
                        cash_entry = JournalEntry(
                            account_id=cash_account.id,
                            debit=amount_usd,
                            credit=0,
                            date=issue_date,
                            description=f"سند قبض رقم {receipt.id} - مبيعات رقم {sale.id}",
                            sale_id=sale.id,
                            receipt_id=receipt.id
                        )

                        customer_entry = JournalEntry(
                            account_id=customers_account.id,
                            debit=0,
                            credit=amount_usd,
                            date=issue_date,
                            description=f"سند قبض رقم {receipt.id} - مبيعات رقم {sale.id}",
                            sale_id=sale.id,
                            receipt_id=receipt.id
                        )
                        session.add(cash_entry)
                        session.add(customer_entry)
                        
                        # تحديث صندوق النقدية
                        cash_box = session.query(صندوق_النقدية).first()
                        if not cash_box:
                            cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                            session.add(cash_box)
                            session.flush()
                            
                        # تحديث رصيد الصندوق
                        new_balance = cash_box.balance + amount_usd
                        cash_box.balance = new_balance
                        cash_box.last_updated = datetime.now()
                        
                        # إضافة حركة نقدية
                        transaction = حركة_نقدية(
                            cash_box_id=cash_box.id,
                            transaction_type="deposit",
                            amount=amount_usd,
                            balance_after=new_balance,
                            transaction_date=issue_date,
                            reference=f"سند قبض رقم {receipt.id} - مبيعات رقم {sale.id}",
                            description=f"سند قبض من العميل {customer.name} - مبيعات رقم {sale.id}",
                            created_by=self.user.id if hasattr(self, 'user') else None
                        )
                        session.add(transaction)

                    # Add receipt to database
                    session.add(receipt)

                    # تسجيل العملية
                    log_info(f"تم إنشاء سند قبض بقيمة ${amount_usd:.2f} لعملية البيع رقم {sale_id}")

                    # Show success message
                    QMessageBox.information(self, _('success'), _('receipt_created_successfully'))

                    # Reload sales data
                    self.load_sales_data()

        except Exception as e:
            log_error(f"خطأ في إنشاء سند القبض: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('receipt_creation_error')}: {str(e)}")

    def print_invoice(self):
        """طباعة فاتورة للمبيعات المحددة"""
        import os
        from datetime import datetime
        import webbrowser

        # Get selected row
        selected_row = self.sales_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, _('warning'), _('please_select_invoice_to_print'))
            return

        # Get sale ID
        sale_id = int(self.sales_table.item(selected_row, 0).text())

        # Crear un nombre de archivo para la factura
        now = datetime.now().strftime('%Y%m%d_%H%M%S')
        invoice_filename = f"sales_invoice_{sale_id}_{now}.html"

        try:
            with session_scope() as session:
                # Get sale with customer information
                sale_info = session.query(Sale, Customer).join(Customer).filter(Sale.id == sale_id).first()

                if not sale_info:
                    QMessageBox.warning(self, _('warning'), _('invoice_not_found'))
                    return

                sale, customer = sale_info

                # Obtener todos los datos necesarios dentro del contexto de la sesión
                current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                sale_date = sale.sale_date.strftime("%Y-%m-%d") if sale.sale_date else ""

                # Get company info if available
                try:
                    from database import CompanyInfo
                    company_info = session.query(CompanyInfo).first()
                    company_name = company_info.name if company_info else "نظام إدارة مبيعات الألماس"
                    company_logo = company_info.logo_path if company_info and company_info.logo_path else ""
                except:
                    company_name = "نظام إدارة مبيعات الألماس"
                    company_logo = ""

                # Store all needed data from sale and customer objects
                sale_data = {
                    'id': sale.id,
                    'diamond_type': sale.diamond_type,
                    'carat_weight': sale.carat_weight,
                    'price_per_carat_usd': sale.price_per_carat_usd,
                    'total_price_usd': sale.total_price_usd,
                    'total_price_sar': sale.total_price_sar,
                    'exchange_rate': sale.exchange_rate,
                    'amount_paid': sale.amount_paid,
                    'amount_due': sale.amount_due,
                    'notes': sale.notes,
                    'sale_date': sale_date
                }

                customer_data = {
                    'name': customer.name,
                    'id_number': customer.id_number or '-',
                    'phone': customer.phone or '-',
                    'address': customer.address or '-'
                }

            # Ahora podemos usar los datos fuera del contexto de la sesión
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>فاتورة بيع رقم {sale_data['id']}</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

                    body {{
                        font-family: 'Tajawal', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background-color: #f9f9f9;
                        color: #333;
                    }}

                    .container {{
                        max-width: 1000px;
                        margin: 0 auto;
                        background-color: #fff;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                    }}

                    .header {{
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }}

                    .logo {{
                        max-width: 150px;
                        margin-bottom: 15px;
                    }}

                    .title {{
                        font-size: 28px;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 10px;
                    }}

                    .subtitle {{
                        font-size: 20px;
                        font-weight: 500;
                        color: #3498db;
                        margin: 10px 0;
                    }}

                    .invoice-details {{
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 30px;
                        flex-wrap: wrap;
                    }}

                    .customer-details, .invoice-info {{
                        width: 48%;
                        background-color: #f8f9fa;
                        padding: 15px;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                    }}

                    @media (max-width: 768px) {{
                        .customer-details, .invoice-info {{
                            width: 100%;
                            margin-bottom: 15px;
                        }}
                    }}

                    .section-title {{
                        color: #3498db;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 5px;
                        margin-bottom: 15px;
                        font-size: 18px;
                    }}

                    .detail-row {{
                        display: flex;
                        margin-bottom: 8px;
                    }}

                    .detail-label {{
                        font-weight: 700;
                        width: 40%;
                        color: #7f8c8d;
                    }}

                    .detail-value {{
                        width: 60%;
                    }}

                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 25px 0;
                        font-size: 16px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
                        border-radius: 5px;
                        overflow: hidden;
                    }}

                    th, td {{
                        padding: 12px 15px;
                        text-align: right;
                    }}

                    th {{
                        background-color: #3498db;
                        color: white;
                        font-weight: 500;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }}

                    tr:nth-child(even) {{
                        background-color: #f2f2f2;
                    }}

                    tr:hover {{
                        background-color: #e9f7fe;
                    }}

                    .totals {{
                        margin-top: 30px;
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                    }}

                    .total-row {{
                        display: flex;
                        justify-content: space-between;
                        padding: 8px 0;
                        border-bottom: 1px solid #eee;
                    }}

                    .total-row:last-child {{
                        border-bottom: none;
                        font-weight: 700;
                        font-size: 18px;
                        color: #2c3e50;
                    }}

                    .total-label {{
                        font-weight: 700;
                    }}

                    .signatures {{
                        display: flex;
                        justify-content: space-between;
                        margin-top: 50px;
                    }}

                    .signature {{
                        width: 200px;
                        text-align: center;
                        border-top: 1px solid #000;
                        padding-top: 5px;
                    }}

                    .footer {{
                        margin-top: 40px;
                        text-align: center;
                        font-size: 14px;
                        color: #95a5a6;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                    }}

                    .print-button {{
                        background-color: #3498db;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        font-size: 16px;
                        border-radius: 5px;
                        cursor: pointer;
                        margin: 20px auto;
                        display: block;
                    }}

                    .print-button:hover {{
                        background-color: #2980b9;
                    }}

                    @media print {{
                        .print-button {{
                            display: none;
                        }}
                        body {{
                            background-color: white;
                        }}
                        .container {{
                            box-shadow: none;
                            padding: 0;
                        }}
                    }}
                </style>
                <script>
                    function printInvoice() {{
                        window.print();
                    }}
                </script>
            </head>
            <body>
                <div class="container">
                    <button onclick="printInvoice()" class="print-button">طباعة الفاتورة</button>

                    <div class="header">
                        {f'<img src="{company_logo}" alt="Company Logo" class="logo">' if company_logo else ''}
                        <div class="title">{company_name}</div>
                        <div class="subtitle">فاتورة مبيعات</div>
                        <div>Sales Invoice #{sale_data['id']}</div>
                    </div>

                    <div class="invoice-details">
                        <div class="customer-details">
                            <div class="section-title">بيانات العميل</div>
                            <div class="detail-row">
                                <div class="detail-label">الاسم:</div>
                                <div class="detail-value">{customer_data['name']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">رقم الهوية:</div>
                                <div class="detail-value">{customer_data['id_number']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">رقم الهاتف:</div>
                                <div class="detail-value">{customer_data['phone']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">العنوان:</div>
                                <div class="detail-value">{customer_data['address']}</div>
                            </div>
                        </div>

                        <div class="invoice-info">
                            <div class="section-title">بيانات الفاتورة</div>
                            <div class="detail-row">
                                <div class="detail-label">رقم الفاتورة:</div>
                                <div class="detail-value">{sale_data['id']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">تاريخ الفاتورة:</div>
                                <div class="detail-value">{sale_data['sale_date']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">تاريخ الطباعة:</div>
                                <div class="detail-value">{current_date}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">سعر الصرف:</div>
                                <div class="detail-value">{sale_data['exchange_rate']:.3f} ريال/دولار</div>
                            </div>
                        </div>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>نوع الألماس</th>
                                <th>الوزن (قيراط)</th>
                                <th>سعر القيراط ($)</th>
                                <th>الإجمالي ($)</th>
                                <th>الإجمالي (ريال)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>{sale_data['diamond_type']}</td>
                                <td>{sale_data['carat_weight']:.3f}</td>
                                <td>{sale_data['price_per_carat_usd']:.3f}</td>
                                <td>{sale_data['total_price_usd']:.3f}</td>
                                <td>{sale_data['total_price_sar']:.3f}</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="totals">
                        <div class="total-row">
                            <div class="total-label">المبلغ الإجمالي ($):</div>
                            <div>{sale_data['total_price_usd']:.3f} $</div>
                        </div>
                        <div class="total-row">
                            <div class="total-label">المبلغ الإجمالي (ريال):</div>
                            <div>{sale_data['total_price_sar']:.3f} ريال</div>
                        </div>
                        <div class="total-row">
                            <div class="total-label">المبلغ المدفوع ($):</div>
                            <div>{sale_data['amount_paid']:.3f} $</div>
                        </div>
                        <div class="total-row">
                            <div class="total-label">المبلغ المتبقي ($):</div>
                            <div>{sale_data['amount_due']:.3f} $</div>
                        </div>
                    </div>

                    {f'''
                    <div style="margin-top: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);">
                        <div style="color: #3498db; border-bottom: 1px solid #eee; padding-bottom: 5px; margin-bottom: 10px; font-size: 18px; font-weight: 700;">ملاحظات</div>
                        <p style="white-space: pre-wrap;">{sale_data['notes']}</p>
                    </div>
                    ''' if sale_data['notes'] else ''}

                    <div class="signatures">
                        <div class="signature">توقيع العميل</div>
                        <div class="signature">توقيع المسؤول</div>
                    </div>

                    <div class="footer">
                        <p>شكراً للتعامل معنا</p>
                        <p>{company_name}</p>
                        <p>تم إنشاء هذه الفاتورة في {current_date}</p>
                    </div>
                </div>
            </body>
            </html>
            """

            # تسجيل العملية
            log_info(f"تم طباعة فاتورة لعملية البيع رقم {sale_id}")

            # Write HTML content to file
            with open(invoice_filename, 'w', encoding='utf-8') as html_file:
                html_file.write(html_content)

            # Open the HTML file in the default browser
            file_path = os.path.abspath(invoice_filename)
            webbrowser.open('file://' + file_path, new=2)

            # Show success message
            QMessageBox.information(self, _('success'), _('invoice_created_and_opened'))

        except Exception as e:
            log_error(f"خطأ في طباعة الفاتورة: {str(e)}", e)
            QMessageBox.critical(self, _('error'), f"{_('invoice_print_error')}: {str(e)}")

    def has_unsaved_changes(self):
        """التحقق من وجود تغييرات غير محفوظة"""
        return self.unsaved_changes

    def mark_as_modified(self):
        """تحديد أن هناك تغييرات غير محفوظة"""
        self.unsaved_changes = True

    def mark_as_saved(self):
        """تحديد أن التغييرات تم حفظها"""
        self.unsaved_changes = False