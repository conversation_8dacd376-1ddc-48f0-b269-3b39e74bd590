from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QTabWidget,
                            QPushButton, QLabel, QLineEdit, QFormLayout,
                            QHBoxLayout, QMessageBox, QSpinBox, QDoubleSpinBox,
                            QFileDialog, QComboBox, QDateEdit, QCheckBox,
                            QGroupBox, QCalendarWidget, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont, QDoubleValidator, QPixmap, QFontDatabase
from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker
import os
import shutil
import sqlite3
from datetime import datetime, timedelta
from database import CompanyInfo, Setting, Base, DataLock, Permission, RolePermission, User, Activation, import_database
from translations import get_translation as _, change_language
from permissions import get_all_permissions, get_user_permissions, has_permission
import app_state
from activation import get_hardware_id, get_activation_info, validate_activation_code
from ui_utils import style_button, style_dialog_buttons

class SettingsScreen(QWidget):
    def __init__(self):
        super().__init__()
        # الحصول على المستخدم الحالي من حالة التطبيق
        self.user = app_state.get_current_user()
        if not self.user:
            QMessageBox.critical(self, "خطأ", "لم يتم العثور على المستخدم الحالي")
            self.close()
            return
        self.init_db()
        self.init_ui()

    def init_db(self):
        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        self.session = Session()

        # استرجاع معلومات الشركة والإعدادات
        self.company_info = self.session.query(CompanyInfo).first()
        if not self.company_info:
            self.company_info = CompanyInfo(
                name="",
                address="",
                tax_number="",
                logo_path=""
            )
            self.session.add(self.company_info)
            self.session.commit()

        self.settings = self.session.query(Setting).first()
        if not self.settings:
            self.settings = Setting()
            self.session.add(self.settings)
            self.session.commit()

    def init_ui(self):
        self.setWindowTitle(_("settings_title"))
        self.setGeometry(100, 100, 800, 500)

        # Get current language from database
        try:
            setting = self.session.query(Setting).first()
            if setting:
                self.current_language = setting.language
            else:
                self.current_language = "ar"  # Arabic as default
        except Exception:
            self.current_language = "ar"  # If error, use Arabic as default

        # Set layout direction based on language
        if self.current_language == "ar":
            self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.company_info_tab = QWidget()
        self.system_settings_tab = QWidget()
        self.backup_tab = QWidget()
        self.data_lock_tab = QWidget()
        self.permissions_tab = QWidget()
        self.activation_tab = QWidget()  # Nueva pestaña para la activación

        # Setup tabs
        self.setup_company_info_tab()
        self.setup_system_settings_tab()
        self.setup_backup_tab()
        self.setup_data_lock_tab()
        self.setup_permissions_tab()
        self.setup_activation_tab()  # Configurar la nueva pestaña

        # Add tabs to tab widget
        self.tab_widget.addTab(self.company_info_tab, _("company_info"))
        self.tab_widget.addTab(self.system_settings_tab, _("system_settings"))
        self.tab_widget.addTab(self.backup_tab, _("backup"))
        self.tab_widget.addTab(self.data_lock_tab, _("data_lock", "إقفال البيانات"))
        self.tab_widget.addTab(self.activation_tab, _("activation", "تفعيل البرنامج"))

        # إضافة تبويب الصلاحيات للمسؤول فقط
        if self.user.role == "admin":
            self.tab_widget.addTab(self.permissions_tab, _("user_permissions", "صلاحيات المستخدمين"))

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_company_info_tab(self):
        layout = QVBoxLayout(self.company_info_tab)

        # العنوان
        title_label = QLabel("معلومات الشركة")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # نموذج البيانات
        form_layout = QFormLayout()

        # اسم الشركة
        self.company_name = QLineEdit(self.company_info.name if self.company_info.name else "")
        form_layout.addRow("اسم الشركة:", self.company_name)

        # عنوان الشركة
        self.company_address = QLineEdit(self.company_info.address if self.company_info.address else "")
        form_layout.addRow("العنوان:", self.company_address)

        # رقم الضريبة
        self.tax_number = QLineEdit(self.company_info.tax_number if self.company_info.tax_number else "")
        form_layout.addRow("رقم الضريبة / السجل التجاري:", self.tax_number)        # شعار الشركة
        self.logo_layout = QHBoxLayout()
        self.logo_path = QLineEdit(self.company_info.logo_path if self.company_info.logo_path else "")
        self.logo_path.setReadOnly(True)
        self.browse_button = QPushButton("استعراض...")
        self.browse_button.clicked.connect(self.browse_logo)
        style_button(self.browse_button, "default", min_width=100, min_height=35)
        self.logo_layout.addWidget(self.logo_path)
        self.logo_layout.addWidget(self.browse_button)
        form_layout.addRow("شعار الشركة:", self.logo_layout)

        # عرض الشعار
        self.logo_preview = QLabel()
        self.logo_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.logo_preview.setFixedHeight(150)
        if self.company_info.logo_path:
            pixmap = QPixmap(self.company_info.logo_path)
            if not pixmap.isNull():
                self.logo_preview.setPixmap(pixmap.scaled(200, 150, Qt.AspectRatioMode.KeepAspectRatio))

        form_layout.addRow("معاينة الشعار:", self.logo_preview)

        layout.addLayout(form_layout)        # أزرار الحفظ
        buttons_layout = QHBoxLayout()
        self.save_company_info_btn = QPushButton("حفظ المعلومات")
        self.save_company_info_btn.clicked.connect(self.save_company_info)
        style_button(self.save_company_info_btn, "success", min_width=150, min_height=40)
        buttons_layout.addWidget(self.save_company_info_btn)

        layout.addLayout(buttons_layout)

    def setup_system_settings_tab(self):
        layout = QVBoxLayout(self.system_settings_tab)

        # العنوان
        title_label = QLabel("إعدادات النظام")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # نموذج البيانات
        form_layout = QFormLayout()

        # سعر الصرف
        self.exchange_rate = QDoubleSpinBox()
        self.exchange_rate.setDecimals(3)
        self.exchange_rate.setRange(0.01, 100.0)
        self.exchange_rate.setValue(self.settings.exchange_rate)
        form_layout.addRow("سعر الصرف (دولار أمريكي / ريال سعودي):", self.exchange_rate)

        # العملة الافتراضية
        self.default_currency = QComboBox()
        self.default_currency.addItem("ريال سعودي", "SAR")
        self.default_currency.addItem("دولار أمريكي", "USD")
        idx = self.default_currency.findData(self.settings.default_currency)
        if idx >= 0:
            self.default_currency.setCurrentIndex(idx)
        form_layout.addRow("العملة الافتراضية:", self.default_currency)

        # اللغة
        self.language = QComboBox()
        self.language.addItem("العربية", "ar")
        self.language.addItem("English", "en")
        idx = self.language.findData(self.settings.language)
        if idx >= 0:
            self.language.setCurrentIndex(idx)
        form_layout.addRow("اللغة:", self.language)

        # إعدادات الخط
        font_group = QGroupBox("إعدادات الخط")
        font_layout = QFormLayout()

        # نوع الخط
        self.font_family = QComboBox()

        # إضافة خيار لتصفية الخطوط
        self.font_filter = QComboBox()
        self.font_filter.addItem("جميع الخطوط", "all")
        self.font_filter.addItem("خطوط عربية", "arabic")
        self.font_filter.addItem("خطوط إنجليزية", "latin")
        self.font_filter.currentIndexChanged.connect(self.filter_fonts)
        font_layout.addRow("تصفية الخطوط:", self.font_filter)

        # الحصول على قائمة الخطوط المتوفرة في النظام
        try:
            self.font_db = QFontDatabase()
            self.system_fonts = self.font_db.families()
        except Exception as e:
            print(f"Error getting system fonts: {e}")
            # Usar una lista de fuentes predeterminadas en caso de error
            self.system_fonts = ["Arial", "Tahoma", "Times New Roman", "Calibri", "Segoe UI", "Verdana", "Helvetica", "Courier New"]

        # تحميل الخطوط الافتراضية (جميع الخطوط)
        try:
            self.load_fonts("all")
        except Exception as e:
            print(f"Error loading fonts: {e}")
            # Manejar el error para evitar que la aplicación se cierre

        # تعيين الخط الحالي
        try:
            current_font = self.settings.font_family if hasattr(self.settings, "font_family") and self.settings.font_family else "Arial"
            idx = self.font_family.findText(current_font)
            if idx >= 0:
                self.font_family.setCurrentIndex(idx)
        except Exception as e:
            print(f"Error setting font family: {e}")
            # Usar valor predeterminado si hay error
            idx = self.font_family.findText("Arial")
            if idx >= 0:
                self.font_family.setCurrentIndex(idx)

        font_layout.addRow("نوع الخط:", self.font_family)

        # حجم الخط
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 18)

        try:
            font_size_value = self.settings.font_size if hasattr(self.settings, "font_size") and self.settings.font_size else 10
            self.font_size.setValue(font_size_value)
        except Exception as e:
            print(f"Error setting font size: {e}")
            # Usar valor predeterminado si hay error
            self.font_size.setValue(10)

        font_layout.addRow("حجم الخط:", self.font_size)

        # معاينة الخط
        self.font_preview = QLabel("")
        self.font_preview.setMinimumHeight(100)  # تعيين ارتفاع أدنى للمعاينة
        self.update_font_preview()

        # تحديث معاينة الخط عند تغيير الإعدادات
        self.font_family.currentIndexChanged.connect(self.update_font_preview)
        self.font_size.valueChanged.connect(self.update_font_preview)

        font_layout.addRow("معاينة:", self.font_preview)
        font_group.setLayout(font_layout)
        form_layout.addRow("", font_group)

        layout.addLayout(form_layout)        # أزرار الحفظ
        buttons_layout = QHBoxLayout()
        self.save_settings_btn = QPushButton("حفظ الإعدادات")
        self.save_settings_btn.clicked.connect(self.save_settings)
        style_button(self.save_settings_btn, "success", min_width=150, min_height=45)
        buttons_layout.addWidget(self.save_settings_btn)

        layout.addLayout(buttons_layout)

    def setup_backup_tab(self):
        layout = QVBoxLayout(self.backup_tab)

        # العنوان
        title_label = QLabel("إعدادات النسخ الاحتياطي")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # مجموعة إعدادات النسخ الاحتياطي
        backup_settings_group = QGroupBox("إعدادات النسخ الاحتياطي")
        backup_settings_layout = QFormLayout()

        # مسار النسخ الاحتياطي
        self.backup_path_layout = QHBoxLayout()
        self.backup_path = QLineEdit(self.settings.backup_path if self.settings.backup_path else "")
        self.backup_path.setReadOnly(True)
        self.backup_path_browse = QPushButton("استعراض...")
        self.backup_path_browse.clicked.connect(self.browse_backup_path)
        style_button(self.backup_path_browse, "default", min_width=100, min_height=35)
        self.backup_path_layout.addWidget(self.backup_path)
        self.backup_path_layout.addWidget(self.backup_path_browse)
        backup_settings_layout.addRow("مسار النسخ الاحتياطي:", self.backup_path_layout)

        # تفعيل النسخ الاحتياطي التلقائي
        self.auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي عند الخروج من البرنامج")
        self.auto_backup.setChecked(self.settings.auto_backup if hasattr(self.settings, "auto_backup") else True)
        backup_settings_layout.addRow("", self.auto_backup)

        # آخر نسخ احتياطي
        last_backup_text = "لم يتم عمل نسخ احتياطي بعد"
        if self.settings.last_backup_date:
            last_backup_text = self.settings.last_backup_date.strftime("%Y-%m-%d %H:%M:%S")
        self.last_backup = QLabel(last_backup_text)
        backup_settings_layout.addRow("آخر نسخ احتياطي:", self.last_backup)

        backup_settings_group.setLayout(backup_settings_layout)
        layout.addWidget(backup_settings_group)

        # أزرار العمليات
        operations_group = QGroupBox("عمليات النسخ الاحتياطي")
        operations_layout = QVBoxLayout()        # إنشاء نسخة احتياطية
        self.create_backup_btn = QPushButton("إنشاء نسخة احتياطية جديدة")
        self.create_backup_btn.clicked.connect(self.create_backup)
        style_button(self.create_backup_btn, "info", min_width=200, min_height=40)
        operations_layout.addWidget(self.create_backup_btn)

        # استعادة نسخة احتياطية
        self.restore_backup_btn = QPushButton("استعادة من نسخة احتياطية")
        self.restore_backup_btn.clicked.connect(self.restore_backup)
        style_button(self.restore_backup_btn, "warning", min_width=200, min_height=40)
        operations_layout.addWidget(self.restore_backup_btn)

        # استيراد قاعدة بيانات قديمة
        self.import_database_btn = QPushButton("استيراد قاعدة بيانات قديمة")
        self.import_database_btn.clicked.connect(self.import_old_database)
        style_button(self.import_database_btn, "default", min_width=200, min_height=40)
        operations_layout.addWidget(self.import_database_btn)

        operations_group.setLayout(operations_layout)
        layout.addWidget(operations_group)        # زر حفظ الإعدادات
        self.save_backup_settings_btn = QPushButton("حفظ إعدادات النسخ الاحتياطي")
        self.save_backup_settings_btn.clicked.connect(self.save_backup_settings)
        style_button(self.save_backup_settings_btn, "success", min_width=200, min_height=40)
        layout.addWidget(self.save_backup_settings_btn)

    def setup_data_lock_tab(self):
        layout = QVBoxLayout(self.data_lock_tab)

        # العنوان
        title_label = QLabel("إقفال البيانات")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # إنشاء قفل البيانات
        create_lock_group = QGroupBox("إنشاء قفل للبيانات")
        create_lock_layout = QFormLayout()

        # نوع القفل
        self.lock_type = QComboBox()
        self.lock_type.addItem("شهر", "month")
        self.lock_type.addItem("ربع سنة", "quarter")
        self.lock_type.addItem("نصف سنة", "half_year")
        self.lock_type.addItem("سنة", "year")
        create_lock_layout.addRow("نوع فترة القفل:", self.lock_type)

        # تاريخ البداية
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        create_lock_layout.addRow("تاريخ البداية:", self.start_date)

        # تاريخ النهاية
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        create_lock_layout.addRow("تاريخ النهاية:", self.end_date)

        # زر إنشاء القفل
        self.create_lock_btn = QPushButton("إقفال الفترة")
        self.create_lock_btn.clicked.connect(self.create_data_lock)

        style_button(self.create_lock_btn, "warning", min_width=150, min_height=45)
        create_lock_layout.addRow("", self.create_lock_btn)
        create_lock_group.setLayout(create_lock_layout)
        layout.addWidget(create_lock_group)

        # عرض الأقفال الموجودة
        locks_group = QGroupBox("الفترات المقفلة")
        locks_layout = QVBoxLayout()

        # قائمة الأقفال
        self.locks_label = QLabel("لا يوجد فترات مقفلة")
        locks_layout.addWidget(self.locks_label)

        # استرجاع الأقفال الموجودة
        self.data_locks = self.session.query(DataLock).filter_by(is_locked=True).all()

        if self.data_locks:
            locks_text = ""
            for lock in self.data_locks:
                start_date = lock.start_date.strftime("%Y-%m-%d")
                end_date = lock.end_date.strftime("%Y-%m-%d")
                locks_text += f"الفترة: {start_date} إلى {end_date} | النوع: {lock.lock_period_type}\n"

            self.locks_label.setText(locks_text)

        locks_group.setLayout(locks_layout)
        layout.addWidget(locks_group)

    def browse_logo(self):
        file_dialog = QFileDialog()
        logo_path, _ = file_dialog.getOpenFileName(self, "اختيار شعار الشركة", "", "ملفات الصور (*.png *.jpg *.jpeg)")

        if logo_path:
            self.logo_path.setText(logo_path)
            pixmap = QPixmap(logo_path)
            if not pixmap.isNull():
                self.logo_preview.setPixmap(pixmap.scaled(200, 150, Qt.AspectRatioMode.KeepAspectRatio))

    def save_company_info(self):
        try:
            self.company_info.name = self.company_name.text()
            self.company_info.address = self.company_address.text()
            self.company_info.tax_number = self.tax_number.text()
            self.company_info.logo_path = self.logo_path.text()

            self.session.commit()
            QMessageBox.information(self, "تم الحفظ", "تم حفظ معلومات الشركة بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ معلومات الشركة: {str(e)}")

    def browse_backup_path(self):
        folder_dialog = QFileDialog()
        backup_path = folder_dialog.getExistingDirectory(self, "اختيار مسار النسخ الاحتياطي")

        if backup_path:
            self.backup_path.setText(backup_path)

    def save_backup_settings(self):
        try:
            self.settings.backup_path = self.backup_path.text()
            self.settings.auto_backup = self.auto_backup.isChecked()

            self.session.commit()
            QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات النسخ الاحتياطي بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ إعدادات النسخ الاحتياطي: {str(e)}")

    def create_backup(self):
        if not self.settings.backup_path:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد مسار النسخ الاحتياطي أولاً")
            return

        try:
            # إنشاء اسم الملف بالتاريخ والوقت الحاليين
            now = datetime.now()
            timestamp = now.strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(self.settings.backup_path, f"diamond_sales_backup_{timestamp}.db")

            # نسخ قاعدة البيانات إلى الملف الجديد
            shutil.copy2("diamond_sales.db", backup_file)

            # تحديث تاريخ آخر نسخ احتياطي
            self.settings.last_backup_date = now
            self.session.commit()

            # تحديث واجهة المستخدم
            self.last_backup.setText(now.strftime("%Y-%m-%d %H:%M:%S"))

            QMessageBox.information(self, "تم النسخ الاحتياطي", f"تم إنشاء النسخة الاحتياطية بنجاح في:\n{backup_file}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        if not self.settings.backup_path:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد مسار النسخ الاحتياطي أولاً")
            return

        # عرض مربع حوار للتحذير
        confirmation = QMessageBox.warning(
            self,
            "تحذير - استعادة النسخة الاحتياطية",
            "سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية. هذا الإجراء لا يمكن التراجع عنه.\n\nهل أنت متأكد من رغبتك في الاستمرار؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if confirmation != QMessageBox.StandardButton.Yes:
            return

        # اختيار ملف النسخة الاحتياطية
        file_dialog = QFileDialog()
        backup_file, _ = file_dialog.getOpenFileName(
            self,
            "اختيار ملف النسخة الاحتياطية",
            self.settings.backup_path,
            "ملفات قاعدة البيانات (*.db)"
        )

        if not backup_file:
            return

        try:
            # إغلاق الجلسة الحالية
            self.session.close()

            # نسخ النسخة الاحتياطية إلى قاعدة البيانات الحالية
            shutil.copy2(backup_file, "diamond_sales.db")

            # إعادة فتح الجلسة
            engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=engine)
            self.session = Session()

            QMessageBox.information(
                self,
                "تم الاستعادة",
                "تم استعادة قاعدة البيانات من النسخة الاحتياطية بنجاح.\n"
                "يرجى إعادة تشغيل البرنامج لتطبيق التغييرات."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الاستعادة",
                f"حدث خطأ أثناء استعادة قاعدة البيانات: {str(e)}"
            )

    def import_old_database(self):
        """استيراد قاعدة بيانات قديمة وتحديثها لتتوافق مع الهيكل الجديد"""
        # عرض مربع حوار للتحذير
        confirmation = QMessageBox.warning(
            self,
            "تحذير - استيراد قاعدة بيانات قديمة",
            "سيتم استيراد البيانات من قاعدة بيانات قديمة وتحديثها لتتوافق مع الهيكل الجديد.\n"
            "هذا الإجراء قد يستغرق بعض الوقت وقد يؤدي إلى فقدان بعض البيانات إذا كانت هناك تعارضات.\n\n"
            "هل أنت متأكد من رغبتك في الاستمرار؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if confirmation != QMessageBox.StandardButton.Yes:
            return

        # اختيار ملف قاعدة البيانات القديمة
        file_dialog = QFileDialog()
        old_db_path, _ = file_dialog.getOpenFileName(
            self,
            "اختيار ملف قاعدة البيانات القديمة",
            "",
            "ملفات قاعدة البيانات (*.db)"
        )

        if not old_db_path:
            return

        # عرض مربع حوار للتأكيد النهائي
        final_confirmation = QMessageBox.warning(
            self,
            "تأكيد نهائي - استيراد قاعدة بيانات قديمة",
            f"سيتم استيراد البيانات من:\n{old_db_path}\n\n"
            "هل أنت متأكد من رغبتك في الاستمرار؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if final_confirmation != QMessageBox.StandardButton.Yes:
            return

        # إغلاق الجلسة الحالية
        self.session.close()

        # استيراد قاعدة البيانات القديمة
        import_progress = QMessageBox(self)
        import_progress.setWindowTitle("جاري الاستيراد")
        import_progress.setText("جاري استيراد البيانات من قاعدة البيانات القديمة...\nقد يستغرق هذا بعض الوقت، يرجى الانتظار.")
        import_progress.setStandardButtons(QMessageBox.StandardButton.NoButton)
        import_progress.show()

        # استدعاء دالة الاستيراد
        success = import_database(old_db_path)

        # إغلاق مربع حوار التقدم
        import_progress.close()

        # إعادة فتح الجلسة
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        self.session = Session()

        # عرض نتيجة الاستيراد
        if success:
            QMessageBox.information(
                self,
                "تم الاستيراد",
                "تم استيراد البيانات من قاعدة البيانات القديمة بنجاح.\n"
                "يرجى إعادة تشغيل البرنامج لتطبيق التغييرات."
            )
        else:
            QMessageBox.critical(
                self,
                "خطأ في الاستيراد",
                "حدث خطأ أثناء استيراد البيانات من قاعدة البيانات القديمة.\n"
                "يرجى التحقق من سجل الأخطاء للحصول على مزيد من المعلومات."
            )

    def save_settings(self):
        try:
            # Guardar el idioma actual antes de cambiarlo
            old_language = self.settings.language

            # Intentar obtener la configuración de fuente actual
            try:
                old_font_family = self.settings.font_family if hasattr(self.settings, "font_family") else "Arial"
                old_font_size = self.settings.font_size if hasattr(self.settings, "font_size") else 10
            except Exception:
                old_font_family = "Arial"
                old_font_size = 10

            # Actualizar configuraciones básicas
            self.settings.exchange_rate = self.exchange_rate.value()
            self.settings.default_currency = self.default_currency.currentData()
            self.settings.language = self.language.currentData()

            # Intentar guardar la configuración de fuente
            try:
                # Verificar si la base de datos tiene las columnas necesarias
                if hasattr(self.settings, "font_family") and hasattr(self.settings, "font_size"):
                    self.settings.font_family = self.font_family.currentText()
                    self.settings.font_size = self.font_size.value()
                else:
                    # Si no existen las columnas, mostrar un mensaje y continuar
                    print("Las columnas de fuente no existen en la base de datos. Se requiere actualizar la estructura.")
            except Exception as font_error:
                print(f"Error al guardar la configuración de fuente: {font_error}")
                # No interrumpir el proceso por un error en la configuración de fuente

            # Guardar cambios
            self.session.commit()

            # Cargar las traducciones actualizadas
            try:
                from translations import change_language
                change_language(self.settings.language)
            except Exception as lang_error:
                print(f"Error al cambiar el idioma: {lang_error}")

            # Verificar si se cambió la fuente
            try:
                font_changed = (old_font_family != self.settings.font_family) or (old_font_size != self.settings.font_size)
            except Exception:
                font_changed = False

            # Mostrar mensaje de éxito
            if old_language != self.settings.language or font_changed:
                # Si se cambió el idioma o la fuente, mostrar mensaje especial
                QMessageBox.information(
                    self,
                    _("settings_changed"),
                    _("restart_required")
                )
            else:
                # Mensaje normal si no se cambió el idioma ni la fuente
                QMessageBox.information(self, _("success"), _("operation_success"))

        except Exception as e:
            print(f"Error al guardar la configuración: {e}")
            QMessageBox.critical(self, _("error"), f"{_('operation_error')}: {str(e)}")

    def create_data_lock(self):
        try:
            # التحقق من صحة التواريخ
            start_date = self.start_date.date().toPyDate()
            end_date = self.end_date.date().toPyDate()

            if start_date >= end_date:
                QMessageBox.warning(self, "تحذير", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return

            # التحقق من عدم وجود تداخل مع الفترات المقفلة
            existing_locks = self.session.query(DataLock).filter(
                DataLock.is_locked == True,
                ((DataLock.start_date <= end_date) & (DataLock.end_date >= start_date))
            ).all()

            if existing_locks:
                QMessageBox.warning(self, "تحذير", "توجد فترة مقفلة تتداخل مع الفترة المحددة")
                return

            # إنشاء قفل جديد
            new_lock = DataLock(
                start_date=start_date,
                end_date=end_date,
                lock_period_type=self.lock_type.currentData(),
                is_locked=True
            )

            self.session.add(new_lock)
            self.session.commit()

            # تحديث قائمة الأقفال
            self.data_locks = self.session.query(DataLock).filter_by(is_locked=True).all()

            # تحديث عرض الأقفال
            locks_text = ""
            for lock in self.data_locks:
                start = lock.start_date.strftime("%Y-%m-%d")
                end = lock.end_date.strftime("%Y-%m-%d")
                locks_text += f"الفترة: {start} إلى {end} | النوع: {lock.lock_period_type}\n"

            self.locks_label.setText(locks_text if locks_text else "لا يوجد فترات مقفلة")

            QMessageBox.information(self, "تم", "تم إقفال الفترة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إقفال الفترة: {str(e)}")

    def filter_fonts(self):
        """تصفية الخطوط بناءً على الاختيار"""
        try:
            # Obtener el tipo de filtro seleccionado
            filter_type = "all"  # Valor predeterminado
            try:
                if self.font_filter and hasattr(self.font_filter, "currentData"):
                    filter_type = self.font_filter.currentData()
            except Exception as e:
                print(f"Error getting filter type: {e}")

            # Cargar las fuentes con el filtro seleccionado
            self.load_fonts(filter_type)

            # إعادة تعيين الخط الحالي بعد التصفية إذا كان موجودًا
            try:
                # Obtener la fuente actual
                current_font = "Arial"  # Valor predeterminado
                if hasattr(self, "settings") and self.settings:
                    if hasattr(self.settings, "font_family") and self.settings.font_family:
                        current_font = self.settings.font_family

                # Buscar la fuente en la lista
                if hasattr(self, "font_family") and self.font_family:
                    idx = self.font_family.findText(current_font)
                    if idx >= 0:
                        self.font_family.setCurrentIndex(idx)
                    elif self.font_family.count() > 0:
                        # Si la fuente no está en la lista, usar la primera
                        self.font_family.setCurrentIndex(0)
                        # Actualizar la vista previa
                        if hasattr(self, "update_font_preview"):
                            self.update_font_preview()
            except Exception as e:
                print(f"Error resetting font after filtering: {e}")
        except Exception as e:
            print(f"Error in filter_fonts: {e}")

    def load_fonts(self, filter_type):
        """تحميل الخطوط بناءً على نوع التصفية"""
        try:
            self.font_family.clear()

            # تحديد الخطوط التي سيتم عرضها
            if filter_type == "all":
                # عرض جميع الخطوط
                filtered_fonts = self.system_fonts
            elif filter_type == "arabic":
                # عرض الخطوط التي تدعم العربية
                filtered_fonts = []
                try:
                    for font in self.system_fonts:
                        try:
                            # التحقق من دعم الخط للغة العربية بطريقة آمنة
                            writing_systems = self.font_db.writingSystems(font)
                            if writing_systems is not None and int(writing_systems) & int(QFontDatabase.WritingSystem.Arabic):
                                filtered_fonts.append(font)
                        except Exception as e:
                            print(f"Error checking Arabic support for font {font}: {e}")
                            # Continue with next font
                            continue
                except Exception as e:
                    print(f"Error filtering Arabic fonts: {e}")
                    # Fallback to all fonts
                    filtered_fonts = self.system_fonts
            elif filter_type == "latin":
                # عرض الخطوط التي تدعم اللاتينية
                filtered_fonts = []
                try:
                    for font in self.system_fonts:
                        try:
                            # التحقق من دعم الخط للغة اللاتينية بطريقة آمنة
                            writing_systems = self.font_db.writingSystems(font)
                            if writing_systems is not None and int(writing_systems) & int(QFontDatabase.WritingSystem.Latin):
                                filtered_fonts.append(font)
                        except Exception as e:
                            print(f"Error checking Latin support for font {font}: {e}")
                            # Continue with next font
                            continue
                except Exception as e:
                    print(f"Error filtering Latin fonts: {e}")
                    # Fallback to all fonts
                    filtered_fonts = self.system_fonts
            else:
                # Default to all fonts
                filtered_fonts = self.system_fonts

            # Si no hay fuentes filtradas, usar todas las fuentes
            if not filtered_fonts:
                filtered_fonts = self.system_fonts

            # إضافة الخطوط المصفاة إلى القائمة المنسدلة
            for font in filtered_fonts:
                self.font_family.addItem(font)

        except Exception as e:
            print(f"Error in load_fonts: {e}")
            # Fallback to a minimal set of common fonts
            self.font_family.clear()
            fallback_fonts = ["Arial", "Tahoma", "Times New Roman", "Calibri", "Segoe UI", "Verdana"]
            for font in fallback_fonts:
                self.font_family.addItem(font)

    def update_font_preview(self):
        """تحديث معاينة الخط بناءً على الإعدادات المحددة"""
        try:
            # Verificar que los controles existen antes de acceder a ellos
            if hasattr(self, 'font_family') and hasattr(self, 'font_size') and hasattr(self, 'font_preview'):
                # Obtener el texto de la fuente actual de forma segura
                try:
                    font_family = ""
                    if self.font_family.count() > 0 and self.font_family.currentIndex() >= 0:
                        font_family = self.font_family.currentText()
                    else:
                        font_family = "Arial"  # Valor predeterminado
                except Exception as e:
                    print(f"Error getting font family: {e}")
                    font_family = "Arial"  # Valor predeterminado

                # Obtener el tamaño de la fuente de forma segura
                try:
                    font_size = 10  # Valor predeterminado
                    if hasattr(self.font_size, "value"):
                        font_size = self.font_size.value()
                except Exception as e:
                    print(f"Error getting font size: {e}")

                try:
                    # إنشاء كائن الخط
                    preview_font = QFont(font_family, font_size)

                    # تطبيق الخط على عنصر المعاينة
                    self.font_preview.setFont(preview_font)

                    # عرض اسم الخط في المعاينة
                    preview_text = f"معاينة الخط: {font_family}, {font_size}pt\nأبجد هوز حطي كلمن سعفص قرشت ثخذ ضظغ\nABCDEFGHIJKLMNOPQRSTUVWXYZ\n1234567890"
                    self.font_preview.setText(preview_text)
                except Exception as e:
                    print(f"Error setting font preview: {e}")
                    # Intentar con una fuente predeterminada
                    try:
                        fallback_font = QFont("Arial", 10)
                        self.font_preview.setFont(fallback_font)
                        self.font_preview.setText("معاينة الخط (خط بديل)\nأبجد هوز حطي كلمن\nABCDEFGHIJKLMNOPQRSTUVWXYZ\n1234567890")
                    except Exception:
                        # Si todo falla, simplemente no hacer nada
                        pass
        except Exception as e:
            print(f"Error updating font preview: {e}")
            # No mostrar mensaje de error al usuario para evitar interrupciones

    def setup_activation_tab(self):
        """
        إعداد تبويب تفعيل البرنامج
        """
        layout = QVBoxLayout(self.activation_tab)

        # الحصول على معلومات التفعيل
        activation = self.session.query(Activation).first()

        # إنشاء نموذج للإعدادات
        form_layout = QFormLayout()

        # معلومات التفعيل الحالية
        activation_group = QGroupBox("معلومات التفعيل الحالية")
        activation_layout = QFormLayout()

        # معرف الجهاز
        hardware_id_label = QLabel(get_hardware_id())
        hardware_id_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        activation_layout.addRow("معرف الجهاز:", hardware_id_label)

        # حالة التفعيل
        activation_info = get_activation_info()
        if activation_info:
            status_label = QLabel("مفعل")
            status_label.setStyleSheet("color: green; font-weight: bold;")

            # تاريخ انتهاء الصلاحية
            expiry_date = activation_info['expiry_date'].strftime("%Y-%m-%d")
            expiry_label = QLabel(expiry_date)

            # الأيام المتبقية
            days_left = activation_info['days_left']
            days_left_label = QLabel(str(days_left))
            if days_left < 30:
                days_left_label.setStyleSheet("color: red; font-weight: bold;")
            else:
                days_left_label.setStyleSheet("color: green; font-weight: bold;")

            # الميزات المفعلة
            features = ", ".join(activation_info['features'])
            features_label = QLabel(features)
        else:
            status_label = QLabel("غير مفعل")
            status_label.setStyleSheet("color: red; font-weight: bold;")
            expiry_label = QLabel("-")
            days_left_label = QLabel("-")
            features_label = QLabel("-")

        activation_layout.addRow("حالة التفعيل:", status_label)
        activation_layout.addRow("تاريخ انتهاء الصلاحية:", expiry_label)
        activation_layout.addRow("الأيام المتبقية:", days_left_label)
        activation_layout.addRow("الميزات المفعلة:", features_label)

        activation_group.setLayout(activation_layout)
        form_layout.addRow("", activation_group)

        # إعدادات التفعيل
        settings_group = QGroupBox("إعدادات التفعيل")
        settings_layout = QFormLayout()

        # مدة صلاحية كود التفعيل الافتراضية
        self.default_expiry_days = QSpinBox()
        self.default_expiry_days.setRange(1, 3650)  # من يوم إلى 10 سنوات
        self.default_expiry_days.setValue(activation.default_expiry_days if activation else 365)
        settings_layout.addRow("مدة صلاحية كود التفعيل الافتراضية (بالأيام):", self.default_expiry_days)

        # عدد أيام التنبيه قبل انتهاء الصلاحية
        self.notify_before_days = QSpinBox()
        self.notify_before_days.setRange(1, 90)  # من يوم إلى 90 يوم
        self.notify_before_days.setValue(activation.notify_before_days if activation else 30)
        settings_layout.addRow("التنبيه قبل انتهاء الصلاحية بـ (أيام):", self.notify_before_days)

        settings_group.setLayout(settings_layout)
        form_layout.addRow("", settings_group)

        layout.addLayout(form_layout)

        # أزرار الحفظ
        buttons_layout = QHBoxLayout()
        self.save_activation_settings_btn = QPushButton("حفظ إعدادات التفعيل")
        self.save_activation_settings_btn.clicked.connect(self.save_activation_settings)
        style_button(self.save_activation_settings_btn, "success", min_width=150, min_height=45)
        buttons_layout.addWidget(self.save_activation_settings_btn)

        layout.addLayout(buttons_layout)

    def save_activation_settings(self):
        """
        حفظ إعدادات التفعيل
        """
        try:
            # الحصول على معلومات التفعيل
            activation = self.session.query(Activation).first()

            if activation:
                # تحديث إعدادات التفعيل
                activation.default_expiry_days = self.default_expiry_days.value()
                activation.notify_before_days = self.notify_before_days.value()

                # حفظ التغييرات
                self.session.commit()
                QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات التفعيل بنجاح")
            else:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على معلومات التفعيل")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ إعدادات التفعيل: {str(e)}")

    def setup_permissions_tab(self):
        """
        إعداد تبويب إدارة الصلاحيات
        """
        layout = QVBoxLayout(self.permissions_tab)

        # العنوان
        title_label = QLabel("إدارة صلاحيات المستخدمين")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # اختيار الدور
        role_layout = QHBoxLayout()
        role_label = QLabel("اختر الدور:")
        self.role_combo = QComboBox()
        self.role_combo.addItems(["admin", "user", "accountant", "sales", "manager"])
        self.role_combo.currentIndexChanged.connect(self.load_role_permissions)
        role_layout.addWidget(role_label)
        role_layout.addWidget(self.role_combo)
        layout.addLayout(role_layout)

        # جدول الصلاحيات
        self.permissions_table = QTableWidget()
        self.permissions_table.setColumnCount(3)
        self.permissions_table.setHorizontalHeaderLabels(["الصلاحية", "الوصف", "مفعلة"])
        self.permissions_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        self.permissions_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.permissions_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.permissions_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        layout.addWidget(self.permissions_table)

        # زر حفظ الصلاحيات
        save_btn = QPushButton("حفظ الصلاحيات")
        save_btn.clicked.connect(self.save_role_permissions)
        style_button(save_btn, "success", min_width=150, min_height=45)
        layout.addWidget(save_btn)

        # تحميل صلاحيات الدور الأول
        self.load_role_permissions()

    def load_role_permissions(self):
        """
        تحميل صلاحيات الدور المحدد
        """
        try:
            # الحصول على الدور المحدد
            role = self.role_combo.currentText()

            # الحصول على جميع الصلاحيات
            all_permissions = get_all_permissions()

            # الحصول على صلاحيات الدور
            role_perms = self.session.query(RolePermission).filter_by(role=role).all()
            role_permission_ids = [rp.permission_id for rp in role_perms]

            # تحديث الجدول
            self.permissions_table.setRowCount(len(all_permissions))

            for i, perm in enumerate(all_permissions):
                # اسم الصلاحية
                name_item = QTableWidgetItem(perm.name)
                name_item.setFlags(name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # جعل الخلية غير قابلة للتعديل
                self.permissions_table.setItem(i, 0, name_item)

                # وصف الصلاحية
                desc_item = QTableWidgetItem(perm.description)
                desc_item.setFlags(desc_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # جعل الخلية غير قابلة للتعديل
                self.permissions_table.setItem(i, 1, desc_item)

                # حالة الصلاحية
                checkbox = QTableWidgetItem()
                checkbox.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)

                # تحديد حالة الصلاحية
                if perm.id in role_permission_ids:
                    checkbox.setCheckState(Qt.CheckState.Checked)
                else:
                    checkbox.setCheckState(Qt.CheckState.Unchecked)

                # تخزين معرف الصلاحية في الخلية
                checkbox.setData(Qt.ItemDataRole.UserRole, perm.id)

                self.permissions_table.setItem(i, 2, checkbox)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل صلاحيات الدور: {str(e)}")

    def save_role_permissions(self):
        """
        حفظ صلاحيات الدور المحدد
        """
        try:
            # الحصول على الدور المحدد
            role = self.role_combo.currentText()

            # حذف جميع صلاحيات الدور الحالية
            self.session.query(RolePermission).filter_by(role=role).delete()

            # إضافة الصلاحيات المحددة
            for i in range(self.permissions_table.rowCount()):
                checkbox = self.permissions_table.item(i, 2)

                if checkbox.checkState() == Qt.CheckState.Checked:
                    # الحصول على معرف الصلاحية
                    permission_id = checkbox.data(Qt.ItemDataRole.UserRole)

                    # إضافة الصلاحية للدور
                    role_perm = RolePermission(
                        role=role,
                        permission_id=permission_id
                    )
                    self.session.add(role_perm)

            # حفظ التغييرات
            self.session.commit()

            QMessageBox.information(self, "تم", "تم حفظ صلاحيات الدور بنجاح")

        except Exception as e:
            self.session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ صلاحيات الدور: {str(e)}")

    def closeEvent(self, event):
        # إغلاق اتصال قاعدة البيانات عند إغلاق النافذة
        self.session.close()
        event.accept()