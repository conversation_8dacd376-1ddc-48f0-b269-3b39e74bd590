from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
                            QPushButton, QLabel, QLineEdit, QComboBox, 
                            QTableWidget, QTableWidgetItem, QFormLayout,
                            QHeaderView, QDialog, QDialogButtonBox, QMessageBox,
                            QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import traceback

from database import Supplier, Purchase, صندوق_النقدية, حركة_نقدية
from ui_utils import style_button, style_dialog_buttons
from datetime import datetime
from translations import get_translation as _

class SuppliersScreen(QWidget):
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_ui()
        self.load_suppliers_data()
        
    def init_ui(self):
        self.setWindowTitle(_("suppliers_title", "نظام إدارة مبيعات الألماس - إدارة الموردين"))
        self.setGeometry(100, 100, 900, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create tabs
        self.suppliers_tab = QWidget()
        self.new_supplier_tab = QWidget()
        
        # Setup tabs
        self.setup_suppliers_tab()
        self.setup_new_supplier_tab()
        
        # Add tabs to tab widget
        self.tab_widget.addTab(self.suppliers_tab, "قائمة الموردين")
        self.tab_widget.addTab(self.new_supplier_tab, "إضافة مورد جديد")
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)
        
    def setup_suppliers_tab(self):
        # Create layout for suppliers tab
        layout = QVBoxLayout(self.suppliers_tab)
        
        # Create search controls
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث باسم المورد أو رقم الهوية")
        
        search_button = QPushButton("بحث")
        search_button.clicked.connect(self.search_suppliers)
        style_button(search_button, "info", min_width=120, min_height=35)
        
        search_layout.addWidget(QLabel("بحث:"))
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_button)
        
        # Create suppliers table
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(6)
        self.suppliers_table.setHorizontalHeaderLabels([
            "رقم المورد", "اسم المورد", "رقم الهوية", "رقم الهاتف", "العنوان", "الرصيد الافتتاحي"
        ])
        header = self.suppliers_table.horizontalHeader()
        for i in range(6):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)
          # Create action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(20)  # زيادة المسافة بين الأزرار
        action_layout.setContentsMargins(5, 5, 5, 15)  # هوامش لمجموعة الأزرار
        
        view_button = QPushButton("عرض التفاصيل")
        view_button.clicked.connect(self.view_supplier_details)
        style_button(view_button, "info")
        
        edit_button = QPushButton("تعديل")
        edit_button.clicked.connect(self.edit_supplier)
        style_button(edit_button, "edit")
        
        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_supplier)
        style_button(delete_button, "delete")
        
        view_purchases_button = QPushButton("عرض المشتريات")
        view_purchases_button.clicked.connect(self.view_supplier_purchases)
        style_button(view_purchases_button, "success")
        
        action_layout.addWidget(view_button)
        action_layout.addWidget(edit_button)
        action_layout.addWidget(delete_button)
        action_layout.addWidget(view_purchases_button)
        
        # Add widgets to layout
        layout.addLayout(search_layout)
        layout.addWidget(self.suppliers_table)
        layout.addLayout(action_layout)
        
    def setup_new_supplier_tab(self):
        # Create layout for new supplier tab
        layout = QVBoxLayout(self.new_supplier_tab)
        
        # Create form layout for supplier details
        form_layout = QFormLayout()
        
        # Name input
        self.name_input = QLineEdit()
        
        # ID Number input
        self.id_number_input = QLineEdit()
        
        # Phone input
        self.phone_input = QLineEdit()
          # Address input
        self.address_input = QLineEdit()
        
        # Opening Balance input
        self.opening_balance_input = QLineEdit()
        self.opening_balance_input.setPlaceholderText("0.00")
        self.opening_balance_input.textChanged.connect(self.calculate_opening_balance_sar)
        
        # Opening Balance in SAR (read-only)
        self.opening_balance_sar_input = QLineEdit()
        self.opening_balance_sar_input.setPlaceholderText("0.00")
        self.opening_balance_sar_input.setReadOnly(True)
        self.opening_balance_sar_input.setStyleSheet("background-color: #f0f0f0;")
        
        # Add fields to form layout
        form_layout.addRow("اسم المورد:", self.name_input)
        form_layout.addRow("رقم الهوية:", self.id_number_input)
        form_layout.addRow("رقم الهاتف:", self.phone_input)
        form_layout.addRow("العنوان:", self.address_input)
        form_layout.addRow("الرصيد الافتتاحي (دولار):", self.opening_balance_input)
        form_layout.addRow("الرصيد الافتتاحي (ريال):", self.opening_balance_sar_input)
        
        # Add spacer
        layout.addSpacerItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
          # Create button to save supplier
        save_button = QPushButton("حفظ بيانات المورد")
        save_button.clicked.connect(self.save_supplier)
        style_button(save_button, "add", min_width=200, min_height=45)
        
        # Add widgets to layout
        layout.addLayout(form_layout)
        layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        layout.addWidget(save_button)
        
    def calculate_opening_balance_sar(self):
        """حساب المقابل بالريال للرصيد الافتتاحي"""
        try:
            usd_amount = float(self.opening_balance_input.text() or '0')
            sar_amount = usd_amount * 3.75  # سعر الصرف الثابت
            self.opening_balance_sar_input.setText(f"{sar_amount:.2f}")
        except ValueError:
            self.opening_balance_sar_input.setText("0.00")
        
    def load_suppliers_data(self):
        try:
            # Connect to database
            engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Get all suppliers
                suppliers = session.query(Supplier).all()
                
                # Update table
                self.suppliers_table.setRowCount(len(suppliers))
                
                for i, supplier in enumerate(suppliers):
                    self.suppliers_table.setItem(i, 0, QTableWidgetItem(str(supplier.id)))
                    self.suppliers_table.setItem(i, 1, QTableWidgetItem(supplier.name))
                    self.suppliers_table.setItem(i, 2, QTableWidgetItem(supplier.id_number))
                    self.suppliers_table.setItem(i, 3, QTableWidgetItem(supplier.phone))
                    self.suppliers_table.setItem(i, 4, QTableWidgetItem(supplier.address))
                    self.suppliers_table.setItem(i, 5, QTableWidgetItem(f"{supplier.opening_balance:.2f}"))
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ",
                    f"حدث خطأ أثناء تحميل بيانات الموردين: {str(e)}\n\nالتفاصيل الكاملة:\n{traceback.format_exc()}")
            finally:
                session.close()
        except Exception as e:
            QMessageBox.critical(self, "خطأ",
                f"خطأ في الاتصال بقاعدة البيانات: {str(e)}\n\nالتفاصيل الكاملة:\n{traceback.format_exc()}")
    
    def search_suppliers(self):
        # Get search text
        search_text = self.search_input.text().strip()
        
        if not search_text:
            # If search is empty, reload all data
            self.load_suppliers_data()
            return
        
        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # Search for suppliers
            suppliers = session.query(Supplier).filter(
                (Supplier.name.like(f'%{search_text}%')) | 
                (Supplier.id_number.like(f'%{search_text}%'))
            ).all()
            
            # Update table
            self.suppliers_table.setRowCount(len(suppliers))
            
            for i, supplier in enumerate(suppliers):
                self.suppliers_table.setItem(i, 0, QTableWidgetItem(str(supplier.id)))
                self.suppliers_table.setItem(i, 1, QTableWidgetItem(supplier.name))
                self.suppliers_table.setItem(i, 2, QTableWidgetItem(supplier.id_number))
                self.suppliers_table.setItem(i, 3, QTableWidgetItem(supplier.phone))
                self.suppliers_table.setItem(i, 4, QTableWidgetItem(supplier.address))
                self.suppliers_table.setItem(i, 5, QTableWidgetItem(f"{supplier.opening_balance:.2f}"))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
        finally:
            session.close()
    
    def save_supplier(self):
        # Get form data
        name = self.name_input.text().strip()
        id_number = self.id_number_input.text().strip()
        phone = self.phone_input.text().strip()
        address = self.address_input.text().strip()
        
        # Get and validate opening balance
        try:
            opening_balance = float(self.opening_balance_input.text().strip() or '0')
        except ValueError:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال رقم صحيح للرصيد الافتتاحي")
            return

        # Validate form data
        if not name:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المورد")
            return

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Create new supplier
            supplier = Supplier(
                name=name,
                id_number=id_number,
                phone=phone,
                address=address,
                opening_balance=opening_balance
            )
            session.add(supplier)
            
            # تم إزالة إضافة الرصيد الافتتاحي إلى صندوق النقدية
            # الرصيد الافتتاحي يُحفظ فقط كمعلومة للمورد ولا يؤثر على صندوق النقدية

            session.commit()
            QMessageBox.information(self, "نجاح", "تم حفظ بيانات المورد بنجاح")

            # Clear form
            self.name_input.clear()
            self.id_number_input.clear()
            self.phone_input.clear()
            self.address_input.clear()
            self.opening_balance_input.clear()
            self.opening_balance_sar_input.clear()

            # Refresh suppliers table
            self.load_suppliers_data()

            # Switch to suppliers tab
            self.tab_widget.setCurrentIndex(0)

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ بيانات المورد: {str(e)}")
        finally:
            session.close()
    
    def view_supplier_details(self):
        # Get selected row
        selected_row = self.suppliers_table.currentRow()
        
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مورد لعرض بياناته")
            return
            
        # Get supplier ID
        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        
        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # Get supplier
            supplier = session.query(Supplier).filter_by(id=supplier_id).first()
            
            if not supplier:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على المورد")
                return
                
            # Show details in a dialog
            dialog = QDialog(self)
            dialog.setWindowTitle(f"بيانات المورد: {supplier.name}")
            dialog.setMinimumWidth(400)
            
            layout = QVBoxLayout(dialog)
            
            # Create labels with supplier information
            info_text = f"""
            <h3>بيانات المورد</h3>
            <p><b>الاسم:</b> {supplier.name}</p>
            <p><b>رقم الهوية:</b> {supplier.id_number or 'غير محدد'}</p>
            <p><b>رقم الهاتف:</b> {supplier.phone or 'غير محدد'}</p>
            <p><b>العنوان:</b> {supplier.address or 'غير محدد'}</p>
            """
            
            info_label = QLabel(info_text)
            info_label.setTextFormat(Qt.TextFormat.RichText)
            layout.addWidget(info_label)
            
            # Purchases summary
            purchases = session.query(Purchase).filter_by(supplier_id=supplier_id).all()
            
            if purchases:
                # Calculate total purchases
                total_carats = sum(purchase.carat_weight for purchase in purchases)
                total_amount_usd = sum(purchase.total_price_usd for purchase in purchases)
                total_amount_sar = sum(purchase.total_price_sar for purchase in purchases)
                total_paid = sum(purchase.amount_paid for purchase in purchases)
                total_due = sum(purchase.amount_due for purchase in purchases)
                
                # Add purchases summary
                purchases_text = f"""
                <h3>ملخص المشتريات</h3>
                <p><b>عدد المشتريات:</b> {len(purchases)}</p>
                <p><b>إجمالي القيراط:</b> {total_carats:.2f}</p>
                <p><b>إجمالي المشتريات بالدولار:</b> {total_amount_usd:.2f} $</p>
                <p><b>إجمالي المشتريات بالريال:</b> {total_amount_sar:.2f} ريال</p>
                <p><b>إجمالي المبالغ المدفوعة:</b> {total_paid:.2f} $</p>
                <p><b>إجمالي المبالغ المستحقة:</b> {total_due:.2f} $</p>                """
                purchases_label = QLabel(purchases_text)
                purchases_label.setTextFormat(Qt.TextFormat.RichText)
                layout.addWidget(purchases_label)
            
            # Add a close button
            buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
            buttons.rejected.connect(dialog.reject)
            style_dialog_buttons(buttons)
            layout.addWidget(buttons)
            
            # Show the dialog
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض بيانات المورد: {str(e)}")
        finally:
            session.close()
    
    def edit_supplier(self):
        # Get selected row
        selected_row = self.suppliers_table.currentRow()
        
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مورد لتعديل بياناته")
            return
            
        # Get supplier ID
        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        
        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # Get supplier
            supplier = session.query(Supplier).filter_by(id=supplier_id).first()
            
            if not supplier:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على المورد")
                return
                
            # Create dialog for editing
            dialog = QDialog(self)
            dialog.setWindowTitle(f"تعديل بيانات المورد: {supplier.name}")
            dialog.setMinimumWidth(400)
            
            layout = QVBoxLayout(dialog)
            form_layout = QFormLayout()
            
            # Create input fields
            name_input = QLineEdit(supplier.name)
            id_number_input = QLineEdit(supplier.id_number or "")
            phone_input = QLineEdit(supplier.phone or "")
            address_input = QLineEdit(supplier.address or "")
            opening_balance_input = QLineEdit(str(supplier.opening_balance))
            
            # Add fields to form layout
            form_layout.addRow("اسم المورد:", name_input)
            form_layout.addRow("رقم الهوية:", id_number_input)
            form_layout.addRow("رقم الهاتف:", phone_input)
            form_layout.addRow("العنوان:", address_input)
            form_layout.addRow("الرصيد الافتتاحي:", opening_balance_input)
            
            layout.addLayout(form_layout)
              # Add buttons
            button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
            button_box.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
            button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            style_dialog_buttons(button_box)
            layout.addWidget(button_box)
            
            # Execute dialog
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Validate opening balance
                try:
                    new_opening_balance = float(opening_balance_input.text().strip())
                except ValueError:
                    QMessageBox.warning(self, "خطأ", "الرجاء إدخال رقم صحيح للرصيد الافتتاحي")
                    return
                
                # Update supplier information
                supplier.name = name_input.text().strip()
                supplier.id_number = id_number_input.text().strip()
                supplier.phone = phone_input.text().strip()
                supplier.address = address_input.text().strip()
                supplier.opening_balance = new_opening_balance
                
                # Save changes
                session.commit()
                
                # Show success message
                QMessageBox.information(self, "نجاح", "تم تعديل بيانات المورد بنجاح")
                
                # Reload suppliers data
                self.load_suppliers_data()
                
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل بيانات المورد: {str(e)}")
        finally:
            session.close()
    
    def delete_supplier(self):
        # Get selected row
        selected_row = self.suppliers_table.currentRow()
        
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مورد لحذفه")
            return
            
        # Get supplier ID
        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier_name = self.suppliers_table.item(selected_row, 1).text()
        
        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # Check if supplier has purchases
            purchases_count = session.query(Purchase).filter_by(supplier_id=supplier_id).count()
            
            if purchases_count > 0:
                QMessageBox.warning(self, "تحذير", 
                                 f"لا يمكن حذف المورد '{supplier_name}' لأنه مرتبط بـ {purchases_count} عملية شراء.")
                return
            
            # Confirm deletion
            reply = QMessageBox.question(self, "تأكيد", 
                                   f"هل أنت متأكد من رغبتك في حذف المورد '{supplier_name}'؟",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                # Delete supplier
                session.query(Supplier).filter_by(id=supplier_id).delete()
                
                # Commit changes
                session.commit()
                
                # Show success message
                QMessageBox.information(self, "نجاح", "تم حذف المورد بنجاح")
                
                # Reload suppliers data
                self.load_suppliers_data()
                
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المورد: {str(e)}")
        finally:
            session.close()
            
    def view_supplier_purchases(self):
        # Get selected row
        selected_row = self.suppliers_table.currentRow()
        
        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار مورد لعرض مشترياته")
            return
            
        # Get supplier ID
        supplier_id = int(self.suppliers_table.item(selected_row, 0).text())
        supplier_name = self.suppliers_table.item(selected_row, 1).text()
        
        # Create dialog for purchases
        dialog = QDialog(self)
        dialog.setWindowTitle(f"مشتريات المورد: {supplier_name}")
        dialog.setMinimumWidth(800)
        dialog.setMinimumHeight(500)
        
        layout = QVBoxLayout(dialog)
        
        # Add title
        title = QLabel(f"مشتريات المورد: <b>{supplier_name}</b>")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Create purchases table
        purchases_table = QTableWidget()
        purchases_table.setColumnCount(8)
        purchases_table.setHorizontalHeaderLabels([
            "رقم المشتريات", "نوع الألماس", "الوزن بالقيراط", 
            "السعر/قيراط ($)", "الإجمالي ($)", "الإجمالي (ريال)", 
            "تاريخ الشراء", "المبلغ المتبقي"
        ])
        header = purchases_table.horizontalHeader()
        for i in range(8):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)
            
        layout.addWidget(purchases_table)
          # Add a close button
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        buttons.rejected.connect(dialog.reject)
        style_dialog_buttons(buttons)
        layout.addWidget(buttons)
        
        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # Get purchases for supplier
            purchases = session.query(Purchase).filter_by(supplier_id=supplier_id).all()
            
            # Update table
            purchases_table.setRowCount(len(purchases))
            
            for i, purchase in enumerate(purchases):
                purchases_table.setItem(i, 0, QTableWidgetItem(str(purchase.id)))
                purchases_table.setItem(i, 1, QTableWidgetItem(purchase.diamond_type))
                purchases_table.setItem(i, 2, QTableWidgetItem(f"{purchase.carat_weight:.2f}"))
                purchases_table.setItem(i, 3, QTableWidgetItem(f"{purchase.price_per_carat_usd:.2f}"))
                purchases_table.setItem(i, 4, QTableWidgetItem(f"{purchase.total_price_usd:.2f}"))
                purchases_table.setItem(i, 5, QTableWidgetItem(f"{purchase.total_price_sar:.2f}"))
                
                # Format date
                date_str = purchase.purchase_date.strftime("%d/%m/%Y") if purchase.purchase_date else ""
                purchases_table.setItem(i, 6, QTableWidgetItem(date_str))
                
                purchases_table.setItem(i, 7, QTableWidgetItem(f"{purchase.amount_due:.2f}"))
                
            # If no purchases, show message
            if not purchases:
                purchases_table.setRowCount(0)
                QMessageBox.information(dialog, "معلومات", "لا توجد مشتريات لهذا المورد")
                
            # Show the dialog
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض مشتريات المورد: {str(e)}")
        finally:
            session.close()