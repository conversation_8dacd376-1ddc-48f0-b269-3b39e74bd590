#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for cash box screen functionality
This will test the screen rendering and the printing functionality
"""

from PyQt6.QtWidgets import QApplication
import sys
from cash_box_screen import شاشة_صندوق_النقدية

class MockUser:
    """Mock user for testing"""
    def __init__(self):
        self.id = 1
        self.role = 'admin'

def main():
    """Main function to test the cash box screen"""
    app = QApplication(sys.argv)
    
    # Create a mock user with admin permissions
    user = MockUser()
    
    # Create and show the cash box screen
    screen = شاشة_صندوق_النقدية(user)
    screen.show()
    
    # Run the application
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
