#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار شامل لصندوق النقدية مع التركيز على سندات الصرف
"""

from PyQt6.QtWidgets import QApplication, QMessageBox, QTableWidget, QTableWidgetItem
from PyQt6.QtCore import Qt, QTimer
import sys
from datetime import datetime, timedelta
from sqlalchemy import func, desc, or_, case, String, Float
from db_session import session_scope
from database import صندوق_النقدية, حركة_نقدية, Receipt, User, Purchase, Supplier

class ComprehensiveCashBoxTester:
    """اختبار شامل لصندوق النقدية"""
    
    def __init__(self):
        self.app = QApplication.instance() or QApplication(sys.argv)
        self.results = {}
    
    def check_withdrawal_vouchers(self):
        """التحقق من وجود سندات الصرف في حركات النقدية"""
        try:
            with session_scope() as session:                # 1. سندات الصرف المباشرة (مباشرة من Receipt)
                withdrawal_receipts = session.query(Receipt).filter(
                    or_(Receipt.receipt_type == "CashOut", Receipt.receipt_type == "صرف")
                ).all()
                
                withdrawal_count = len(withdrawal_receipts)
                  # 2. سندات الصرف غير المباشرة (من فواتير المشتريات)
                purchase_withdrawals = session.query(Purchase).filter(
                    Purchase.amount_paid > 0
                ).all()
                
                purchase_withdrawal_count = len(purchase_withdrawals)
                
                # 3. جميع حركات السحب في قاعدة البيانات
                withdraw_transactions = session.query(حركة_نقدية).filter(
                    حركة_نقدية.transaction_type == "withdraw"
                ).all()
                
                withdraw_count = len(withdraw_transactions)
                
                # 4. التحقق من تطابق سندات الصرف المباشرة
                direct_matching_count = 0
                direct_missing = []
                
                for receipt in withdrawal_receipts:                    # البحث عن الحركة المرتبطة
                    transaction = session.query(حركة_نقدية).filter(
                        or_(
                            حركة_نقدية.reference.like(f"%سند صرف%{receipt.id}%"),
                            حركة_نقدية.reference.like(f"%voucher%{receipt.id}%")
                        )
                    ).first()
                    
                    if transaction:
                        direct_matching_count += 1
                    else:
                        # تجميع معلومات السندات المفقودة
                        supplier_name = "غير معروف"
                        if receipt.supplier_id:
                            supplier = session.query(Supplier).filter_by(id=receipt.supplier_id).first()
                            if supplier:
                                supplier_name = supplier.name
                        
                        direct_missing.append({
                            "id": receipt.id,
                            "amount": receipt.amount_usd,
                            "date": receipt.issue_date,
                            "supplier": supplier_name
                        })
                
                # 5. التحقق من تطابق سندات الصرف من المشتريات
                purchase_matching_count = 0
                purchase_missing = []
                
                for purchase in purchase_withdrawals:
                    # البحث عن الحركة المرتبطة
                    transaction = session.query(حركة_نقدية).filter(
                        حركة_نقدية.reference.like(f"%فاتورة مشتريات%{purchase.id}%")
                    ).first()
                    
                    if transaction:
                        purchase_matching_count += 1
                    else:
                        # تجميع معلومات الفواتير المفقودة
                        supplier_name = "غير معروف"
                        if purchase.supplier_id:
                            supplier = session.query(Supplier).filter_by(id=purchase.supplier_id).first()
                            if supplier:
                                supplier_name = supplier.name
                          purchase_missing.append({
                            "id": purchase.id,
                            "amount": purchase.amount_paid,
                            "date": purchase.purchase_date,
                            "supplier": supplier_name
                        })
                
                # 6. التحقق من الرصيد
                cash_box = session.query(صندوق_النقدية).first()
                cash_box_balance = cash_box.balance if cash_box else 0
                
                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0
                
                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0
                
                calculated_balance = total_deposits - total_withdrawals
                balance_difference = abs(calculated_balance - cash_box_balance)                
                # 7. تجميع النتائج
                self.results = {
                    "direct_withdrawal_receipts": withdrawal_count,
                    "purchase_withdrawals": purchase_withdrawal_count,
                    "all_withdraw_transactions": withdraw_count,
                    
                    "direct_matching_count": direct_matching_count,
                    "direct_missing_count": withdrawal_count - direct_matching_count,
                    "direct_missing_details": direct_missing,
                    
                    "purchase_matching_count": purchase_matching_count,
                    "purchase_missing_count": purchase_withdrawal_count - purchase_matching_count,
                    "purchase_missing_details": purchase_missing,
                    
                    "total_deposits": total_deposits,
                    "total_withdrawals": total_withdrawals,
                    "calculated_balance": calculated_balance,
                    "cash_box_balance": cash_box_balance,
                    "balance_difference": balance_difference
                }
                
                return self.results
                
        except Exception as e:
            self.results = {"error": str(e)}
            return self.results
    
    def display_results(self):
        """عرض نتائج الاختبار"""
        if "error" in self.results:
            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء الاختبار: {self.results['error']}")
            return
        
        message = f"""
        === نتائج اختبار صندوق النقدية ===
        
        1. سندات الصرف المباشرة:
           - إجمالي السندات: {self.results['direct_withdrawal_receipts']}
           - السندات المطابقة: {self.results['direct_matching_count']}
           - السندات المفقودة: {self.results['direct_missing_count']}
        
        2. سندات الصرف من المشتريات:
           - إجمالي دفعات المشتريات: {self.results['purchase_withdrawals']}
           - الدفعات المطابقة: {self.results['purchase_matching_count']}
           - الدفعات المفقودة: {self.results['purchase_missing_count']}
        
        3. حالة الصندوق:
           - إجمالي الإيداعات: {self.results['total_deposits']:.2f} $
           - إجمالي المسحوبات: {self.results['total_withdrawals']:.2f} $
           - الرصيد المحسوب: {self.results['calculated_balance']:.2f} $
           - رصيد الصندوق: {self.results['cash_box_balance']:.2f} $
           - الفرق: {self.results['balance_difference']:.2f} $
        
        === ملخص الاختبار ===
        """
        
        has_issues = False
        issues_text = ""
        
        if self.results['direct_missing_count'] > 0:
            has_issues = True
            issues_text += f"\n- يوجد {self.results['direct_missing_count']} سند صرف مباشر غير مرتبط بحركات نقدية"
            
            # إظهار تفاصيل السندات المفقودة
            if self.results['direct_missing_details']:
                issues_text += ":\n"
                for receipt in self.results['direct_missing_details'][:5]:  # عرض أول 5 سندات فقط
                    issues_text += f"  * سند رقم {receipt['id']} - {receipt['amount']:.2f} $ - {receipt['supplier']}\n"
                if len(self.results['direct_missing_details']) > 5:
                    issues_text += f"  * و{len(self.results['direct_missing_details']) - 5} سندات أخرى\n"
        
        if self.results['purchase_missing_count'] > 0:
            has_issues = True
            issues_text += f"\n- يوجد {self.results['purchase_missing_count']} دفعة مشتريات غير مرتبطة بحركات نقدية"
            
            # إظهار تفاصيل الفواتير المفقودة
            if self.results['purchase_missing_details']:
                issues_text += ":\n"
                for purchase in self.results['purchase_missing_details'][:5]:  # عرض أول 5 فواتير فقط
                    issues_text += f"  * فاتورة رقم {purchase['id']} - {purchase['amount']:.2f} $ - {purchase['supplier']}\n"
                if len(self.results['purchase_missing_details']) > 5:
                    issues_text += f"  * و{len(self.results['purchase_missing_details']) - 5} فواتير أخرى\n"
        
        if self.results['balance_difference'] > 0.01:
            has_issues = True
            issues_text += f"\n- يوجد اختلاف في الرصيد بقيمة {self.results['balance_difference']:.2f} $"
        
        if has_issues:
            QMessageBox.warning(None, "تحذير", message + "\nتم رصد المشكلات التالية:" + issues_text + "\n\nيرجى تشغيل برنامج fix_cash_vouchers.py لإصلاح هذه المشكلات.")
        else:
            QMessageBox.information(None, "نجاح", message + "\nجميع سندات الصرف مرتبطة بحركات نقدية بشكل صحيح، والأرصدة متطابقة.")
    
    def run_test(self):
        """تشغيل الاختبار"""
        self.check_withdrawal_vouchers()
        self.display_results()

def test_cash_box_screen():
    """اختبار شاشة صندوق النقدية"""
    try:
        # محاولة استيراد الملف المحسن
        from cash_box_screen_fixed import شاشة_صندوق_النقدية
    except ImportError:
        # إذا لم ينجح، نستخدم الملف الأصلي
        try:
            from cash_box_screen import شاشة_صندوق_النقدية
        except ImportError:
            QMessageBox.critical(None, "خطأ", "لم يتم العثور على ملف شاشة صندوق النقدية")
            return
    
    app = QApplication.instance() or QApplication(sys.argv)
    
    # إنشاء مستخدم وهمي للاختبار
    class MockUser:
        def __init__(self):
            self.id = 1
            self.role = 'admin'
    
    # إنشاء شاشة صندوق النقدية
    cash_box_screen = شاشة_صندوق_النقدية(MockUser())
    cash_box_screen.show()
    
    # إغلاق الشاشة بعد 5 ثوانٍ
    QTimer.singleShot(5000, cash_box_screen.close)
    
    app.exec()

if __name__ == "__main__":
    # تحديد ما إذا كنت تريد تشغيل اختبار الشاشة أو اختبار البيانات
    if len(sys.argv) > 1 and sys.argv[1] == "--ui":
        test_cash_box_screen()
    else:
        # تشغيل اختبار بيانات صندوق النقدية
        tester = ComprehensiveCashBoxTester()
        tester.run_test()
    
    # إنشاء شاشة صندوق النقدية
    cash_box_screen = شاشة_صندوق_النقدية(MockUser())
    cash_box_screen.show()
    
    # تشغيل التطبيق
    app.exec()

if __name__ == "__main__":
    # تشغيل اختبار بيانات صندوق النقدية
    tester = ComprehensiveCashBoxTester()
    tester.run_test()
    
    # إذا كان الوسيط --ui موجودًا، قم بعرض شاشة صندوق النقدية
    if len(sys.argv) > 1 and sys.argv[1] == "--ui":
        test_cash_box_screen()
