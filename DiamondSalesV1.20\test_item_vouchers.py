"""
ملف اختبار ميزة سندات الأصناف
يقوم هذا الملف باختبار جميع الوظائف الجديدة المتعلقة بسندات الأصناف
"""

import sys
import os
from datetime import datetime, timedelta
from database import Receipt, ReceiptItem, InventoryMovement, Customer, Supplier, Category, Unit, OpeningBalance
from db_session import session_scope
from inventory_utils import (
    calculate_available_quantity, 
    validate_quantity_availability, 
    create_inventory_movement,
    get_full_inventory_summary
)
from logger import log_info, log_error

def test_database_structure():
    """اختبار بنية قاعدة البيانات الجديدة"""
    print("🔍 اختبار بنية قاعدة البيانات...")
    
    try:
        with session_scope() as session:
            # اختبار وجود الجداول الجديدة
            receipt_items = session.query(ReceiptItem).count()
            inventory_movements = session.query(InventoryMovement).count()
            
            print(f"✅ جدول ReceiptItem: {receipt_items} سجل")
            print(f"✅ جدول InventoryMovement: {inventory_movements} سجل")
            
            # اختبار وجود البيانات الأساسية
            categories = session.query(Category).count()
            units = session.query(Unit).count()
            opening_balances = session.query(OpeningBalance).count()
            
            print(f"✅ الفئات: {categories} فئة")
            print(f"✅ الوحدات: {units} وحدة")
            print(f"✅ الأرصدة الافتتاحية: {opening_balances} رصيد")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار بنية قاعدة البيانات: {str(e)}")
        return False

def test_inventory_calculations():
    """اختبار حسابات المخزون"""
    print("\n🧮 اختبار حسابات المخزون...")
    
    try:
        # اختبار حساب الكمية المتاحة
        diamond_type = "ألماس أبيض 1 قيراط"
        available_qty = calculate_available_quantity(diamond_type)
        print(f"✅ الكمية المتاحة لـ {diamond_type}: {available_qty:.3f}")
        
        # اختبار التحقق من توفر الكمية
        test_quantity = 10.0
        is_available, available = validate_quantity_availability(diamond_type, test_quantity)
        print(f"✅ التحقق من توفر {test_quantity} من {diamond_type}: {'متوفر' if is_available else 'غير متوفر'} (متاح: {available:.3f})")
        
        # اختبار ملخص المخزون
        inventory_summary = get_full_inventory_summary()
        print(f"✅ ملخص المخزون: {len(inventory_summary)} صنف")
        
        for diamond_type, data in list(inventory_summary.items())[:3]:  # عرض أول 3 أصناف
            print(f"   - {diamond_type}: متاح {data['available_quantity']:.3f}, حالة: {data['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حسابات المخزون: {str(e)}")
        return False

def test_create_item_voucher():
    """اختبار إنشاء سند أصناف"""
    print("\n📝 اختبار إنشاء سند أصناف...")
    
    try:
        with session_scope() as session:
            # الحصول على عميل ومورد للاختبار
            customer = session.query(Customer).first()
            supplier = session.query(Supplier).first()
            
            if not customer:
                print("⚠️ لا يوجد عملاء في قاعدة البيانات")
                return False
            
            if not supplier:
                print("⚠️ لا يوجد موردين في قاعدة البيانات")
                return False
            
            # إنشاء سند صرف أصناف للعميل
            receipt_out = Receipt(
                customer_id=customer.id,
                receipt_type="ItemOut",
                issue_date=datetime.now(),
                notes="سند اختبار - صرف أصناف للعميل"
            )
            session.add(receipt_out)
            session.flush()
            
            # إضافة تفاصيل الأصناف
            receipt_item = ReceiptItem(
                receipt_id=receipt_out.id,
                diamond_type="ألماس أبيض 1 قيراط",
                category_id=1,
                unit_id=1,
                quantity=5.0,
                price_per_unit_usd=1000.0,
                total_value_usd=5000.0
            )
            session.add(receipt_item)
            
            # إنشاء حركة مخزون
            success = create_inventory_movement(
                diamond_type="ألماس أبيض 1 قيراط",
                movement_type="out",
                quantity=5.0,
                price_per_unit=1000.0,
                category_id=1,
                unit_id=1,
                reference_type="receipt",
                reference_id=receipt_out.id,
                customer_id=customer.id,
                notes="اختبار صرف أصناف",
                session=session
            )
            
            if success:
                print(f"✅ تم إنشاء سند صرف أصناف رقم {receipt_out.id} للعميل {customer.name}")
            else:
                print("❌ فشل في إنشاء حركة المخزون")
                return False
            
            # إنشاء سند استلام أصناف من المورد
            receipt_in = Receipt(
                supplier_id=supplier.id,
                receipt_type="ItemIn",
                issue_date=datetime.now(),
                notes="سند اختبار - استلام أصناف من المورد"
            )
            session.add(receipt_in)
            session.flush()
            
            # إضافة تفاصيل الأصناف
            receipt_item_in = ReceiptItem(
                receipt_id=receipt_in.id,
                diamond_type="ألماس أصفر 0.5 قيراط",
                category_id=1,
                unit_id=1,
                quantity=10.0,
                price_per_unit_usd=800.0,
                total_value_usd=8000.0
            )
            session.add(receipt_item_in)
            
            # إنشاء حركة مخزون
            success = create_inventory_movement(
                diamond_type="ألماس أصفر 0.5 قيراط",
                movement_type="in",
                quantity=10.0,
                price_per_unit=800.0,
                category_id=1,
                unit_id=1,
                reference_type="receipt",
                reference_id=receipt_in.id,
                supplier_id=supplier.id,
                notes="اختبار استلام أصناف",
                session=session
            )
            
            if success:
                print(f"✅ تم إنشاء سند استلام أصناف رقم {receipt_in.id} من المورد {supplier.name}")
            else:
                print("❌ فشل في إنشاء حركة المخزون للاستلام")
                return False
            
            session.commit()
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء سند أصناف: {str(e)}")
        return False

def test_voucher_queries():
    """اختبار استعلامات السندات"""
    print("\n🔍 اختبار استعلامات السندات...")
    
    try:
        with session_scope() as session:
            # اختبار استعلام سندات الأصناف
            item_vouchers = session.query(Receipt).filter(
                Receipt.receipt_type.in_(["ItemOut", "ItemIn"])
            ).all()
            
            print(f"✅ عدد سندات الأصناف: {len(item_vouchers)}")
            
            for voucher in item_vouchers:
                voucher_type = "صرف أصناف" if voucher.receipt_type == "ItemOut" else "استلام أصناف"
                entity_name = "غير محدد"
                
                if voucher.customer_id:
                    customer = session.query(Customer).filter_by(id=voucher.customer_id).first()
                    if customer:
                        entity_name = customer.name
                elif voucher.supplier_id:
                    supplier = session.query(Supplier).filter_by(id=voucher.supplier_id).first()
                    if supplier:
                        entity_name = supplier.name
                
                items_count = session.query(ReceiptItem).filter_by(receipt_id=voucher.id).count()
                print(f"   - سند {voucher_type} رقم {voucher.id}: {entity_name} ({items_count} صنف)")
            
            # اختبار استعلام تفاصيل الأصناف
            receipt_items = session.query(ReceiptItem).all()
            print(f"✅ عدد تفاصيل الأصناف: {len(receipt_items)}")
            
            # اختبار استعلام حركات المخزون
            inventory_movements = session.query(InventoryMovement).all()
            print(f"✅ عدد حركات المخزون: {len(inventory_movements)}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار استعلامات السندات: {str(e)}")
        return False

def test_inventory_after_movements():
    """اختبار المخزون بعد الحركات"""
    print("\n📊 اختبار المخزون بعد الحركات...")
    
    try:
        # اختبار الكميات المتاحة بعد الحركات
        test_items = [
            "ألماس أبيض 1 قيراط",
            "ألماس أصفر 0.5 قيراط",
            "زمرد طبيعي",
            "ياقوت أحمر"
        ]
        
        for item in test_items:
            available_qty = calculate_available_quantity(item)
            print(f"✅ {item}: {available_qty:.3f} متاح")
        
        # اختبار ملخص المخزون المحدث
        inventory_summary = get_full_inventory_summary()
        print(f"\n📈 ملخص المخزون المحدث:")
        
        for diamond_type, data in inventory_summary.items():
            print(f"   - {diamond_type}:")
            print(f"     الرصيد الافتتاحي: {data['opening_quantity']:.3f}")
            print(f"     إجمالي الداخل: {data['total_in']:.3f}")
            print(f"     إجمالي الخارج: {data['total_out']:.3f}")
            print(f"     المتاح: {data['available_quantity']:.3f}")
            print(f"     الحالة: {data['status']}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المخزون بعد الحركات: {str(e)}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار ميزة سندات الأصناف")
    print("=" * 50)
    
    tests = [
        ("اختبار بنية قاعدة البيانات", test_database_structure),
        ("اختبار حسابات المخزون", test_inventory_calculations),
        ("اختبار إنشاء سند أصناف", test_create_item_voucher),
        ("اختبار استعلامات السندات", test_voucher_queries),
        ("اختبار المخزون بعد الحركات", test_inventory_after_movements)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                failed += 1
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: خطأ - {str(e)}")
        
        print("-" * 30)
    
    print(f"\n📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print(f"\n⚠️ {failed} اختبار فشل. يرجى مراجعة الأخطاء.")

if __name__ == "__main__":
    run_all_tests()
