"""
اختبار إصلاح مشاكل شاشة التقارير
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from reports_screen import ReportsScreen, safe_format, safe_str
from database import User
from db_session import session_scope

def test_safe_format_functions():
    """اختبار الدوال المساعدة للتنسيق الآمن"""
    try:
        # اختبار safe_format
        assert safe_format(123.456) == "123.456"
        assert safe_format(None) == "0.000"
        assert safe_format(0) == "0.000"
        assert safe_format(123.456, "{:.2f}") == "123.46"
        assert safe_format("invalid", "{:.2f}", "error") == "error"
        
        # اختبار safe_str
        assert safe_str("test") == "test"
        assert safe_str(None) == "غير محدد"
        assert safe_str("") == "غير محدد"
        assert safe_str(123) == "123"
        assert safe_str(None, "custom_default") == "custom_default"
        
        print("✅ جميع اختبارات الدوال المساعدة نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال المساعدة: {str(e)}")
        return False

def test_reports_screen():
    """اختبار فتح شاشة التقارير"""
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إنشاء مستخدم وهمي للاختبار
        with session_scope() as session:
            user = session.query(User).first()
            if not user:
                print("لا يوجد مستخدمين في قاعدة البيانات")
                return False
        
        # إنشاء شاشة التقارير
        reports_screen = ReportsScreen(user)
        reports_screen.show()
        
        print("✅ تم فتح شاشة التقارير بنجاح")
        
        # إغلاق التطبيق
        reports_screen.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فتح شاشة التقارير: {str(e)}")
        return False

def test_translations():
    """اختبار دالة الترجمة المحدثة"""
    try:
        from translations import get_translation
        
        # اختبار الترجمة مع معامل واحد
        result1 = get_translation("reports_title")
        print(f"ترجمة 'reports_title': {result1}")
        
        # اختبار الترجمة مع معاملين
        result2 = get_translation("unknown_key", "نص افتراضي")
        print(f"ترجمة مفتاح غير موجود: {result2}")
        
        # اختبار الترجمة مع مفتاح غير موجود بدون نص افتراضي
        result3 = get_translation("another_unknown_key")
        print(f"ترجمة مفتاح غير موجود بدون افتراضي: {result3}")
        
        print("✅ جميع اختبارات الترجمة نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الترجمة: {str(e)}")
        return False

def test_format_edge_cases():
    """اختبار حالات خاصة للتنسيق"""
    try:
        # اختبار قيم مختلفة
        test_cases = [
            (None, "0.000"),
            (0, "0.000"),
            (0.0, "0.000"),
            (123.456789, "123.457"),
            (-123.456, "-123.456"),
            (float('inf'), "inf"),
            (float('-inf'), "-inf"),
        ]
        
        for value, expected in test_cases:
            try:
                result = safe_format(value)
                print(f"safe_format({value}) = {result}")
            except Exception as e:
                print(f"خطأ في تنسيق {value}: {e}")
        
        # اختبار تنسيقات مختلفة
        format_tests = [
            (123.456, "{:.2f}", "123.46"),
            (123.456, "{:.0f}", "123"),
            (123.456, "{:.5f}", "123.45600"),
        ]
        
        for value, fmt, expected in format_tests:
            result = safe_format(value, fmt)
            print(f"safe_format({value}, '{fmt}') = {result}")
            if result != expected:
                print(f"⚠️ متوقع: {expected}, حصلت على: {result}")
        
        print("✅ جميع اختبارات الحالات الخاصة نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحالات الخاصة: {str(e)}")
        return False

def test_string_edge_cases():
    """اختبار حالات خاصة للنصوص"""
    try:
        # اختبار قيم مختلفة
        test_cases = [
            (None, "غير محدد"),
            ("", "غير محدد"),
            ("   ", "   "),  # مسافات فقط
            ("نص عادي", "نص عادي"),
            (123, "123"),
            (0, "0"),
            (False, "False"),
            (True, "True"),
        ]
        
        for value, expected in test_cases:
            result = safe_str(value)
            print(f"safe_str({repr(value)}) = {repr(result)}")
            if result != expected:
                print(f"⚠️ متوقع: {repr(expected)}, حصلت على: {repr(result)}")
        
        # اختبار مع نص افتراضي مخصص
        custom_tests = [
            (None, "مخصص", "مخصص"),
            ("", "مخصص", "مخصص"),
            ("نص", "مخصص", "نص"),
        ]
        
        for value, default, expected in custom_tests:
            result = safe_str(value, default)
            print(f"safe_str({repr(value)}, '{default}') = {repr(result)}")
            if result != expected:
                print(f"⚠️ متوقع: {repr(expected)}, حصلت على: {repr(result)}")
        
        print("✅ جميع اختبارات النصوص نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النصوص: {str(e)}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاحات شاشة التقارير")
    print("=" * 50)
    
    tests = [
        ("اختبار الدوال المساعدة", test_safe_format_functions),
        ("اختبار دالة الترجمة", test_translations),
        ("اختبار الحالات الخاصة للتنسيق", test_format_edge_cases),
        ("اختبار الحالات الخاصة للنصوص", test_string_edge_cases),
        ("اختبار فتح شاشة التقارير", test_reports_screen)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                failed += 1
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: خطأ - {str(e)}")
        
        print("-" * 30)
    
    print(f"\n📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! تم إصلاح المشاكل.")
    else:
        print(f"\n⚠️ {failed} اختبار فشل. يرجى مراجعة الأخطاء.")

if __name__ == "__main__":
    main()
