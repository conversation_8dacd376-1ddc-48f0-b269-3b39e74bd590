#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار تقارير السندات مع أسماء العملاء والموردين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import *
from reports_screen import ReportsScreen
from datetime import datetime
import tkinter as tk

def test_voucher_reports():
    """اختبار تقارير السندات"""
    print("بدء اختبار تقارير السندات...")
    
    # إنشاء جلسة قاعدة البيانات
    session = get_db_session()
    
    try:
        # إنشاء root window وهمي
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء كائن ReportsScreen
        reports = ReportsScreen(root, session)
        
        # اختبار توليد تقرير السندات
        print("توليد تقرير السندات...")
        
        # تحديد تواريخ الاختبار
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
        
        # توليد التقرير
        try:
            result = reports.generate_vouchers_report(
                start_date=start_date,
                end_date=end_date,
                voucher_type=None,  # جميع الأنواع
                name_filter=""  # بدون فلتر
            )
            
            if result:
                print(f"تم توليد التقرير بنجاح! عدد السندات: {len(result)}")
                
                # عرض أول 5 سندات كعينة
                print("\nعينة من السندات:")
                for i, voucher_data in enumerate(result[:5]):
                    print(f"السند {i+1}:")
                    print(f"  - الرقم: {voucher_data.get('number', 'غير محدد')}")
                    print(f"  - النوع: {voucher_data.get('type', 'غير محدد')}")
                    print(f"  - المرجع: {voucher_data.get('reference', 'غير محدد')}")
                    print(f"  - اسم العميل/المورد: {voucher_data.get('entity_name', 'غير محدد')}")
                    print(f"  - المبلغ: {voucher_data.get('amount_usd', 0)} دولار")
                    print()
            else:
                print("لم يتم العثور على سندات في التواريخ المحددة")
                
        except Exception as e:
            print(f"خطأ في توليد تقرير السندات: {e}")
            import traceback
            traceback.print_exc()
        
        # اختبار أنواع السندات المختلفة
        voucher_types = ['إيرادات', 'مدفوعات', 'قبض', 'صرف']
        for voucher_type in voucher_types:
            try:
                print(f"\naختبار تقرير سندات {voucher_type}...")
                result = reports.generate_vouchers_report(
                    start_date=start_date,
                    end_date=end_date,
                    voucher_type=voucher_type,
                    name_filter=""
                )
                
                if result:
                    print(f"  - عدد سندات {voucher_type}: {len(result)}")
                    # عرض أول سند مع اسم العميل/المورد
                    if result:
                        first_voucher = result[0]
                        entity_name = first_voucher.get('entity_name', 'غير محدد')
                        print(f"  - مثال: اسم العميل/المورد: {entity_name}")
                else:
                    print(f"  - لا توجد سندات من نوع {voucher_type}")
                    
            except Exception as e:
                print(f"خطأ في اختبار سندات {voucher_type}: {e}")
        
        # اختبار السندات مع فلتر الاسم
        print("\nاختبار فلتر الأسماء...")
        try:
            # البحث عن أسماء العملاء
            customers = session.query(Customer).limit(3).all()
            for customer in customers:
                result = reports.generate_vouchers_report(
                    start_date=start_date,
                    end_date=end_date,
                    voucher_type=None,
                    name_filter=customer.name
                )
                
                if result:
                    print(f"  - سندات العميل '{customer.name}': {len(result)}")
                else:
                    print(f"  - لا توجد سندات للعميل '{customer.name}'")
                    
        except Exception as e:
            print(f"خطأ في اختبار فلتر الأسماء: {e}")
            
    except Exception as e:
        print(f"خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'session' in locals():
            session.close()
        if 'root' in locals():
            root.destroy()

def check_voucher_data():
    """فحص بيانات السندات الموجودة"""
    print("فحص بيانات السندات في قاعدة البيانات...")
    
    session = get_db_session()
    
    try:
        # عدد السندات الكلي
        total_vouchers = session.query(Voucher).count()
        print(f"إجمالي السندات: {total_vouchers}")
        
        # السندات حسب النوع
        voucher_types = session.query(Voucher.voucher_type).distinct().all()
        print("أنواع السندات الموجودة:")
        for vtype in voucher_types:
            count = session.query(Voucher).filter_by(voucher_type=vtype[0]).count()
            print(f"  - {vtype[0]}: {count} سند")
        
        # السندات المرتبطة بمبيعات
        vouchers_with_sales = session.query(Voucher).filter(Voucher.sale_id.isnot(None)).count()
        print(f"السندات المرتبطة بمبيعات: {vouchers_with_sales}")
        
        # السندات المرتبطة بمشتريات
        vouchers_with_purchases = session.query(Voucher).filter(Voucher.purchase_id.isnot(None)).count()
        print(f"السندات المرتبطة بمشتريات: {vouchers_with_purchases}")
        
        # السندات المرتبطة مباشرة بعملاء
        vouchers_with_customers = session.query(Voucher).filter(Voucher.customer_id.isnot(None)).count()
        print(f"السندات المرتبطة مباشرة بعملاء: {vouchers_with_customers}")
        
        # السندات المرتبطة مباشرة بموردين
        vouchers_with_suppliers = session.query(Voucher).filter(Voucher.supplier_id.isnot(None)).count()
        print(f"السندات المرتبطة مباشرة بموردين: {vouchers_with_suppliers}")
        
        # عينة من السندات غير المرتبطة
        unlinked_vouchers = session.query(Voucher).filter(
            Voucher.sale_id.is_(None),
            Voucher.purchase_id.is_(None),
            Voucher.customer_id.is_(None),
            Voucher.supplier_id.is_(None)
        ).limit(5).all()
        
        print(f"\nسندات غير مرتبطة (عينة): {len(unlinked_vouchers)}")
        for voucher in unlinked_vouchers:
            print(f"  - السند {voucher.voucher_number}: {voucher.voucher_type} - {voucher.description}")
        
    except Exception as e:
        print(f"خطأ في فحص البيانات: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        session.close()

if __name__ == "__main__":
    print("=" * 50)
    print("اختبار شامل لتقارير السندات")
    print("=" * 50)
    
    # فحص البيانات أولاً
    check_voucher_data()
    
    print("\n" + "=" * 50)
    
    # اختبار التقارير
    test_voucher_reports()
    
    print("\nانتهى الاختبار.")
