"""
اختبار إصلاح مشاكل شاشة السندات
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from vouchers_screen import VouchersScreen
from database import User
from db_session import session_scope

def test_vouchers_screen():
    """اختبار فتح شاشة السندات"""
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إنشاء مستخدم وهمي للاختبار
        with session_scope() as session:
            user = session.query(User).first()
            if not user:
                print("لا يوجد مستخدمين في قاعدة البيانات")
                return False
        
        # إنشاء شاشة السندات
        vouchers_screen = VouchersScreen(user)
        vouchers_screen.show()
        
        print("✅ تم فتح شاشة السندات بنجاح")
        
        # إغلاق التطبيق
        vouchers_screen.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فتح شاشة السندات: {str(e)}")
        return False

def test_safe_format_functions():
    """اختبار الدوال المساعدة للتنسيق الآمن"""
    from vouchers_screen import safe_format, safe_str
    
    try:
        # اختبار safe_format
        assert safe_format(123.456) == "123.456"
        assert safe_format(None) == "0.000"
        assert safe_format(0) == "0.000"
        assert safe_format(123.456, "{:.2f}") == "123.46"
        
        # اختبار safe_str
        assert safe_str("test") == "test"
        assert safe_str(None) == "غير محدد"
        assert safe_str("") == "غير محدد"
        assert safe_str(123) == "123"
        
        print("✅ جميع اختبارات الدوال المساعدة نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال المساعدة: {str(e)}")
        return False

def test_translations():
    """اختبار دالة الترجمة المحدثة"""
    try:
        from translations import get_translation
        
        # اختبار الترجمة مع معامل واحد
        result1 = get_translation("vouchers")
        print(f"ترجمة 'vouchers': {result1}")
        
        # اختبار الترجمة مع معاملين
        result2 = get_translation("unknown_key", "نص افتراضي")
        print(f"ترجمة مفتاح غير موجود: {result2}")
        
        # اختبار الترجمة مع مفتاح غير موجود بدون نص افتراضي
        result3 = get_translation("another_unknown_key")
        print(f"ترجمة مفتاح غير موجود بدون افتراضي: {result3}")
        
        print("✅ جميع اختبارات الترجمة نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الترجمة: {str(e)}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاحات شاشة السندات")
    print("=" * 50)
    
    tests = [
        ("اختبار الدوال المساعدة", test_safe_format_functions),
        ("اختبار دالة الترجمة", test_translations),
        ("اختبار فتح شاشة السندات", test_vouchers_screen)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                failed += 1
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: خطأ - {str(e)}")
        
        print("-" * 30)
    
    print(f"\n📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! تم إصلاح المشاكل.")
    else:
        print(f"\n⚠️ {failed} اختبار فشل. يرجى مراجعة الأخطاء.")

if __name__ == "__main__":
    main()
