"""
ملف تحديث قاعدة البيانات لإضافة ميزة سندات الأصناف
يقوم هذا الملف بتحديث قاعدة البيانات الموجودة لتشمل الجداول والحقول الجديدة
"""

import sqlite3
from datetime import datetime
from logger import log_info, log_error

def update_database_for_items():
    """
    تحديث قاعدة البيانات لإضافة دعم سندات الأصناف
    """
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        log_info("بدء تحديث قاعدة البيانات لدعم سندات الأصناف")
        
        # 1. التحقق من وجود جدول receipt_items وإنشاؤه إذا لم يكن موجوداً
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='receipt_items'")
        if not cursor.fetchone():
            log_info("إنشاء جدول receipt_items")
            cursor.execute("""
                CREATE TABLE receipt_items (
                    id INTEGER PRIMARY KEY,
                    receipt_id INTEGER NOT NULL REFERENCES receipts(id),
                    diamond_type TEXT NOT NULL,
                    category_id INTEGER REFERENCES categories(id),
                    unit_id INTEGER REFERENCES units(id),
                    quantity REAL NOT NULL,
                    price_per_unit_usd REAL,
                    total_value_usd REAL,
                    total_value_sar REAL,
                    exchange_rate REAL,
                    notes TEXT
                )
            """)
            log_info("تم إنشاء جدول receipt_items بنجاح")
        else:
            log_info("جدول receipt_items موجود بالفعل")
        
        # 2. التحقق من وجود جدول inventory_movements وإنشاؤه إذا لم يكن موجوداً
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_movements'")
        if not cursor.fetchone():
            log_info("إنشاء جدول inventory_movements")
            cursor.execute("""
                CREATE TABLE inventory_movements (
                    id INTEGER PRIMARY KEY,
                    diamond_type TEXT NOT NULL,
                    category_id INTEGER REFERENCES categories(id),
                    unit_id INTEGER REFERENCES units(id),
                    movement_type TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price_per_unit_usd REAL,
                    total_value_usd REAL,
                    total_value_sar REAL,
                    exchange_rate REAL,
                    movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    reference_type TEXT,
                    reference_id INTEGER,
                    customer_id INTEGER REFERENCES customers(id),
                    supplier_id INTEGER REFERENCES suppliers(id),
                    notes TEXT,
                    created_by INTEGER REFERENCES users(id)
                )
            """)
            
            # إنشاء فهارس لتحسين الأداء
            cursor.execute("CREATE INDEX idx_inventory_movements_date ON inventory_movements (movement_date)")
            cursor.execute("CREATE INDEX idx_inventory_movements_type ON inventory_movements (movement_type)")
            cursor.execute("CREATE INDEX idx_inventory_movements_diamond_type ON inventory_movements (diamond_type)")
            
            log_info("تم إنشاء جدول inventory_movements والفهارس بنجاح")
        else:
            log_info("جدول inventory_movements موجود بالفعل")
        
        # 3. التحقق من وجود عمود notes في جدول receipts وإضافته إذا لم يكن موجوداً
        cursor.execute("PRAGMA table_info(receipts)")
        receipts_columns = [column[1] for column in cursor.fetchall()]
        
        if 'notes' not in receipts_columns:
            log_info("إضافة عمود notes إلى جدول receipts")
            cursor.execute("ALTER TABLE receipts ADD COLUMN notes TEXT")
            log_info("تم إضافة عمود notes إلى جدول receipts")
        else:
            log_info("عمود notes موجود بالفعل في جدول receipts")
        
        # 4. التحقق من وجود الأعمدة الجديدة في جدول opening_balances
        cursor.execute("PRAGMA table_info(opening_balances)")
        opening_columns = [column[1] for column in cursor.fetchall()]
        
        if 'category_id' not in opening_columns:
            log_info("إضافة عمود category_id إلى جدول opening_balances")
            cursor.execute("ALTER TABLE opening_balances ADD COLUMN category_id INTEGER REFERENCES categories(id)")
        
        if 'unit_id' not in opening_columns:
            log_info("إضافة عمود unit_id إلى جدول opening_balances")
            cursor.execute("ALTER TABLE opening_balances ADD COLUMN unit_id INTEGER REFERENCES units(id)")
        
        # 5. إضافة بيانات تجريبية للفئات والوحدات إذا لم تكن موجودة
        cursor.execute("SELECT COUNT(*) FROM categories")
        categories_count = cursor.fetchone()[0]
        
        if categories_count == 0:
            log_info("إضافة فئات افتراضية")
            default_categories = [
                ("ألماس طبيعي", "ألماس طبيعي مستخرج من المناجم"),
                ("ألماس صناعي", "ألماس مصنع في المختبر"),
                ("أحجار كريمة", "أحجار كريمة أخرى"),
                ("مجوهرات", "مجوهرات ومصنوعات ذهبية")
            ]
            
            for name, description in default_categories:
                cursor.execute("INSERT INTO categories (name, description) VALUES (?, ?)", (name, description))
            
            log_info(f"تم إضافة {len(default_categories)} فئة افتراضية")
        
        cursor.execute("SELECT COUNT(*) FROM units")
        units_count = cursor.fetchone()[0]
        
        if units_count == 0:
            log_info("إضافة وحدات افتراضية")
            default_units = [
                ("قيراط", "ct", "وحدة قياس الألماس والأحجار الكريمة", 1),
                ("جرام", "g", "وحدة قياس الوزن", 1),
                ("قطعة", "pcs", "عدد القطع", 1),
                ("مجموعة", "set", "مجموعة من القطع", 1)
            ]
            
            for name, symbol, description, category_id in default_units:
                cursor.execute("INSERT INTO units (name, symbol, description, category_id) VALUES (?, ?, ?, ?)", 
                             (name, symbol, description, category_id))
            
            log_info(f"تم إضافة {len(default_units)} وحدة افتراضية")
        
        # 6. إضافة بيانات تجريبية للرصيد الافتتاحي إذا لم تكن موجودة
        cursor.execute("SELECT COUNT(*) FROM opening_balances")
        opening_count = cursor.fetchone()[0]
        
        if opening_count == 0:
            log_info("إضافة أرصدة افتتاحية تجريبية")
            sample_opening_balances = [
                ("ألماس أبيض 1 قيراط", 100.0, 1000.0, 100000.0, 375000.0, 3.75, 1, 1),
                ("ألماس أصفر 0.5 قيراط", 50.0, 800.0, 40000.0, 150000.0, 3.75, 1, 1),
                ("زمرد طبيعي", 25.0, 500.0, 12500.0, 46875.0, 3.75, 3, 1),
                ("ياقوت أحمر", 15.0, 1200.0, 18000.0, 67500.0, 3.75, 3, 1)
            ]
            
            for diamond_type, quantity, price, total_usd, total_sar, rate, cat_id, unit_id in sample_opening_balances:
                cursor.execute("""
                    INSERT INTO opening_balances 
                    (diamond_type, quantity, price_per_carat_usd, total_value_usd, total_value_sar, exchange_rate, category_id, unit_id, date_created)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (diamond_type, quantity, price, total_usd, total_sar, rate, cat_id, unit_id, datetime.now()))
            
            log_info(f"تم إضافة {len(sample_opening_balances)} رصيد افتتاحي تجريبي")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        log_info("تم تحديث قاعدة البيانات بنجاح لدعم سندات الأصناف")
        return True
        
    except Exception as e:
        log_error(f"خطأ في تحديث قاعدة البيانات: {str(e)}", e)
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def verify_database_update():
    """
    التحقق من نجاح تحديث قاعدة البيانات
    """
    try:
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول الجديدة
        tables_to_check = ['receipt_items', 'inventory_movements']
        
        for table in tables_to_check:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if not cursor.fetchone():
                log_error(f"الجدول {table} غير موجود")
                return False
        
        # التحقق من وجود الأعمدة الجديدة
        cursor.execute("PRAGMA table_info(receipts)")
        receipts_columns = [column[1] for column in cursor.fetchall()]
        
        if 'notes' not in receipts_columns:
            log_error("عمود notes غير موجود في جدول receipts")
            return False
        
        conn.close()
        log_info("تم التحقق من تحديث قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        log_error(f"خطأ في التحقق من تحديث قاعدة البيانات: {str(e)}", e)
        return False

if __name__ == "__main__":
    print("بدء تحديث قاعدة البيانات...")
    
    if update_database_for_items():
        print("تم تحديث قاعدة البيانات بنجاح")
        
        if verify_database_update():
            print("تم التحقق من التحديث بنجاح")
        else:
            print("فشل في التحقق من التحديث")
    else:
        print("فشل في تحديث قاعدة البيانات")
