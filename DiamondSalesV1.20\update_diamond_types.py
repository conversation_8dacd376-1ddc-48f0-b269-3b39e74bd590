import sqlite3

def update_diamond_types():
    try:
        # اتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        # تحديث نوع الألماس في جدول المبيعات
        cursor.execute("UPDATE sales SET diamond_type = 'الماس مدور' WHERE diamond_type = 'Round'")
        cursor.execute("UPDATE sales SET diamond_type = 'باجيت' WHERE diamond_type = 'Baguette'")
        
        # تحديث نوع الألماس في جدول المشتريات
        cursor.execute("UPDATE purchases SET diamond_type = 'الماس مدور' WHERE diamond_type = 'Round'")
        cursor.execute("UPDATE purchases SET diamond_type = 'باجيت' WHERE diamond_type = 'Baguette'")
        
        # حفظ التغييرات
        conn.commit()
        
        # إغلاق الاتصال
        conn.close()
        
        print("تم تحديث أنواع الألماس بنجاح!")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء تحديث أنواع الألماس: {str(e)}")
        return False

if __name__ == "__main__":
    update_diamond_types()
