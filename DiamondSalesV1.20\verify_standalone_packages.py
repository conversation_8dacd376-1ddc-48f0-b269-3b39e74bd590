#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diamond Sales V1.30 - Standalone Packages Verification
التحقق من الحزم المستقلة لبرنامج مبيعات الألماس
"""

import os
import zipfile
import subprocess
from pathlib import Path

def print_status(message, status="INFO"):
    """طباعة رسالة حالة بتنسيق منظم"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def verify_zip_integrity(zip_path):
    """التحقق من سلامة الملف المضغوط"""
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # اختبار سلامة الملف المضغوط
            bad_file = zip_ref.testzip()
            if bad_file:
                print_status(f"ملف تالف في الأرشيف: {bad_file}", "ERROR")
                return False
            
            # عد الملفات
            file_count = len(zip_ref.namelist())
            size = os.path.getsize(zip_path)
            print_status(f"سليم - {file_count} ملف ({size/1024/1024:.1f} MB)", "SUCCESS")
            return True
    except Exception as e:
        print_status(f"خطأ في فحص الأرشيف: {str(e)}", "ERROR")
        return False

def verify_standalone_packages():
    """التحقق من الحزم المستقلة"""
    print("\n" + "="*60)
    print("🔍 التحقق من الحزم المستقلة")
    print("="*60)
    
    base_path = Path("standalone_packages")
    
    if not base_path.exists():
        print_status("مجلد الحزم المستقلة غير موجود", "ERROR")
        return False
    
    # قائمة الحزم المتوقعة
    expected_packages = [
        {
            "name": "DiamondSales_v1.30_Standalone",
            "description": "البرنامج الرئيسي",
            "executable": "DiamondSales.exe",
            "required_files": ["DiamondSales.exe", "diamond_sales.db", "README.txt"],
            "required_dirs": ["assets", "translations", "_internal"]
        },
        {
            "name": "ActivationGenerator_v1.30_Standalone", 
            "description": "مولد أكواد التفعيل",
            "executable": "ActivationGenerator.exe",
            "required_files": ["ActivationGenerator.exe", "README.txt"],
            "required_dirs": ["_internal"]
        },
        {
            "name": "ResetPassword_v1.30_Standalone",
            "description": "أداة إعادة تعيين كلمة المرور",
            "executable": "reset_password.exe", 
            "required_files": ["reset_password.exe", "README.txt"],
            "required_dirs": []
        }
    ]
    
    all_passed = True
    
    for package in expected_packages:
        print(f"\n📦 فحص {package['description']}:")
        
        # فحص المجلد
        package_dir = base_path / package["name"]
        zip_file = base_path / f"{package['name']}.zip"
        
        # فحص وجود المجلد
        if package_dir.exists():
            print_status(f"مجلد الحزمة: موجود", "SUCCESS")
            
            # فحص الملفات المطلوبة
            for required_file in package["required_files"]:
                file_path = package_dir / required_file
                if file_path.exists():
                    size = file_path.stat().st_size
                    print_status(f"  {required_file}: موجود ({size:,} بايت)", "SUCCESS")
                else:
                    print_status(f"  {required_file}: مفقود", "ERROR")
                    all_passed = False
            
            # فحص المجلدات المطلوبة
            for required_dir in package["required_dirs"]:
                dir_path = package_dir / required_dir
                if dir_path.exists() and dir_path.is_dir():
                    file_count = len(list(dir_path.rglob("*")))
                    print_status(f"  {required_dir}/: موجود ({file_count} عنصر)", "SUCCESS")
                else:
                    print_status(f"  {required_dir}/: مفقود", "ERROR")
                    all_passed = False
        else:
            print_status(f"مجلد الحزمة: مفقود", "ERROR")
            all_passed = False
          # فحص الملف المضغوط
        if zip_file.exists():
            print(f"ℹ️ الملف المضغوط: ", end="")
            if not verify_zip_integrity(zip_file):
                all_passed = False
        else:
            print_status(f"الملف المضغوط: مفقود", "ERROR")
            all_passed = False
    
    return all_passed

def verify_main_readme():
    """التحقق من ملف التعليمات الرئيسي"""
    print("\n" + "="*60)
    print("📚 التحقق من ملف التعليمات الرئيسي")
    print("="*60)
    
    readme_path = Path("standalone_packages/README.txt")
    
    if readme_path.exists():
        size = readme_path.stat().st_size
        print_status(f"README.txt: موجود ({size:,} بايت)", "SUCCESS")
        
        # فحص محتوى الملف
        try:
            with open(readme_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "الحزم المستقلة" in content and "مبيعات الألماس" in content:
                    print_status("المحتوى: صحيح ومكتمل", "SUCCESS")
                    return True
                else:
                    print_status("المحتوى: غير مكتمل", "WARNING")
                    return False
        except Exception as e:
            print_status(f"خطأ في قراءة الملف: {str(e)}", "ERROR")
            return False
    else:
        print_status("README.txt: مفقود", "ERROR")
        return False

def generate_delivery_report():
    """إنشاء تقرير التسليم النهائي"""
    print("\n" + "="*60)
    print("📊 تقرير التسليم النهائي")
    print("="*60)
    
    # فحص جميع المكونات
    packages_ok = verify_standalone_packages()
    readme_ok = verify_main_readme()
    
    # حساب أحجام الملفات
    base_path = Path("standalone_packages")
    total_size = 0
    package_info = []
    
    for zip_file in base_path.glob("*.zip"):
        size = zip_file.stat().st_size
        total_size += size
        package_info.append({
            "name": zip_file.name,
            "size_mb": size / 1024 / 1024
        })
    
    # عرض النتائج
    print(f"\n📈 النتائج:")
    print(f"   - حالة الحزم: {'✅ صحيحة' if packages_ok else '❌ بها مشاكل'}")
    print(f"   - ملف التعليمات: {'✅ موجود' if readme_ok else '❌ مفقود'}")
    print(f"   - الحجم الإجمالي: {total_size/1024/1024:.1f} MB")
    
    print(f"\n📦 تفاصيل الحزم:")
    for package in package_info:
        print(f"   ✅ {package['name']} ({package['size_mb']:.1f} MB)")
    
    # تقييم عام
    overall_success = packages_ok and readme_ok
    
    if overall_success:
        print_status("جميع الحزم المستقلة جاهزة للتوزيع! 🎉", "SUCCESS")
        print("\n🎯 ملفات التوزيع النهائية:")
        print("   📦 DiamondSales_v1.30_Standalone.zip - البرنامج الرئيسي")
        print("   🔑 ActivationGenerator_v1.30_Standalone.zip - مولد أكواد التفعيل")
        print("   🔓 ResetPassword_v1.30_Standalone.zip - أداة إعادة تعيين كلمة المرور")
        print("   📚 README.txt - دليل الاستخدام الشامل")
        
        print("\n💡 مميزات الحزم المستقلة:")
        print("   ✅ كل حزمة تعمل بشكل مستقل")
        print("   ✅ لا تحتاج لتثبيت")
        print("   ✅ سهولة في التوزيع والنقل")
        print("   ✅ تعليمات مفصلة لكل برنامج")
        print("   ✅ أحجام ملفات معقولة")
    else:
        print_status("يوجد مشاكل في بعض الحزم", "WARNING")
    
    return overall_success

def main():
    """الدالة الرئيسية"""
    print("🚀 التحقق من الحزم المستقلة لبرنامج مبيعات الألماس")
    print("="*70)
    
    # التأكد من وجودنا في المجلد الصحيح
    if not os.path.exists("standalone_packages"):
        print_status("لم يتم العثور على مجلد standalone_packages", "ERROR")
        print_status("يرجى تشغيل create_standalone_packages.py أولاً", "WARNING")
        return False
    
    # بدء عملية التحقق
    success = generate_delivery_report()
    
    if success:
        print("\n📋 تعليمات التوزيع:")
        print("   1. انسخ الملفات الثلاثة المضغوطة للعميل")
        print("   2. أرفق ملف README.txt مع كل توزيع")
        print("   3. تأكد من أن العميل يقرأ التعليمات قبل الاستخدام")
        print("   4. كل حزمة تحتوي على ملف README.txt خاص بها")
    
    return success

if __name__ == "__main__":
    main()
